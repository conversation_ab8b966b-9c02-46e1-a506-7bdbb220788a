{"name": "woocommerce-shipping-usps", "version": "5.2.4", "lockfileVersion": 3, "requires": true, "_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "aliases": [], "content-hash": "7ce6af46f01d4a15f460d57f164ffd5a", "minimum-stability": "stable", "packages-dev": [{"name": "dealerdirect/phpcodesniffer-composer-installer", "version": "v1.0.0", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.frenck.nl", "role": "Developer / IT Manager"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/composer-installer/graphs/contributors"}], "description": "PHP_CodeSniffer Standards Composer Installer Plugin", "homepage": "http://www.dealerdirect.com", "keywords": ["PHPCodeSniffer", "PHP_CodeSniffer", "code quality", "codesniffer", "composer", "installer", "phpcbf", "phpcs", "plugin", "qa", "quality", "standard", "standards", "style guide", "stylecheck", "tests"], "license": ["MIT"], "notification-url": "https://packagist.org/downloads/", "time": "2023-01-05T11:28:13+00:00", "type": "composer-plugin", "autoload": {"psr-4": {"PHPCSStandards\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\": "src/"}}, "dist": {"reference": "4be43904336affa5c2f70744a348312336afd0da", "shasum": "", "type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/composer-installer/zipball/4be43904336affa5c2f70744a348312336afd0da"}, "extra": {"class": "PHPCSStandards\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin"}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.4", "squizlabs/php_codesniffer": "^2.0 || ^3.1.0 || ^4.0"}, "require-dev": {"composer/composer": "*", "ext-json": "*", "ext-zip": "*", "php-parallel-lint/php-parallel-lint": "^1.3.1", "phpcompatibility/php-compatibility": "^9.0", "yoast/phpunit-polyfills": "^1.0"}, "source": {"reference": "4be43904336affa5c2f70744a348312336afd0da", "type": "git", "url": "https://github.com/PHPCSStandards/composer-installer.git"}, "support": {"issues": "https://github.com/PHPCSStandards/composer-installer/issues", "source": "https://github.com/PHPCSStandards/composer-installer"}}, {"name": "phpcompatibility/php-compatibility", "version": "9.3.5", "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://github.com/wimg", "role": "lead"}, {"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCompatibility/PHPCompatibility/graphs/contributors"}], "description": "A set of sniffs for PHP_CodeSniffer that checks for PHP cross-version compatibility.", "homepage": "http://techblog.wimgodden.be/tag/codesniffer/", "keywords": ["compatibility", "phpcs", "standards"], "license": ["LGPL-3.0-or-later"], "notification-url": "https://packagist.org/downloads/", "time": "2019-12-27T09:44:58+00:00", "type": "phpcodesniffer-standard", "conflict": {"squizlabs/php_codesniffer": "2.6.2"}, "dist": {"reference": "9fb324479acf6f39452e0655d2429cc0d3914243", "shasum": "", "type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibility/zipball/9fb324479acf6f39452e0655d2429cc0d3914243"}, "require": {"php": ">=5.3", "squizlabs/php_codesniffer": "^2.3 || ^3.0.2"}, "require-dev": {"phpunit/phpunit": "~4.5 || ^5.0 || ^6.0 || ^7.0"}, "source": {"reference": "9fb324479acf6f39452e0655d2429cc0d3914243", "type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibility.git"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5 || This Composer plugin will sort out the PHPCS 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibility/issues", "source": "https://github.com/PHPCompatibility/PHPCompatibility"}}, {"name": "phpcompatibility/phpcompatibility-paragonie", "version": "1.3.3", "authors": [{"name": "<PERSON><PERSON>", "role": "lead"}, {"name": "<PERSON>", "role": "lead"}], "description": "A set of rulesets for PHP_CodeSniffer to check for PHP cross-version compatibility issues in projects, while accounting for polyfills provided by the Paragonie polyfill libraries.", "funding": [{"type": "github", "url": "https://github.com/PHPCompatibility"}, {"type": "github", "url": "https://github.com/jrfnl"}, {"type": "open_collective", "url": "https://opencollective.com/php_codesniffer"}], "homepage": "http://phpcompatibility.com/", "keywords": ["compatibility", "paragonie", "phpcs", "polyfill", "standards", "static analysis"], "license": ["LGPL-3.0-or-later"], "notification-url": "https://packagist.org/downloads/", "time": "2024-04-24T21:30:46+00:00", "type": "phpcodesniffer-standard", "dist": {"reference": "293975b465e0e709b571cbf0c957c6c0a7b9a2ac", "shasum": "", "type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibilityParagonie/zipball/293975b465e0e709b571cbf0c957c6c0a7b9a2ac"}, "require": {"phpcompatibility/php-compatibility": "^9.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0", "paragonie/random_compat": "dev-master", "paragonie/sodium_compat": "dev-master"}, "source": {"reference": "293975b465e0e709b571cbf0c957c6c0a7b9a2ac", "type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie.git"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0 || This Composer plugin will sort out the PHP_CodeSniffer 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie/issues", "security": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie/security/policy", "source": "https://github.com/PHPCompatibility/PHPCompatibilityParagonie"}}, {"name": "phpcompatibility/phpcompatibility-wp", "version": "2.1.5", "authors": [{"name": "<PERSON><PERSON>", "role": "lead"}, {"name": "<PERSON>", "role": "lead"}], "description": "A ruleset for PHP_CodeSniffer to check for PHP cross-version compatibility issues in projects, while accounting for polyfills provided by WordPress.", "funding": [{"type": "github", "url": "https://github.com/PHPCompatibility"}, {"type": "github", "url": "https://github.com/jrfnl"}, {"type": "open_collective", "url": "https://opencollective.com/php_codesniffer"}], "homepage": "http://phpcompatibility.com/", "keywords": ["compatibility", "phpcs", "standards", "static analysis", "wordpress"], "license": ["LGPL-3.0-or-later"], "notification-url": "https://packagist.org/downloads/", "time": "2024-04-24T21:37:59+00:00", "type": "phpcodesniffer-standard", "dist": {"reference": "01c1ff2704a58e46f0cb1ca9d06aee07b3589082", "shasum": "", "type": "zip", "url": "https://api.github.com/repos/PHPCompatibility/PHPCompatibilityWP/zipball/01c1ff2704a58e46f0cb1ca9d06aee07b3589082"}, "require": {"phpcompatibility/php-compatibility": "^9.0", "phpcompatibility/phpcompatibility-paragonie": "^1.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0"}, "source": {"reference": "01c1ff2704a58e46f0cb1ca9d06aee07b3589082", "type": "git", "url": "https://github.com/PHPCompatibility/PHPCompatibilityWP.git"}, "suggest": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0 || This Composer plugin will sort out the PHP_CodeSniffer 'installed_paths' automatically.", "roave/security-advisories": "dev-master || Helps prevent installing dependencies with known security issues."}, "support": {"issues": "https://github.com/PHPCompatibility/PHPCompatibilityWP/issues", "security": "https://github.com/PHPCompatibility/PHPCompatibilityWP/security/policy", "source": "https://github.com/PHPCompatibility/PHPCompatibilityWP"}}, {"name": "phpcsstandards/phpcsextra", "version": "1.2.1", "authors": [{"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHPCSExtra/graphs/contributors"}], "description": "A collection of sniffs and standards for use with PHP_CodeSniffer.", "funding": [{"type": "github", "url": "https://github.com/PHPCSStandards"}, {"type": "github", "url": "https://github.com/jrfnl"}, {"type": "open_collective", "url": "https://opencollective.com/php_codesniffer"}], "keywords": ["PHP_CodeSniffer", "phpcbf", "phpcodesniffer-standard", "phpcs", "standards", "static analysis"], "license": ["LGPL-3.0-or-later"], "notification-url": "https://packagist.org/downloads/", "time": "2023-12-08T16:49:07+00:00", "type": "phpcodesniffer-standard", "dist": {"reference": "11d387c6642b6e4acaf0bd9bf5203b8cca1ec489", "shasum": "", "type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHPCSExtra/zipball/11d387c6642b6e4acaf0bd9bf5203b8cca1ec489"}, "extra": {"branch-alias": {"dev-develop": "1.x-dev", "dev-stable": "1.x-dev"}}, "require": {"php": ">=5.4", "phpcsstandards/phpcsutils": "^1.0.9", "squizlabs/php_codesniffer": "^3.8.0"}, "require-dev": {"php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcsstandards/phpcsdevcs": "^1.1.6", "phpcsstandards/phpcsdevtools": "^1.2.1", "phpunit/phpunit": "^4.5 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "source": {"reference": "11d387c6642b6e4acaf0bd9bf5203b8cca1ec489", "type": "git", "url": "https://github.com/PHPCSStandards/PHPCSExtra.git"}, "support": {"issues": "https://github.com/PHPCSStandards/PHPCSExtra/issues", "security": "https://github.com/PHPCSStandards/PHPCSExtra/security/policy", "source": "https://github.com/PHPCSStandards/PHPCSExtra"}}, {"name": "phpcsstandards/phpcsutils", "version": "1.0.12", "authors": [{"name": "<PERSON>", "homepage": "https://github.com/jrfnl", "role": "lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHPCSUtils/graphs/contributors"}], "description": "A suite of utility functions for use with PHP_CodeSniffer", "funding": [{"type": "github", "url": "https://github.com/PHPCSStandards"}, {"type": "github", "url": "https://github.com/jrfnl"}, {"type": "open_collective", "url": "https://opencollective.com/php_codesniffer"}], "homepage": "https://phpcsutils.com/", "keywords": ["PHP_CodeSniffer", "phpcbf", "phpcodesniffer-standard", "phpcs", "phpcs3", "standards", "static analysis", "tokens", "utility"], "license": ["LGPL-3.0-or-later"], "notification-url": "https://packagist.org/downloads/", "time": "2024-05-20T13:34:27+00:00", "type": "phpcodesniffer-standard", "autoload": {"classmap": ["PHPCSUtils/"]}, "dist": {"reference": "87b233b00daf83fb70f40c9a28692be017ea7c6c", "shasum": "", "type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHPCSUtils/zipball/87b233b00daf83fb70f40c9a28692be017ea7c6c"}, "extra": {"branch-alias": {"dev-develop": "1.x-dev", "dev-stable": "1.x-dev"}}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^0.4.1 || ^0.5 || ^0.6.2 || ^0.7 || ^1.0", "php": ">=5.4", "squizlabs/php_codesniffer": "^3.10.0 || 4.0.x-dev@dev"}, "require-dev": {"ext-filter": "*", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcsstandards/phpcsdevcs": "^1.1.6", "yoast/phpunit-polyfills": "^1.1.0 || ^2.0.0"}, "source": {"reference": "87b233b00daf83fb70f40c9a28692be017ea7c6c", "type": "git", "url": "https://github.com/PHPCSStandards/PHPCSUtils.git"}, "support": {"docs": "https://phpcsutils.com/", "issues": "https://github.com/PHPCSStandards/PHPCSUtils/issues", "security": "https://github.com/PHPCSStandards/PHPCSUtils/security/policy", "source": "https://github.com/PHPCSStandards/PHPCSUtils"}}, {"name": "squizlabs/php_codesniffer", "version": "3.10.3", "authors": [{"name": "<PERSON>", "role": "Former lead"}, {"name": "<PERSON>", "role": "Current lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer/graphs/contributors"}], "bin": ["bin/phpcbf", "bin/phpcs"], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "funding": [{"type": "github", "url": "https://github.com/PHPCSStandards"}, {"type": "github", "url": "https://github.com/jrfnl"}, {"type": "open_collective", "url": "https://opencollective.com/php_codesniffer"}], "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "keywords": ["phpcs", "standards", "static analysis"], "license": ["BSD-3-<PERSON><PERSON>"], "notification-url": "https://packagist.org/downloads/", "time": "2024-09-18T10:38:58+00:00", "type": "library", "dist": {"reference": "62d32998e820bddc40f99f8251958aed187a5c9c", "shasum": "", "type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/62d32998e820bddc40f99f8251958aed187a5c9c"}, "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.3.4"}, "source": {"reference": "62d32998e820bddc40f99f8251958aed187a5c9c", "type": "git", "url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git"}, "support": {"issues": "https://github.com/PHPCSStandards/PHP_CodeSniffer/issues", "security": "https://github.com/PHPCSStandards/PHP_CodeSniffer/security/policy", "source": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "wiki": "https://github.com/PHPCSStandards/PHP_CodeSniffer/wiki"}}, {"name": "woocommerce/qit-cli", "version": "0.7.1", "bin": ["qit"], "description": "A command line interface for WooCommerce Quality Insights Toolkit (QIT).", "license": ["GPL-3.0-or-later"], "notification-url": "https://packagist.org/downloads/", "time": "2024-10-30T21:07:50+00:00", "type": "library", "dist": {"reference": "124a4fc100d0eb1486622ab3e2ffb24c31f5ba9b", "shasum": "", "type": "zip", "url": "https://api.github.com/repos/woocommerce/qit-cli/zipball/124a4fc100d0eb1486622ab3e2ffb24c31f5ba9b"}, "require": {"ext-curl": "*", "php": "^7.2.5 | ^8"}, "source": {"reference": "124a4fc100d0eb1486622ab3e2ffb24c31f5ba9b", "type": "git", "url": "https://github.com/woocommerce/qit-cli.git"}, "support": {"issues": "https://github.com/woocommerce/qit-cli/issues", "source": "https://github.com/woocommerce/qit-cli/tree/0.7.1"}}, {"name": "woocommerce/woocommerce-sniffs", "version": "1.0.0", "description": "WooCommerce sniffs", "keywords": ["phpcs", "standards", "static analysis", "woocommerce", "wordpress"], "license": ["MIT"], "notification-url": "https://packagist.org/downloads/", "time": "2023-09-29T13:52:33+00:00", "type": "phpcodesniffer-standard", "dist": {"reference": "3a65b917ff5ab5e65609e5dcb7bc62f9455bbef8", "shasum": "", "type": "zip", "url": "https://api.github.com/repos/woocommerce/woocommerce-sniffs/zipball/3a65b917ff5ab5e65609e5dcb7bc62f9455bbef8"}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0.0", "php": ">=7.0", "phpcompatibility/phpcompatibility-wp": "^2.1.0", "wp-coding-standards/wpcs": "^3.0.0"}, "source": {"reference": "3a65b917ff5ab5e65609e5dcb7bc62f9455bbef8", "type": "git", "url": "https://github.com/woocommerce/woocommerce-sniffs.git"}, "support": {"issues": "https://github.com/woocommerce/woocommerce-sniffs/issues", "source": "https://github.com/woocommerce/woocommerce-sniffs/tree/1.0.0"}}, {"name": "wp-coding-standards/wpcs", "version": "3.1.0", "authors": [{"name": "Contributors", "homepage": "https://github.com/WordPress/WordPress-Coding-Standards/graphs/contributors"}], "description": "PHP_CodeSniffer rules (sniffs) to enforce WordPress coding conventions", "funding": [{"type": "custom", "url": "https://opencollective.com/php_codesniffer"}], "keywords": ["phpcs", "standards", "static analysis", "wordpress"], "license": ["MIT"], "notification-url": "https://packagist.org/downloads/", "time": "2024-03-25T16:39:00+00:00", "type": "phpcodesniffer-standard", "dist": {"reference": "9333efcbff231f10dfd9c56bb7b65818b4733ca7", "shasum": "", "type": "zip", "url": "https://api.github.com/repos/WordPress/WordPress-Coding-Standards/zipball/9333efcbff231f10dfd9c56bb7b65818b4733ca7"}, "require": {"ext-filter": "*", "ext-libxml": "*", "ext-tokenizer": "*", "ext-xmlreader": "*", "php": ">=5.4", "phpcsstandards/phpcsextra": "^1.2.1", "phpcsstandards/phpcsutils": "^1.0.10", "squizlabs/php_codesniffer": "^3.9.0"}, "require-dev": {"php-parallel-lint/php-console-highlighter": "^1.0.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcompatibility/php-compatibility": "^9.0", "phpcsstandards/phpcsdevtools": "^1.2.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "source": {"reference": "9333efcbff231f10dfd9c56bb7b65818b4733ca7", "type": "git", "url": "https://github.com/WordPress/WordPress-Coding-Standards.git"}, "suggest": {"ext-iconv": "For improved results", "ext-mbstring": "For improved results"}, "support": {"issues": "https://github.com/WordPress/WordPress-Coding-Standards/issues", "source": "https://github.com/WordPress/WordPress-Coding-Standards", "wiki": "https://github.com/WordPress/WordPress-Coding-Standards/wiki"}}], "platform-dev": [], "plugin-api-version": "2.6.0", "prefer-lowest": false, "prefer-stable": false, "stability-flags": [], "packages": {"0": {"name": "automattic/jetpack-autoloader", "version": "v3.1.3", "extraneous": true, "license": ["GPL-2.0-or-later"]}, "1": {"name": "dvdoug/boxpacker", "version": "3.9.3", "extraneous": true, "funding": [{"type": "github", "url": "https://github.com/dvdoug"}], "license": ["MIT"]}, "2": {"name": "psr/log", "version": "1.1.4", "extraneous": true, "license": ["MIT"]}, "3": {"name": "woocommerce/box-packer", "version": "1.2.0", "extraneous": true, "license": ["MIT"]}, "": {"name": "woocommerce-shipping-usps", "version": "5.0.1", "devDependencies": {"node-wp-i18n": "~1.2.7"}, "engines": {"node": ">=8.9.3", "npm": ">=5.5.1"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==", "dev": true}, "node_modules/bluebird": {"version": "3.7.2", "resolved": "https://registry.npmjs.org/bluebird/-/bluebird-3.7.2.tgz", "integrity": "sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==", "dev": true}, "node_modules/brace-expansion": {"version": "1.1.11", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "dev": true, "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==", "dev": true}, "node_modules/encoding": {"version": "0.1.13", "resolved": "https://registry.npmjs.org/encoding/-/encoding-0.1.13.tgz", "integrity": "sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==", "dev": true, "dependencies": {"iconv-lite": "^0.6.2"}}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==", "dev": true}, "node_modules/gettext-parser": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/gettext-parser/-/gettext-parser-3.1.1.tgz", "integrity": "sha512-vNhWcqXEtZPs5Ft1ReA34g7ByWotpcOIeJvXVy2jF3/G2U9v6W0wG4Z4hXzcU8R//jArqkgHcVCGgGqa4vxVlQ==", "dev": true, "dependencies": {"encoding": "^0.1.12", "readable-stream": "^3.2.0", "safe-buffer": "^5.1.2"}}, "node_modules/glob": {"version": "7.2.3", "resolved": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "integrity": "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/iconv-lite": {"version": "0.6.3", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "dev": true, "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/inflight": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "integrity": "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==", "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.", "dev": true, "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==", "dev": true}, "node_modules/lodash": {"version": "4.17.21", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==", "dev": true}, "node_modules/minimatch": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dev": true, "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "integrity": "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==", "dev": true, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/mkdirp": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==", "dev": true, "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/node-wp-i18n": {"version": "1.2.7", "resolved": "https://registry.npmjs.org/node-wp-i18n/-/node-wp-i18n-1.2.7.tgz", "integrity": "sha512-4X+890+Irj8sY+6WKkFx+4wk/GGu9mGLDY1PVPF9AWF1zTKWClLA83QikcQKX55rjjKpN1jSZEQoEANNVSSBYw==", "dev": true, "dependencies": {"bluebird": "^3.4.1", "gettext-parser": "^3.1.0", "glob": "^7.0.5", "lodash": "^4.14.2", "minimist": "^1.2.5", "mkdirp": "^1.0.4", "tmp": "^0.2.1"}, "bin": {"wpi18n": "bin/wpi18n"}}, "node_modules/once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "dev": true, "dependencies": {"wrappy": "1"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/readable-stream": {"version": "3.6.2", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "integrity": "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==", "dev": true, "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==", "dev": true}, "node_modules/string_decoder": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==", "dev": true, "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/tmp": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/tmp/-/tmp-0.2.3.tgz", "integrity": "sha512-nZD7m9iCPC5g0pYmcaxogYKggSfLsdxl8of3Q/oIbqCqLLIO9IAF0GWjX1z9NZRHPiXv8Wex4yDCaZsgEw0Y8w==", "dev": true, "engines": {"node": ">=14.14"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==", "dev": true}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==", "dev": true}}, "platform": {"php": ">=7.4"}}