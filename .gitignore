/nbproject/private/
node_modules
project.xml
project.properties
.DS_Store
Thumbs.db
.buildpath
.project
.settings*
.vscode
sftp-config.json
/deploy/
/wc-apidocs/
/languages/
screenshots/
/assets/**/*.min.js

# Ignore all log files except for .htaccess
/logs/*
!/logs/.htaccess

tests/e2e/config/local-*
.eslintcache

/vendor/
/includes/box-packer/prefixed/

dist
.idea

woocommerce-shipping-usps.zip
/.phpunit.result.cache
/tests/php/config.php
/tests/php/compare/
