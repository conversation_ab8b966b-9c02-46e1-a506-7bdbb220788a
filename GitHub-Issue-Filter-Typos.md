# GitHub Issue: Fix filter name typos: 'woocommmerce' should be 'woocommerce'

## Issue Title
Fix filter name typos: 'woocommmerce' should be 'woocommerce'

## Issue Body

### Description
There are several filter names in the codebase that contain a typo where 'woocommmerce' (with an extra 'm') is used instead of 'woocommerce'. This affects developers who want to hook into these filters.

### Affected Filters

The following filters have the typo and need to be corrected:

#### 1. `woocommmerce_shipping_usps_tubes_remove_non_standard_fees`
**Files affected:**
- `includes/api/legacy/class-legacy-api.php:932`
- `includes/api/rest/class-rest-api.php:1655`

**Current (incorrect):**
```php
apply_filters( 'woocommmerce_shipping_usps_tubes_remove_non_standard_fees', true )
```

**Should be:**
```php
apply_filters( 'woocommerce_shipping_usps_tubes_remove_non_standard_fees', true )
```

#### 2. `woocommmerce_shipping_usps_custom_service_rate_name`
**Files affected:**
- `includes/api/legacy/class-legacy-api.php:982`
- `includes/api/rest/class-rest-api.php:1726`

**Current (incorrect):**
```php
apply_filters( 'woocommmerce_shipping_usps_custom_service_rate_name', $rate_name, $quote )
```

**Should be:**
```php
apply_filters( 'woocommerce_shipping_usps_custom_service_rate_name', $rate_name, $quote )
```

#### 3. `woocommmerce_shipping_usps_should_show_service_commitment`
**Files affected:**
- `includes/api/rest/class-rest-api.php:1693`

**Current (incorrect):**
```php
apply_filters( 'woocommmerce_shipping_usps_should_show_service_commitment', true, $rate_option )
```

**Should be:**
```php
apply_filters( 'woocommerce_shipping_usps_should_show_service_commitment', true, $rate_option )
```

### Impact

- **Developers** who try to hook into these filters using the correct spelling will find their hooks don't work
- **Backward compatibility** concerns if these filters are already being used by some developers with the incorrect spelling
- **Consistency** issues with other WooCommerce filter naming conventions

### Proposed Solution

1. **Fix the typos** in all affected files
2. **Consider backward compatibility** - if any of these filters are documented or widely used, we might need to support both versions temporarily
3. **Add deprecation notices** if maintaining backward compatibility for the incorrect filter names

### Additional Notes

- These typos appear to have been present for some time across both Legacy and REST API implementations
- The issue was discovered during code review of PR #445
- This affects filter consistency across the entire plugin

### Acceptance Criteria

- [ ] All filter names are corrected to use 'woocommerce' instead of 'woocommmerce'
- [ ] Backward compatibility is considered for existing implementations
- [ ] Documentation is updated if these filters are documented anywhere
- [ ] Tests are updated to use correct filter names

### Labels Suggested
- `bug`
- `good first issue`
- `documentation`

### Priority
**Medium** - While this doesn't break core functionality, it affects developer experience and plugin consistency.

---

## Instructions for Creating the Issue

1. Go to https://github.com/woocommerce/woocommerce-shipping-usps/issues/new
2. Copy the title: `Fix filter name typos: 'woocommmerce' should be 'woocommerce'`
3. Copy the issue body content above (everything under "Issue Body")
4. Add appropriate labels: `bug`, `good first issue`, `documentation`
5. Submit the issue

## Files to Fix

When implementing the fix, the following files need to be updated:

### includes/api/legacy/class-legacy-api.php
- Line 932: Fix `woocommmerce_shipping_usps_tubes_remove_non_standard_fees`
- Line 982: Fix `woocommmerce_shipping_usps_custom_service_rate_name`

### includes/api/rest/class-rest-api.php  
- Line 1655: Fix `woocommmerce_shipping_usps_tubes_remove_non_standard_fees`
- Line 1693: Fix `woocommmerce_shipping_usps_should_show_service_commitment`
- Line 1726: Fix `woocommmerce_shipping_usps_custom_service_rate_name`
