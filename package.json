{"name": "woocommerce-shipping-usps", "title": "WooCommerce Shipping USPS", "version": "5.2.4", "homepage": "https://woocommerce.com/products/usps-shipping-method/", "repository": {"type": "git", "url": "git://github.com/woocommerce/woocommerce-shipping-usps.git"}, "config": {"use_pnpm": true, "translate": true, "use_gh_release_notes": true}, "devDependencies": {"node-wp-i18n": "~1.2.7"}, "scripts": {"prebuild": "rm -rf ./vendor && composer clear-cache", "build": "composer install --no-dev -o && pnpm run makepot && pnpm run archive", "build:dev": "composer install -o && pnpm run makepot", "archive": "composer archive --file=$npm_package_name --format=zip", "postarchive": "rm -rf $npm_package_name && unzip $npm_package_name.zip -d $npm_package_name && rm $npm_package_name.zip && zip -r $npm_package_name.zip $npm_package_name && rm -rf $npm_package_name", "makepot": "wpi18n makepot --domain-path languages --pot-file $npm_package_name.pot --type plugin --main-file $npm_package_name.php --exclude node_modules,tests,docs", "build:qit": "composer install --no-dev -o && pnpm run makepot && pnpm run archive:qit && pnpm run postarchive", "archive:qit": "composer archive --file=$npm_package_name --format=zip && pnpm run zip:phpstan_config", "zip:phpstan_config": "zip -r $npm_package_name.zip .phpstan/dist/* -j", "test:php:setup": "./tests/bin/install-wc-tests.sh && composer install --no-dev -o", "test:php": "phpunit"}, "engines": {"node": "^22.14.0", "pnpm": "^10.4.1"}}