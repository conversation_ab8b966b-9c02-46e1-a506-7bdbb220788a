<?xml version="1.0"?>
<ruleset name="WooCommerce USPS Shipping">
    <description>Custom PHPCS configuration for WooCommerce USPS Shipping</description>

    <!-- What to scan -->
    <file>.</file>

    <!-- Exclude patterns -->
    <exclude-pattern>vendor/</exclude-pattern>
    <exclude-pattern>.git/</exclude-pattern>
    <exclude-pattern>assets/</exclude-pattern>
    <exclude-pattern>node_modules/</exclude-pattern>
    <exclude-pattern>tests/</exclude-pattern>
    <exclude-pattern>.phpstan/</exclude-pattern>

    <!-- Include the WordPress-Extra, WordPress-Docs and WordPress-Core rulesets. -->
    <rule ref="WordPress-Extra">
        <!-- We are okay with short array syntax -->
        <exclude name="Generic.Arrays.DisallowShortArraySyntax"/>
        <!-- We don't require file comments -->
        <exclude name="Squiz.Commenting.FileComment"/>
    </rule>
    <rule ref="WordPress-Docs"/>
    <rule ref="WordPress-Core"/>

    <!-- Include the WooCommerce-Core ruleset -->
    <rule ref="WooCommerce-Core"/>

    <!-- Exclude problematic PHPCompatibility sniffs -->
    <rule ref="PHPCompatibility">
        <exclude name="PHPCompatibility"/>
    </rule>

    <!-- Show progress and sniff codes -->
    <arg value="ps"/>

    <!-- Strip the filepaths down to the relevant bit. -->
    <arg name="basepath" value="./"/>

    <!-- Check up to 8 files simultaneously. -->
    <arg name="parallel" value="8"/>

    <!-- Only check the PHP files. JS files are checked separately with JSCS and JSHint. -->
    <arg name="extensions" value="php"/>

    <!-- Check for cross-version compatibility. -->
    <config name="testVersion" value="7.4-"/>

    <!-- Custom properties for WordPress sniffs -->
    <config name="minimum_supported_wp_version" value="5.6"/>

    <!-- Custom sanitizing functions -->
    <rule ref="WordPress.Security.ValidatedSanitizedInput">
        <properties>
            <property name="customSanitizingFunctions" type="array">
                <element value="wc_clean"/>
                <element value="wc_sanitize_tooltip"/>
                <element value="sanitize_text_field"/>
                <element value="sanitize_textarea_field"/>
            </property>
        </properties>
    </rule>

    <!-- Custom escaping functions -->
    <rule ref="WordPress.Security.EscapeOutput">
        <properties>
            <property name="customEscapingFunctions" type="array">
                <element value="wc_help_tip"/>
                <element value="wc_sanitize_tooltip"/>
            </property>
        </properties>
    </rule>
</ruleset>
