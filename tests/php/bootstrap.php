<?php
/**
 * PHPUnit bootstrap file.
 *
 * @package Woocommerce_Shipping_Usps
 */

/**
 * Support for:
 * 1. `WC_DEVELOP_DIR` environment variable.
 * 2. Tests checked out to /tmp.
 */
if ( false !== getenv( 'WC_DEVELOP_DIR' ) ) {
	$wc_root = getenv( 'WC_DEVELOP_DIR' );
} elseif ( file_exists( '/tmp/woocommerce/plugins/woocommerce/tests/legacy/bootstrap.php' ) ) {
	$wc_root = '/tmp/woocommerce/plugins/woocommerce';
} else {
	exit( 'Could not determine test root directory. Aborting. Have you run bin/install-wc-tests.sh?' );
}

$_tests_dir = getenv( 'WP_TESTS_DIR' );

if ( ! $_tests_dir ) {
	$_tests_dir = rtrim( sys_get_temp_dir(), '/\\' ) . '/wordpress-tests-lib';
}

// Set default path to PHPUnit Polyfills if not defined in environment
if (!getenv('WP_TESTS_PHPUNIT_POLYFILLS_PATH')) {
	putenv('WP_TESTS_PHPUNIT_POLYFILLS_PATH=' . dirname(dirname(__DIR__)) . '/vendor/yoast/phpunit-polyfills');
}

// Forward custom PHPUnit Polyfills configuration to PHPUnit bootstrap file.
$_phpunit_polyfills_path = getenv( 'WP_TESTS_PHPUNIT_POLYFILLS_PATH' );
if ( false !== $_phpunit_polyfills_path ) {
	define( 'WP_TESTS_PHPUNIT_POLYFILLS_PATH', $_phpunit_polyfills_path );
}

if ( ! file_exists( "{$_tests_dir}/includes/functions.php" ) ) {
	echo "Could not find {$_tests_dir}/includes/functions.php, have you run bin/install-wp-tests.sh ?" . PHP_EOL; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
	exit( 1 );
}

// Give access to tests_add_filter() function.
require_once "{$_tests_dir}/includes/functions.php";

/**
 * Manually load the plugin being tested.
 */
function _manually_load_plugin() {
//	require_once dirname( dirname( dirname( __FILE__ ) ) ) . '/vendor/autoload_packages.php';
	require dirname( dirname( dirname( __FILE__ ) ) ) . '/woocommerce-shipping-usps.php';
}

tests_add_filter( 'muplugins_loaded', '_manually_load_plugin' );

require $wc_root . '/tests/legacy/bootstrap.php';
