<?php
/**
 * Sample configuration file for USPS API parity tests.
 *
 * Copy this file to config.php and update with your test credentials.
 */

return array(
	// Set to true to use real API calls instead of mock responses.
	'use_real_api' => false,

	// Legacy API credentials.
	'legacy_api'   => array(
		// Your USPS Web Tools User ID.
		'user_id' => '',
	),

	// REST API credentials.
	'rest_api'     => array(
		// Your USPS REST API Client ID.
		'client_id'     => '',

		// Your USPS REST API Client Secret.
		'client_secret' => '',
	),
);
