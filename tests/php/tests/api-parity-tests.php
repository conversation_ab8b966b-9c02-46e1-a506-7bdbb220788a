<?php
/**
 * Test case for USPS API.
 *
 * @package WC_Shipping_USPS
 */

require_once WC_USPS_ABSPATH . 'includes/class-wc-shipping-usps.php';
require_once __DIR__ . '/test-data.php';

/**
 * Custom logger class to capture API requests and responses.
 */
class Capture_Logger extends WooCommerce\USPS\Logger {
	/**
	 * Captured debug logs.
	 *
	 * @var array
	 */
	private $captured_logs = array();

	/**
	 * Add a debug log entry and capture it.
	 *
	 * @param string $message Message to display.
	 * @param array  $data    Additional contextual data to pass.
	 *
	 * @return void
	 */
	public function debug( string $message, array $data = array() ) {
		// Call parent method to ensure normal logging behavior.
		parent::debug( $message, $data );

		// Capture the log.
		$this->captured_logs[] = array(
			'message' => $message,
			'data'    => $data,
		);
	}

	/**
	 * Get all captured logs.
	 *
	 * @return array
	 */
	public function get_captured_logs() {
		return $this->captured_logs;
	}

	/**
	 * Clear captured logs.
	 *
	 * @return void
	 */
	public function clear_captured_logs() {
		$this->captured_logs = array();
	}
}

/**
 * USPS API Test class.
 */
class API_Parity_Tests extends WP_UnitTestCase {

	/**
	 * Test data.
	 *
	 * @var array
	 */
	protected $test_data;

	/**
	 * Config data.
	 *
	 * @var array
	 */
	protected $config;

	/**
	 * Legacy API logger.
	 *
	 * @var Capture_Logger
	 */
	protected $legacy_logger;

	/**
	 * REST API logger.
	 *
	 * @var Capture_Logger
	 */
	protected $rest_logger;

	/**
	 * Set up.
	 */
	public function setUp(): void {
		parent::setUp();
		$this->test_data = wc_usps_get_test_data();

		// Load config.
		$config_file = dirname( __DIR__ ) . '/config.php';
		if ( file_exists( $config_file ) ) {
			$this->config = include $config_file;
		} else {
			$this->markTestSkipped( 'Config file not found. Copy config.sample.php to config.php and update with your credentials.' );
		}
	}

	/**
	 * Created product IDs for cleanup.
	 *
	 * @var array
	 */
	protected $created_product_ids = array();

	/**
	 * Create a WooCommerce-compatible package from test data.
	 *
	 * @param array $from_address From address.
	 * @param array $to_address   To address.
	 * @param array $products     Products to ship.
	 * @param int   $quantity     Quantity of each product.
	 *
	 * @return array
	 */
	protected function create_package( $from_address, $to_address, $products, $quantity = 1 ) {
		$package = array(
			'destination' => $to_address,
			'origin'      => $from_address,
			'contents'    => array(),
		);

		foreach ( $products as $product_data ) {
			// Create a real WooCommerce product using WooCommerce core helper.
			$product = WC_Helper_Product::create_simple_product(
				true,
				array(
					'name'   => $product_data['name'],
					'weight' => $product_data['weight'],
					'length' => $product_data['length'],
					'width'  => $product_data['width'],
					'height' => $product_data['height'],
					'price'  => $product_data['value'],
				)
			);

			// Store the product ID for cleanup.
			$this->created_product_ids[] = $product->get_id();

			$package['contents'][] = array(
				'data'     => $product,
				'quantity' => $quantity,
			);
		}

		return $package;
	}

	/**
	 * Clean up created products after tests.
	 */
	public function tearDown(): void {
		// Delete any products created during the test.
		foreach ( $this->created_product_ids as $product_id ) {
			WC_Helper_Product::delete_product( $product_id );
		}

		// Reset the array.
		$this->created_product_ids = array();

		parent::tearDown();
	}

	/**
	 * Initialize USPS shipping method with specified API type.
	 *
	 * @param string $api_type API type ('legacy' or 'rest').
	 *
	 * @return WC_Shipping_USPS
	 */
	protected function init_shipping_method( $api_type = 'legacy' ) {
		$shipping_method = new WC_Shipping_USPS();

		// Set API type.
		$shipping_method->api_type = $api_type;

		// Set API credentials.
		if ( 'legacy' === $api_type ) {
			$shipping_method->user_id = $this->config['legacy_api']['user_id'];
		} else {
			$shipping_method->client_id     = $this->config['rest_api']['client_id'];
			$shipping_method->client_secret = $this->config['rest_api']['client_secret'];
		}

		// Load services based on API type.
		$services_path             = WC_USPS_ABSPATH . 'includes/data/data-services.php';
		$shipping_method->services = include $services_path;

		// Load flat rate boxes.
		$shipping_method->flat_rate_boxes = include WC_USPS_ABSPATH . 'includes/data/data-flat-rate-boxes.php';

		// Set default settings.
		$shipping_method->title                    = 'USPS';
		$shipping_method->origin                   = '90210'; // Default origin ZIP code for testing.
		$shipping_method->packing_method           = 'per_item';
		$shipping_method->enable_standard_services = true;
		$shipping_method->enable_flat_rate_boxes   = 'yes';
		$shipping_method->debug                    = true; // Enable debug mode to capture API requests.

		// Create custom logger to capture API requests.
		if ( 'legacy' === $api_type ) {
			$this->legacy_logger     = new Capture_Logger( $shipping_method->debug );
			$shipping_method->logger = $this->legacy_logger;
		} else {
			$this->rest_logger       = new Capture_Logger( $shipping_method->debug );
			$shipping_method->logger = $this->rest_logger;
		}

		// Initialize the API.
		$shipping_method->oauth = new WooCommerce\USPS\API\USPS_OAuth( $shipping_method );
		$shipping_method->api   = 'rest' === $api_type
			? new WooCommerce\USPS\API\REST_API( $shipping_method )
			: new WooCommerce\USPS\API\Legacy_API( $shipping_method );

		return $shipping_method;
	}

	/**
	 * Test API parity for a given scenario.
	 *
	 * @param string $scenario_key The scenario key in test_data.
	 */
	protected function test_api_parity_for_scenario( $scenario_key ) {
		// Skip if using mock responses.
		if ( ! $this->config['use_real_api'] ) {
			$this->markTestSkipped( 'Test skipped because use_real_api is set to false.' );
		}

		// Get test scenario.
		$scenario = $this->test_data['test_scenarios'][ $scenario_key ];

		// Get addresses.
		$from_address = $this->test_data['from_addresses'][ $scenario['from_address'] ];

		// Get to address based on whether it's domestic or international.
		if ( isset( $this->test_data['to_addresses_domestic'][ $scenario['to_address'] ] ) ) {
			$to_address = $this->test_data['to_addresses_domestic'][ $scenario['to_address'] ];
		} else {
			$to_address = $this->test_data['to_addresses_international'][ $scenario['to_address'] ];
		}

		// Get products.
		$products = array();
		foreach ( $scenario['products'] as $product_key ) {
			$products[] = $this->test_data['products'][ $product_key ];
		}

		// Create package.
		$package = $this->create_package( $from_address, $to_address, $products );

		// Initialize shipping method with legacy API.
		$legacy_shipping = $this->init_shipping_method( 'legacy' );

		// Calculate shipping with legacy API.
		$legacy_shipping->calculate_shipping( $package );
		$legacy_rates = $legacy_shipping->found_rates;

		// Initialize shipping method with REST API.
		$rest_shipping = $this->init_shipping_method( 'rest' );

		// Calculate shipping with REST API.
		$rest_shipping->calculate_shipping( $package );
		$rest_rates = $rest_shipping->found_rates;

		// Save API requests and responses to files for comparison first
		// to ensure they're saved even if compare_rates throws an exception
		$this->save_requests_to_files(
			$this->legacy_logger->get_captured_logs(),
			$this->rest_logger->get_captured_logs(),
			$scenario_key
		);

		// Save rates to files for comparison first
		// to ensure they're saved even if compare_rates throws an exception
		$this->save_rates_to_files( $legacy_rates, $rest_rates, $scenario_key );

		// Compare rates.
		try {
			$this->compare_rates( $legacy_rates, $rest_rates, $scenario_key );
		} catch ( Exception $e ) {
			// Log the exception but continue execution
			error_log( 'Exception in compare_rates: ' . $e->getMessage() );
			
			// Re-throw the exception to maintain the original test behavior
			throw $e;
		}
	}

	/**
	 * Test domestic small package rate calculation.
	 */
	public function test_domestic_small_package_rate_calculation() {
		$this->test_api_parity_for_scenario( 'domestic_small_package' );
	}

	/**
	 * Test domestic medium package rate calculation.
	 */
	public function test_domestic_medium_package_rate_calculation() {
		$this->test_api_parity_for_scenario( 'domestic_medium_package' );
	}

	/**
	 * Test domestic large package rate calculation.
	 */
	public function test_domestic_large_package_rate_calculation() {
		$this->test_api_parity_for_scenario( 'domestic_large_package' );
	}

	/**
	 * Test domestic media package rate calculation.
	 */
	public function test_domestic_media_package_rate_calculation() {
		$this->test_api_parity_for_scenario( 'domestic_media_package' );
	}

	/**
	 * Test international small package rate calculation.
	 */
	public function test_international_small_package_rate_calculation() {
		$this->test_api_parity_for_scenario( 'international_small_package' );
	}

	/**
	 * Test international medium package rate calculation.
	 */
	public function test_international_medium_package_rate_calculation() {
		$this->test_api_parity_for_scenario( 'international_medium_package' );
	}

	/**
	 * Test domestic flat envelope to PO Box rate calculation.
	 */
	public function test_domestic_flat_envelope_to_pobox_rate_calculation() {
		$this->test_api_parity_for_scenario( 'domestic_flat_envelope_to_pobox' );
	}

	/**
	 * Test domestic mixed small and medium items rate calculation.
	 */
	public function test_domestic_mixed_small_and_medium_rate_calculation() {
		$this->test_api_parity_for_scenario( 'domestic_mixed_small_and_medium' );
	}

	/**
	 * Test domestic two small items to commercial address rate calculation.
	 */
	public function test_domestic_two_small_items_commercial_rate_calculation() {
		$this->test_api_parity_for_scenario( 'domestic_two_small_items_commercial' );
	}

	/**
	 * Test international Canada medium weight rate calculation.
	 */
	public function test_international_canada_medium_weight_rate_calculation() {
		$this->test_api_parity_for_scenario( 'international_canada_medium_weight' );
	}

	/**
	 * Test international UK envelope rate calculation.
	 */
	public function test_international_uk_envelope_rate_calculation() {
		$this->test_api_parity_for_scenario( 'international_uk_envelope' );
	}

	/**
	 * Test international Australia large heavy rate calculation.
	 */
	public function test_international_australia_large_heavy_rate_calculation() {
		$this->test_api_parity_for_scenario( 'international_australia_large_heavy' );
	}

	/**
	 * Test domestic medium to PO Box rate calculation.
	 */
	public function test_domestic_medium_to_pobox_rate_calculation() {
		$this->test_api_parity_for_scenario( 'domestic_medium_to_pobox' );
	}

	/**
	 * Test domestic large to commercial address rate calculation.
	 */
	public function test_domestic_large_to_commercial_rate_calculation() {
		$this->test_api_parity_for_scenario( 'domestic_large_to_commercial' );
	}

	/**
	 * Test domestic small package to PO Box rate calculation.
	 */
	public function test_domestic_small_to_pobox_rate_calculation() {
		$this->test_api_parity_for_scenario( 'domestic_small_to_pobox' );
	}

	/**
	 * Test two medium items to residential address rate calculation.
	 */
	public function test_domestic_two_medium_items_residential_rate_calculation() {
		$this->test_api_parity_for_scenario( 'domestic_two_medium_items_residential' );
	}

	/**
	 * Test mixed flat envelope and small item to residential address rate calculation.
	 */
	public function test_domestic_flat_and_small_mixed_residential_rate_calculation() {
		$this->test_api_parity_for_scenario( 'domestic_flat_and_small_mixed_residential' );
	}

	/**
	 * Test international Canada envelope rate calculation.
	 */
	public function test_international_canada_envelope_rate_calculation() {
		$this->test_api_parity_for_scenario( 'international_canada_envelope' );
	}

	/**
	 * Test international Australia small package rate calculation.
	 */
	public function test_international_australia_small_package_rate_calculation() {
		$this->test_api_parity_for_scenario( 'international_australia_small_package' );
	}

	/**
	 * Test domestic large package to PO Box rate calculation.
	 */
	public function test_domestic_large_to_pobox_rate_calculation() {
		$this->test_api_parity_for_scenario( 'domestic_large_to_pobox' );
	}

	/**
	 * Normalize a rate label for comparison by removing provider suffix and delivery estimates.
	 *
	 * - Strips the trailing/inline "(USPS)".
	 * - Strips any parenthetical that contains the word "days" (e.g., "(6 days)").
	 * - Trims extra whitespace and collapses multiple spaces.
	 *
	 * @param string $label Original rate label.
	 * @return string Normalized label for comparison.
	 */
	private function normalize_label_for_compare( $label ) {
		$label = (string) $label;
		// Remove provider suffix.
		$label = preg_replace( '/\s*\(USPS\)/i', '', $label );
		// Remove any parenthetical containing "days".
		$label = preg_replace( '/\s*\([^)]*days\)/i', '', $label );
		// Trim and collapse whitespace.
		$label = trim( $label );
		$label = preg_replace( '/\s{2,}/', ' ', $label );
		return $label;
	}
	
	/**
	 * Compare rates from legacy and REST APIs.
	 *
	 * @param array  $legacy_rates Rates from legacy API.
	 * @param array  $rest_rates   Rates from REST API.
	 * @param string $scenario_key The scenario key for file naming.
	 */
	protected function compare_rates( $legacy_rates, $rest_rates, $scenario_key ) {
		// Check that both APIs returned rates.
		$this->assertNotEmpty( $legacy_rates, 'Legacy API did not return any rates.' );
		$this->assertNotEmpty( $rest_rates, 'REST API did not return any rates.' );

		// Build frequency maps keyed by label + rounded cost (to 2 decimals).
		$legacy_map = array();
		foreach ( $legacy_rates as $legacy_rate ) {
			$label = isset( $legacy_rate['label'] ) ? (string) $legacy_rate['label'] : '';
			$label = $this->normalize_label_for_compare( $label );
			$cost  = isset( $legacy_rate['cost'] ) ? (float) $legacy_rate['cost'] : 0.0;
			$key   = $label . '|' . sprintf( '%.2f', round( $cost, 2 ) );
			if ( ! isset( $legacy_map[ $key ] ) ) {
				$legacy_map[ $key ] = 0;
			}
			$legacy_map[ $key ]++;
		}

		$rest_map = array();
		foreach ( $rest_rates as $rest_rate ) {
			$label = isset( $rest_rate['label'] ) ? (string) $rest_rate['label'] : '';
			$label = $this->normalize_label_for_compare( $label );
			$cost  = isset( $rest_rate['cost'] ) ? (float) $rest_rate['cost'] : 0.0;
			$key   = $label . '|' . sprintf( '%.2f', round( $cost, 2 ) );
			if ( ! isset( $rest_map[ $key ] ) ) {
				$rest_map[ $key ] = 0;
			}
			$rest_map[ $key ]++;
		}

		// Check that both APIs returned the same number of rates.
		$this->assertEquals(
			array_sum( $legacy_map ),
			array_sum( $rest_map ),
			'Legacy and REST APIs returned different number of rates.'
		);

		// Compare the sets of label+price combinations.
		ksort( $legacy_map );
		ksort( $rest_map );

		$this->assertEquals(
			$legacy_map,
			$rest_map,
			'Legacy and REST APIs returned different label+price combinations.'
		);
	}

	/**
	 * Save rates to files for comparison.
	 *
	 * @param array  $legacy_rates Rates from legacy API.
	 * @param array  $rest_rates   Rates from REST API.
	 * @param string $scenario_key The scenario key for file naming.
	 */
	protected function save_rates_to_files( $legacy_rates, $rest_rates, $scenario_key ) {
		// Create directory for this scenario if it doesn't exist.
		$compare_dir = dirname( __DIR__ ) . '/compare/' . $scenario_key;
		if ( ! file_exists( $compare_dir ) ) {
			mkdir( $compare_dir, 0755, true );
		}

		// Format the rates for better readability.
		$formatted_legacy_rates = $this->format_rates_for_output( $legacy_rates );
		$formatted_rest_rates   = $this->format_rates_for_output( $rest_rates );

		// Save legacy rates to file.
		file_put_contents(
			$compare_dir . '/legacy_rates.json',
			json_encode( $formatted_legacy_rates, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES )
		);

		// Save REST rates to file.
		file_put_contents(
			$compare_dir . '/rest_rates.json',
			json_encode( $formatted_rest_rates, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES )
		);

		// Save a comparison file that shows differences.
		$comparison = array(
			'scenario'          => $scenario_key,
			'legacy_count'      => count( $legacy_rates ),
			'rest_count'        => count( $rest_rates ),
			'matching_rates'    => array(),
			'legacy_only_rates' => array(),
			'rest_only_rates'   => array(),
			'different_costs'   => array(),
		);

		// Build maps keyed by label + rounded cost, and group costs by label.
		$legacy_key_counts       = array();
		$rest_key_counts         = array();
		$legacy_costs_by_label   = array();
		$rest_costs_by_label     = array();

		foreach ( $legacy_rates as $legacy_rate ) {
			$label    = isset( $legacy_rate['label'] ) ? (string) $legacy_rate['label'] : '';
			$label    = $this->normalize_label_for_compare( $label );
			$cost     = isset( $legacy_rate['cost'] ) ? (float) $legacy_rate['cost'] : 0.0;
			$cost_key = sprintf( '%.2f', round( $cost, 2 ) );
			$key     = $label . '|' . $cost_key;

			if ( ! isset( $legacy_key_counts[ $key ] ) ) {
				$legacy_key_counts[ $key ] = 0;
			}
			$legacy_key_counts[ $key ]++;

			if ( ! isset( $legacy_costs_by_label[ $label ] ) ) {
				$legacy_costs_by_label[ $label ] = array();
			}
			if ( ! in_array( $cost_key, $legacy_costs_by_label[ $label ], true ) ) {
				$legacy_costs_by_label[ $label ][] = $cost_key;
			}
		}

		foreach ( $rest_rates as $rest_rate ) {
			$label    = isset( $rest_rate['label'] ) ? (string) $rest_rate['label'] : '';
			$label    = $this->normalize_label_for_compare( $label );
			$cost     = isset( $rest_rate['cost'] ) ? (float) $rest_rate['cost'] : 0.0;
			$cost_key = sprintf( '%.2f', round( $cost, 2 ) );
			$key     = $label . '|' . $cost_key;

			if ( ! isset( $rest_key_counts[ $key ] ) ) {
				$rest_key_counts[ $key ] = 0;
			}
			$rest_key_counts[ $key ]++;

			if ( ! isset( $rest_costs_by_label[ $label ] ) ) {
				$rest_costs_by_label[ $label ] = array();
			}
			if ( ! in_array( $cost_key, $rest_costs_by_label[ $label ], true ) ) {
				$rest_costs_by_label[ $label ][] = $cost_key;
			}
		}

		// Determine matching and only-in lists based on label + price keys.
		foreach ( $legacy_key_counts as $key => $count ) {
			if ( isset( $rest_key_counts[ $key ] ) ) {
				$comparison['matching_rates'][] = $key;
			} else {
				$comparison['legacy_only_rates'][ $key ] = $count;
			}
		}
		foreach ( $rest_key_counts as $key => $count ) {
			if ( ! isset( $legacy_key_counts[ $key ] ) ) {
				$comparison['rest_only_rates'][ $key ] = $count;
			}
		}

		// Determine labels with different cost sets between APIs.
		$all_labels = array_unique( array_merge( array_keys( $legacy_costs_by_label ), array_keys( $rest_costs_by_label ) ) );
		foreach ( $all_labels as $label ) {
			$legacy_costs = isset( $legacy_costs_by_label[ $label ] ) ? $legacy_costs_by_label[ $label ] : array();
			$rest_costs   = isset( $rest_costs_by_label[ $label ] ) ? $rest_costs_by_label[ $label ] : array();
			sort( $legacy_costs );
			sort( $rest_costs );

			if ( $legacy_costs !== $rest_costs ) {
				$comparison['different_costs'][ $label ] = array(
					'legacy_costs'      => $legacy_costs,
					'rest_costs'        => $rest_costs,
					'legacy_only_costs' => array_values( array_diff( $legacy_costs, $rest_costs ) ),
					'rest_only_costs'   => array_values( array_diff( $rest_costs, $legacy_costs ) ),
				);
			}
		}

		// Save comparison to file.
		file_put_contents(
			$compare_dir . '/comparison.json',
			json_encode( $comparison, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES )
		);
	}

	/**
	 * Format rates for output to make them more readable.
	 *
	 * @param array $rates Rates to format.
	 * @return array Formatted rates.
	 */
	protected function format_rates_for_output( $rates ) {
		$formatted_rates = array();

		foreach ( $rates as $rate_id => $rate ) {
			$formatted_rates[ $rate_id ] = array(
				'id'        => $rate_id,
				'label'     => $rate['label'],
				'cost'      => $rate['cost'],
				'meta_data' => isset( $rate['meta_data'] ) ? $rate['meta_data'] : array(),
			);
		}

		return $formatted_rates;
	}

	/**
	 * Save API requests to files for comparison.
	 *
	 * @param array  $legacy_logs Logs from legacy API.
	 * @param array  $rest_logs   Logs from REST API.
	 * @param string $scenario_key The scenario key for file naming.
	 */
	protected function save_requests_to_files( $legacy_logs, $rest_logs, $scenario_key ) {
		// Create directory for this scenario if it doesn't exist.
		$compare_dir = dirname( __DIR__ ) . '/compare/' . $scenario_key;
		if ( ! file_exists( $compare_dir ) ) {
			mkdir( $compare_dir, 0755, true );
		}

		// Extract request and response data from logs.
		$legacy_requests = $this->extract_requests_from_logs( $legacy_logs );
		$legacy_response = $this->extract_response_from_logs( $legacy_logs );
		$rest_requests   = $this->extract_requests_from_logs( $rest_logs );
		$rest_responses  = $this->extract_responses_from_logs( $rest_logs );

		// For backwards compatibility: if there is exactly one request, write the single object.
		$legacy_request_payload = array();
		if ( is_array( $legacy_requests ) ) {
			if ( 1 === count( $legacy_requests ) ) {
				$legacy_request_payload = $legacy_requests[0];
			} elseif ( count( $legacy_requests ) > 1 ) {
				$legacy_request_payload = array_values( $legacy_requests );
			}
		}

		$rest_request_payload = array();
		if ( is_array( $rest_requests ) ) {
			if ( 1 === count( $rest_requests ) ) {
				$rest_request_payload = $rest_requests[0];
			} elseif ( count( $rest_requests ) > 1 ) {
				$rest_request_payload = array_values( $rest_requests );
			}
		}

		// Save legacy request to file.
		$legacy_request_file   = $compare_dir . '/legacy_request.json';
		$legacy_request_result = file_put_contents(
			$legacy_request_file,
			json_encode( $legacy_request_payload, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES )
		);

		// Save legacy response to file.
		$legacy_response_file   = $compare_dir . '/legacy_response.json';
		$legacy_response_result = file_put_contents(
			$legacy_response_file,
			json_encode( $legacy_response, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES )
		);

		// Save REST request to file.
		$rest_request_file   = $compare_dir . '/rest_request.json';
		$rest_request_result = file_put_contents(
			$rest_request_file,
			json_encode( $rest_request_payload, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES )
		);

		// Save REST response(s) to file. Write a single object if exactly one, otherwise an array.
		$rest_response_payload = array();
		if ( is_array( $rest_responses ) ) {
			if ( 1 === count( $rest_responses ) ) {
				$rest_response_payload = $rest_responses[0];
			} elseif ( count( $rest_responses ) > 1 ) {
				$rest_response_payload = array_values( $rest_responses );
			}
		}

		$rest_response_file   = $compare_dir . '/rest_response.json';
		$rest_response_result = file_put_contents(
			$rest_response_file,
			json_encode( $rest_response_payload, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES )
		);
	}

	/**
	 * Extract request data from logs.
	 *
	 * @param array $logs Captured logs.
	 * @return array Request data.
	 */
	private function extract_request_from_logs( $logs ) {
		// First, try to find a log entry with "Request" in the message.
		foreach ( $logs as $log ) {
			if ( strpos( $log['message'], 'Request' ) !== false ) {
				return $log['data'];
			}
		}

		// If no specific "Request" entry is found, look for any entry with request-like data.
		foreach ( $logs as $log ) {
			if ( isset( $log['data']['method'] ) || isset( $log['data']['url'] ) || isset( $log['data']['body'] ) ) {
				return $log['data'];
			}
		}

		// If still nothing found, return the first log entry with data.
		foreach ( $logs as $log ) {
			if ( ! empty( $log['data'] ) ) {
				return $log['data'];
			}
		}

		return array();
	}

	/**
	 * Extract all request entries from logs.
	 *
	 * @param array $logs Captured logs.
	 * @return array Array of request payloads in the order they appeared.
	 */
	private function extract_requests_from_logs( $logs ) {
		$requests = array();

		// Collect all entries that clearly indicate a request in the message.
		foreach ( $logs as $log ) {
			if ( isset( $log['message'] ) && false !== stripos( $log['message'], 'Request' ) ) {
				if ( isset( $log['data'] ) && ! empty( $log['data'] ) ) {
					$requests[] = $log['data'];
				}
			}
		}

		// If none were labeled, heuristically collect payloads that look like HTTP/API requests.
		if ( empty( $requests ) ) {
			foreach ( $logs as $log ) {
				if ( isset( $log['data'] ) && ( isset( $log['data']['method'] ) || isset( $log['data']['url'] ) || isset( $log['data']['body'] ) || isset( $log['data']['pricingOptions'] ) ) ) {
					$requests[] = $log['data'];
				}
			}
		}

		return $requests;
	}

	/**
	 * Extract all response entries from logs.
	 *
	 * @param array $logs Captured logs.
	 * @return array Array of response payloads in the order they appeared.
	 */
	private function extract_responses_from_logs( $logs ) {
		$responses = array();

		// Collect all entries that clearly indicate a response in the message.
		foreach ( $logs as $log ) {
			if ( isset( $log['message'] ) && false !== stripos( $log['message'], 'Response' ) ) {
				if ( isset( $log['data'] ) && ! empty( $log['data'] ) ) {
					$responses[] = $log['data'];
				}
			}
		}

		// If none were labeled, heuristically collect payloads that look like responses.
		if ( empty( $responses ) ) {
			foreach ( $logs as $log ) {
				if ( isset( $log['data'] ) && ( isset( $log['data']['BODY'] ) || isset( $log['data']['body'] ) || isset( $log['data']['CODE'] ) || isset( $log['data']['response'] ) ) ) {
					$responses[] = $log['data'];
				}
			}
		}

		// Fallback: if still nothing found, return the last log entry with data (as a single element array).
		if ( empty( $responses ) ) {
			$last_log = end( $logs );
			if ( ! empty( $last_log['data'] ) ) {
				$responses[] = $last_log['data'];
			}
		}

		return $responses;
	}

	/**
	 * Extract response data from logs.
	 *
	 * @param array $logs Captured logs.
	 * @return array Response data.
	 */
	private function extract_response_from_logs( $logs ) {
		// First, try to find a log entry with "Response" in the message.
		foreach ( $logs as $log ) {
			if ( strpos( $log['message'], 'Response' ) !== false ) {
				return $log['data'];
			}
		}

		// If no specific "Response" entry is found, look for any entry with response-like data.
		foreach ( $logs as $log ) {
			if ( isset( $log['data']['body'] ) && isset( $log['data']['response'] ) ) {
				return $log['data'];
			}
		}

		// If still nothing found, return the last log entry with data.
		$last_log = end( $logs );
		if ( ! empty( $last_log['data'] ) ) {
			return $last_log['data'];
		}

		return array();
	}
}
