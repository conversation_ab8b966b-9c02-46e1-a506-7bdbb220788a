<?php
/**
 * Test data for WooCommerce USPS Shipping tests.
 *
 * This file contains common test data used across all test cases
 * for both legacy and REST API implementations.
 *
 * @package WC_Shipping_USPS
 */

/**
 * Returns test data for USPS shipping tests.
 *
 * @return array Test data.
 */
function wc_usps_get_test_data() {
	return array(
		// From addresses (domestic).
		'from_addresses'             => array(
			'domestic_1' => array(
				'address_1' => '1600 Pennsylvania Avenue NW',
				'address_2' => '',
				'city'      => 'Washington',
				'state'     => 'DC',
				'postcode'  => '20500',
				'country'   => 'US',
			),
			'domestic_2' => array(
				'address_1' => '350 Fifth Avenue',
				'address_2' => 'Suite 5000',
				'city'      => 'New York',
				'state'     => 'NY',
				'postcode'  => '10118',
				'country'   => 'US',
			),
			'domestic_3' => array(
				'address_1' => '1 Infinite Loop',
				'address_2' => '',
				'city'      => 'Cupertino',
				'state'     => 'CA',
				'postcode'  => '95014',
				'country'   => 'US',
			),
		),

		// To addresses (domestic).
		'to_addresses_domestic'      => array(
			'residential_1' => array(
				'address_1'   => '123 Main Street',
				'address_2'   => 'Apt 4B',
				'city'        => 'Chicago',
				'state'       => 'IL',
				'postcode'    => '60601',
				'country'     => 'US',
				'residential' => true,
			),
			'commercial_1'  => array(
				'address_1'   => '555 Market Street',
				'address_2'   => 'Floor 10',
				'city'        => 'San Francisco',
				'state'       => 'CA',
				'postcode'    => '94105',
				'country'     => 'US',
				'residential' => false,
			),
			'po_box_1'      => array(
				'address_1'   => 'PO Box 12345',
				'address_2'   => '',
				'city'        => 'Seattle',
				'state'       => 'WA',
				'postcode'    => '98101',
				'country'     => 'US',
				'residential' => false,
			),
		),

		// To addresses (international).
		'to_addresses_international' => array(
			'canada_1'    => array(
				'address_1' => '150 Elgin Street',
				'address_2' => '',
				'city'      => 'Ottawa',
				'state'     => 'ON',
				'postcode'  => 'K2P 1L4',
				'country'   => 'CA',
			),
			'uk_1'        => array(
				'address_1' => '10 Downing Street',
				'address_2' => '',
				'city'      => 'London',
				'state'     => '',
				'postcode'  => 'SW1A 2AA',
				'country'   => 'GB',
			),
			'australia_1' => array(
				'address_1' => '1 Parliament Drive',
				'address_2' => '',
				'city'      => 'Canberra',
				'state'     => 'ACT',
				'postcode'  => '2600',
				'country'   => 'AU',
			),
		),

		// Products with different weights and dimensions.
		'products'                   => array(
			'small_light'   => array(
				'name'   => 'Small Light Product',
				'weight' => 0.5, // lbs
				'length' => 6,   // inches
				'width'  => 4,   // inches
				'height' => 2,   // inches
				'value'  => 19.99,
			),
			'medium_weight' => array(
				'name'   => 'Medium Weight Product',
				'weight' => 3,   // lbs
				'length' => 10,  // inches
				'width'  => 8,   // inches
				'height' => 6,   // inches
				'value'  => 49.99,
			),
			'large_heavy'   => array(
				'name'   => 'Large Heavy Product',
				'weight' => 15,  // lbs
				'length' => 24,  // inches
				'width'  => 18,  // inches
				'height' => 12,  // inches
				'value'  => 199.99,
			),
			'flat_envelope' => array(
				'name'   => 'Document Envelope',
				'weight' => 0.2, // lbs
				'length' => 12,  // inches
				'width'  => 9,   // inches
				'height' => 0.25, // inches
				'value'  => 9.99,
			),
			'media_item'    => array(
				'name'   => 'Media Item (Book)',
				'weight' => 2,   // lbs
				'length' => 9,   // inches
				'width'  => 6,   // inches
				'height' => 1,   // inches
				'value'  => 24.99,
			),
		),

		// Packages with different characteristics.
		'packages'                   => array(
			'small_package'        => array(
				'length'    => 8,   // inches
				'width'     => 6,   // inches
				'height'    => 4,   // inches
				'weight'    => 1,   // lbs
				'value'     => 25.00,
				'signature' => false,
				'insurance' => false,
			),
			'medium_package'       => array(
				'length'    => 12,  // inches
				'width'     => 10,  // inches
				'height'    => 8,   // inches
				'weight'    => 5,   // lbs
				'value'     => 75.00,
				'signature' => true,
				'insurance' => false,
			),
			'large_package'        => array(
				'length'    => 20,  // inches
				'width'     => 16,  // inches
				'height'    => 12,  // inches
				'weight'    => 20,  // lbs
				'value'     => 250.00,
				'signature' => true,
				'insurance' => true,
			),
			'flat_rate_envelope'   => array(
				'length'    => 12.5, // inches
				'width'     => 9.5,  // inches
				'height'    => 0.5,  // inches
				'weight'    => 0.5,  // lbs
				'value'     => 15.00,
				'signature' => false,
				'insurance' => false,
				'box_id'    => 'FLAT RATE ENVELOPE',
			),
			'flat_rate_medium_box' => array(
				'length'    => 13.625, // inches
				'width'     => 11.875, // inches
				'height'    => 3.375,  // inches
				'weight'    => 3,      // lbs
				'value'     => 50.00,
				'signature' => false,
				'insurance' => false,
				'box_id'    => 'MD FLAT RATE BOX',
			),
		),

		// Service types to test.
		'service_types'              => array(
			'domestic'      => array(
				'D_FIRST_CLASS',
				'D_GROUND_ADVANTAGE',
				'D_EXPRESS_MAIL',
				'D_MEDIA_MAIL',
				'D_LIBRARY_MAIL',
				'D_PRIORITY_MAIL',
			),
			'international' => array(
				'I_EXPRESS_MAIL',
				'I_PRIORITY_MAIL',
				'I_GLOBAL_EXPRESS',
				'I_FIRST_CLASS',
				'I_POSTCARDS',
			),
		),

		// Test scenarios combining different elements.
		'test_scenarios'             => array(
			'domestic_small_package'                    => array(
				'from_address'  => 'domestic_1',
				'to_address'    => 'residential_1',
				'products'      => array( 'small_light' ),
				'service_types' => array( 'D_FIRST_CLASS', 'D_GROUND_ADVANTAGE' ),
			),
			'domestic_medium_package'                   => array(
				'from_address'  => 'domestic_2',
				'to_address'    => 'commercial_1',
				'products'      => array( 'medium_weight' ),
				'service_types' => array( 'D_PRIORITY_MAIL', 'D_GROUND_ADVANTAGE' ),
			),
			'domestic_large_package'                    => array(
				'from_address'  => 'domestic_3',
				'to_address'    => 'residential_1',
				'products'      => array( 'large_heavy' ),
				'service_types' => array( 'D_PRIORITY_MAIL', 'D_EXPRESS_MAIL' ),
			),
			'domestic_media_package'                    => array(
				'from_address'  => 'domestic_1',
				'to_address'    => 'po_box_1',
				'products'      => array( 'media_item' ),
				'service_types' => array( 'D_MEDIA_MAIL', 'D_PRIORITY_MAIL' ),
			),
			'international_small_package'               => array(
				'from_address'  => 'domestic_1',
				'to_address'    => 'canada_1',
				'products'      => array( 'small_light' ),
				'service_types' => array( 'I_FIRST_CLASS', 'I_PRIORITY_MAIL' ),
			),
			'international_medium_package'              => array(
				'from_address'  => 'domestic_2',
				'to_address'    => 'uk_1',
				'products'      => array( 'medium_weight' ),
				'service_types' => array( 'I_PRIORITY_MAIL', 'I_EXPRESS_MAIL' ),
			),

			// New scenarios for expanded parity coverage.
			'domestic_flat_envelope_to_pobox'           => array(
				'from_address'  => 'domestic_2',
				'to_address'    => 'po_box_1',
				'products'      => array( 'flat_envelope' ),
				'service_types' => array( 'D_FIRST_CLASS', 'D_PRIORITY_MAIL' ),
			),
			'domestic_mixed_small_and_medium'           => array(
				'from_address'  => 'domestic_3',
				'to_address'    => 'residential_1',
				'products'      => array( 'small_light', 'medium_weight' ),
				'service_types' => array( 'D_PRIORITY_MAIL', 'D_GROUND_ADVANTAGE' ),
			),
			'domestic_two_small_items_commercial'       => array(
				'from_address'  => 'domestic_1',
				'to_address'    => 'commercial_1',
				'products'      => array( 'small_light', 'small_light' ),
				'service_types' => array( 'D_FIRST_CLASS', 'D_GROUND_ADVANTAGE' ),
			),
			'international_canada_medium_weight'        => array(
				'from_address'  => 'domestic_3',
				'to_address'    => 'canada_1',
				'products'      => array( 'medium_weight' ),
				'service_types' => array( 'I_PRIORITY_MAIL', 'I_FIRST_CLASS' ),
			),
			'international_uk_envelope'                 => array(
				'from_address'  => 'domestic_1',
				'to_address'    => 'uk_1',
				'products'      => array( 'flat_envelope' ),
				'service_types' => array( 'I_FIRST_CLASS', 'I_PRIORITY_MAIL' ),
			),
			'international_australia_large_heavy'       => array(
				'from_address'  => 'domestic_2',
				'to_address'    => 'australia_1',
				'products'      => array( 'large_heavy' ),
				'service_types' => array( 'I_PRIORITY_MAIL', 'I_EXPRESS_MAIL' ),
			),
			'domestic_medium_to_pobox'                  => array(
				'from_address'  => 'domestic_2',
				'to_address'    => 'po_box_1',
				'products'      => array( 'medium_weight' ),
				'service_types' => array( 'D_PRIORITY_MAIL', 'D_GROUND_ADVANTAGE' ),
			),
			'domestic_large_to_commercial'              => array(
				'from_address'  => 'domestic_1',
				'to_address'    => 'commercial_1',
				'products'      => array( 'large_heavy' ),
				'service_types' => array( 'D_PRIORITY_MAIL', 'D_EXPRESS_MAIL' ),
			),
			// Additional scenarios for expanded parity coverage.
			'domestic_small_to_pobox'                   => array(
				'from_address'  => 'domestic_1',
				'to_address'    => 'po_box_1',
				'products'      => array( 'small_light' ),
				'service_types' => array( 'D_FIRST_CLASS', 'D_GROUND_ADVANTAGE' ),
			),
			'domestic_two_medium_items_residential'     => array(
				'from_address'  => 'domestic_2',
				'to_address'    => 'residential_1',
				'products'      => array( 'medium_weight', 'medium_weight' ),
				'service_types' => array( 'D_PRIORITY_MAIL', 'D_GROUND_ADVANTAGE' ),
			),
			'domestic_flat_and_small_mixed_residential' => array(
				'from_address'  => 'domestic_3',
				'to_address'    => 'residential_1',
				'products'      => array( 'flat_envelope', 'small_light' ),
				'service_types' => array( 'D_FIRST_CLASS', 'D_GROUND_ADVANTAGE' ),
			),
			'international_canada_envelope'             => array(
				'from_address'  => 'domestic_1',
				'to_address'    => 'canada_1',
				'products'      => array( 'flat_envelope' ),
				'service_types' => array( 'I_FIRST_CLASS', 'I_PRIORITY_MAIL' ),
			),
			'international_australia_small_package'     => array(
				'from_address'  => 'domestic_3',
				'to_address'    => 'australia_1',
				'products'      => array( 'small_light' ),
				'service_types' => array( 'I_FIRST_CLASS', 'I_PRIORITY_MAIL' ),
			),
			'domestic_large_to_pobox'                   => array(
				'from_address'  => 'domestic_1',
				'to_address'    => 'po_box_1',
				'products'      => array( 'large_heavy' ),
				'service_types' => array( 'D_PRIORITY_MAIL', 'D_EXPRESS_MAIL' ),
			),
		),
	);
}
