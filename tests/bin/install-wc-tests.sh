#!/usr/bin/env bash
set -e

trap 'echo "🟥 An error occurred. Exiting..."; exit 1' ERR

if [ $# -lt 3 ]; then
	echo -e "Using defaults, run command like following to customize: $0 <db-name> <db-user> <db-pass> [db-host] [wp-version] [wc-version]"
fi

docker compose -f tests/docker-compose.yml up -d

DB_NAME=${1-test_wp}
DB_USER=${2-test_wp}
DB_PASS=${3-test_wp}
DB_HOST=${4-127.0.0.1:4477}
WP_VERSION=${5-latest}
WC_VERSION=${6-"9.0.0"}

# ascii art for a nice title

echo "******************************"
echo "Setting up WordPress $WP_VERSION and WooCommerce $WC_VERSION test environment"
echo "ℹ️ DB_NAME: $DB_NAME"
echo "ℹ️ DB_USER: $DB_USER"
echo "ℹ️ DB_PASS: $DB_PASS"
echo "ℹ️ DB_HOST: $DB_HOST"

export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion

download() {
	if [ `which curl` ]; then
		curl -s "$1" > "$2";
	elif [ `which wget` ]; then
		wget -nv -O "$2" "$1"
	fi
}

if [ -z $WC_VERSION ] || [ $WC_VERSION == 'latest' ]; then
	echo "WooCommerce version not provided. Fetching latest tag from org..."
	download https://api.wordpress.org/plugins/info/1.0/woocommerce.json ~/woocommerce.json
	WC_VERSION=$(grep -o '"version":"[^"]*' ~/woocommerce.json | sed 's/"version":"//')
	echo "WooCommerce version fetched: $WC_VERSION"
fi

install_wc() {
	echo
	echo "=============================="
	echo "Installing WooCommerce $WC_VERSION"
	echo "=============================="
	echo "🟢 Cloning WooCommerce from GitHub into /tmp/woocommerce"
	git clone --depth=1 --branch=$WC_VERSION https://github.com/woocommerce/woocommerce.git /tmp/woocommerce
	cd /tmp/woocommerce/plugins/woocommerce
	# if nvm is not installed, don't call it
	if ! command -v nvm &> /dev/null
	then
		echo
		echo "⚠️ nvm could not be found, skipping nvm install"
		echo
	else
		nvm install
	fi
	echo
	echo "🟢 Running WooCommerce's pnpm install"
	echo
	pnpm install
	echo
	echo "🟢 Running WooCommerce's composer install"
	echo
	composer i

	echo
	echo "🟢 Running WooCommerce's generate-feature-config"
	echo
	php bin/generate-feature-config.php

	echo
	echo "🟢 Done unzipping WooCommerce"
	echo
}

install_wp() {
	echo
	echo "=============================="
	echo "Installing WordPress via WooCommerce install script"
	echo "=============================="
	bash /tmp/woocommerce/plugins/woocommerce/tests/bin/install.sh $DB_NAME $DB_USER "$DB_PASS" $DB_HOST $WP_VERSION
	echo
	echo "🟢 Done unzipping WordPress"
}

if [ -z "$TMPDIR" ]; then
	echo
	echo "⚠️ Variable \$TMPDIR is not set. Unable to remove current wordpress-tests-lib directory if needed - this might cause problems."
	echo
else
	echo "Removing directories wordpress and wordpress-tests-lib from \$TMPDIR ($TMPDIR)..."
	rm -rf $TMPDIR/wordpress $TMPDIR/wordpress-tests-lib
fi

rm -rf /tmp/woocommerce && install_wc && install_wp
