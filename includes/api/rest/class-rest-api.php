<?php
/**
 * USPS Legacy API class file.
 *
 * @package WC_Shipping_USPS
 */

namespace WooCommerce\USPS\API;

use WC_Product;
use WC_Shipping_USPS;
use WC_Shipping_USPS_Admin;
use WooCommerce\BoxPacker\Abstract_Packer;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\BoxPacker\WC_Boxpack;

require_once WC_USPS_API_DIR . '/class-abstract-api.php';

// phpcs:disable WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase --- USPS API provides an object with camelCase properties and method

/**
 * USPS REST API class.
 */
class REST_API extends Abstract_API {

	/**
	 * Standard rate requests.
	 *
	 * @var array
	 */
	private array $standard_rate_requests = array();

	/**
	 * Priority flat rate requests.
	 *
	 * @var array
	 */
	private array $priority_flat_rate_requests = array();

	/**
	 * Express flat rate requests.
	 *
	 * @var array
	 */
	private array $express_flat_rate_requests = array();

	/**
	 * Current package being processed.
	 *
	 * @var array
	 */
	private array $package = array();

	/**
	 * Whether the current shipment is domestic.
	 *
	 * @var bool
	 */
	private bool $is_domestic_shipment = true;

	/**
	 * Class constructor.
	 *
	 * @param WC_Shipping_USPS $shipping_method USPS shipping method object.
	 */
	public function __construct( WC_Shipping_USPS $shipping_method ) {
		$this->shipping_method = $shipping_method;
	}

	/**
	 * Calculate shipping cost.
	 *
	 * This method processes each package individually, sending a separate API request
	 * for each package regardless of its size.
	 *
	 * @since   4.4.7
	 * @version 4.4.7
	 *
	 * @param array $package Package to ship.
	 *
	 * @return void
	 */
	public function calculate_shipping( $package ) {
		if ( empty( $package['destination']['postcode'] ) || empty( $package['destination']['country'] ) ) {
			$this->shipping_method->debug( __( 'No destination postcode provided. USPS rates cannot be calculated.', 'woocommerce-shipping-usps' ) );

			return;
		}

		$this->run_pre_calculation_setup( $package );

		$this->shipping_method->debug( '------------------Start Shipping Calculation------------------' );

		// Calculate rates for each rate type.
		$this->maybe_calculate_standard_rates();
		$this->maybe_calculate_priority_flat_rates();
		$this->maybe_calculate_express_flat_rates();

		// Store the raw found rates, so we can pass to the filter later.
		$this->shipping_method->raw_found_rates = $this->shipping_method->found_rates;

		// Validate the found rates.
		$this->validate_found_rates();

		// Add rates.
		if ( ! empty( $this->shipping_method->found_rates ) ) {
			$this->check_found_rates();
		} elseif ( $this->shipping_method->fallback ) {
			$this->shipping_method->add_rate(
				array(
					'id'    => $this->shipping_method->get_rate_id() . '_fallback',
					'label' => $this->shipping_method->title,
					'cost'  => $this->shipping_method->fallback,
					'sort'  => 0,
				)
			);
		} else {
			$this->shipping_method->debug( __( 'Warning: The fallback amount is not set.', 'woocommerce-shipping-usps' ) );
		}

		$this->shipping_method->debug( '------------------End Shipping Calculation------------------' );
	}

	/**
	 * Perform a request to check REST API credentials.
	 */
	public function validate_credentials() {
		return $this->shipping_method->oauth->is_authenticated();
	}

	/**
	 * Get Standard Rate API requests.
	 *
	 * @return array
	 */
	private function get_standard_rate_api_requests(): array {
		if ( $this->shipping_method->is_package_overweight( $this->package ) ) {
			return array();
		}

		// Use the selected packing method from the instance settings.
		switch ( $this->shipping_method->packing_method ) {
			case 'box_packing':
				$requests = $this->box_shipping();
				break;
			case 'weight_based':
				$requests = $this->weight_based_shipping();
				break;
			case 'per_item':
			default:
				$requests = $this->per_item_shipping();
				break;
		}

		return $requests;
	}

	/**
	 * Process API requests and update found rates.
	 *
	 * @param array $requests Requests to process.
	 *
	 * @return void
	 */
	private function process_api_requests_and_update_rates( array $requests ): void {
		if ( empty( $requests ) ) {
			return;
		}

		// Get API responses for all requests.
		$api_responses = $this->get_api_responses( $requests );
		if ( empty( $api_responses ) ) {
			return;
		}

		// Parse the rates from the API responses.
		$rates = $this->parse_rates_from_api_responses( $api_responses );

		// Merge with existing rates.
		$this->shipping_method->found_rates = array_merge( $this->shipping_method->found_rates, $rates );
	}

	/**
	 * Maybe calculate standard rates for the package.
	 *
	 * @return void
	 */
	private function maybe_calculate_standard_rates(): void {
		if ( ! $this->shipping_method->enable_standard_services ) {
			return;
		}

		// Store the requests in the class property for later validation.
		$this->standard_rate_requests = $this->get_standard_rate_api_requests();

		// Process the requests and update rates.
		$this->process_api_requests_and_update_rates( $this->standard_rate_requests );
	}

	/**
	 * Maybe calculate priority flat rates for the package.
	 *
	 * @return void
	 */
	private function maybe_calculate_priority_flat_rates(): void {
		if ( 'yes' !== $this->shipping_method->enable_flat_rate_boxes && 'priority' !== $this->shipping_method->enable_flat_rate_boxes ) {
			return;
		}

		// Store the requests in the class property for later validation.
		$this->priority_flat_rate_requests = $this->get_flat_rate_api_requests( 'priority' );

		// Process the requests and update rates.
		$this->process_api_requests_and_update_rates( $this->priority_flat_rate_requests );
	}

	/**
	 * Maybe calculate express flat rates for the package.
	 *
	 * @return void
	 */
	private function maybe_calculate_express_flat_rates(): void {
		if ( 'yes' !== $this->shipping_method->enable_flat_rate_boxes && 'express' !== $this->shipping_method->enable_flat_rate_boxes ) {
			return;
		}

		// Store the requests in the class property for later validation.
		$this->express_flat_rate_requests = $this->get_flat_rate_api_requests( 'express' );

		// Process the requests and update rates.
		$this->process_api_requests_and_update_rates( $this->express_flat_rate_requests );
	}

	/**
	 * Validate found rates to ensure they have enough packages.
	 * Also adds unpacked item costs to rates if needed.
	 *
	 * @return void
	 */
	private function validate_found_rates(): void {
		if ( ! $this->shipping_method->found_rates ) {
			return;
		}

		$rate_id_prefix = $this->shipping_method->get_rate_id() . ':';

		foreach ( $this->shipping_method->found_rates as $key => $value ) {
			// Handle new flat-rate keys built as "{rate_id}:flatrate:{service}:{box_id}".
			if ( 0 === strpos( $key, $rate_id_prefix . 'flatrate:express:' ) ) {
				$expected_packages = isset( $this->express_flat_rate_requests ) ? count( $this->express_flat_rate_requests ) : 0;
				if ( $expected_packages && $value['packages'] < $expected_packages ) {
					$this->shipping_method->debug( "Unsetting {$key} - too few packages." );
					unset( $this->shipping_method->found_rates[ $key ] );
				}
			} elseif ( 0 === strpos( $key, $rate_id_prefix . 'flatrate:priority:' ) ) {
				$expected_packages = isset( $this->priority_flat_rate_requests ) ? count( $this->priority_flat_rate_requests ) : 0;
				if ( $expected_packages && $value['packages'] < $expected_packages ) {
					$this->shipping_method->debug( "Unsetting {$key} - too few packages." );
					unset( $this->shipping_method->found_rates[ $key ] );
				}
			} elseif ( isset( $this->standard_rate_requests ) ) {
				// Standard (non-flat-rate) rates must match the number of standard requests.
				if ( $value['packages'] < count( $this->standard_rate_requests ) ) {
					$this->shipping_method->debug( "Unsetting {$key} - too few packages." );
					unset( $this->shipping_method->found_rates[ $key ] );
				}
			}

			if ( $this->shipping_method->unpacked_item_costs && ! empty( $this->shipping_method->found_rates[ $key ] ) ) {
				// translators: %s is a USPS rate key.
				$this->shipping_method->debug( sprintf( __( 'Adding unpacked item costs to rate %s', 'woocommerce-shipping-usps' ), $key ) );
				$this->shipping_method->found_rates[ $key ]['cost'] += $this->shipping_method->unpacked_item_costs;
			}
		}
	}

	/**
	 * Generate XML requests using box packing method.
	 *
	 * @version 4.4.7
	 *
	 * @return array Array of JSON requests.
	 */
	private function box_shipping(): array {

		$requests = array();

		$boxpacker = ( new WC_Boxpack( 'in', 'lbs', $this->shipping_method->box_packer_library ) )->get_packer();

		// Define boxes.
		foreach ( $this->shipping_method->boxes as $key => $box ) {
			$newbox = $boxpacker->add_box( $box['outer_length'], $box['outer_width'], $box['outer_height'], $box['box_weight'] );
			$newbox->set_id( isset( $box['name'] ) ? $box['name'] : $key );
			$newbox->set_inner_dimensions( $box['inner_length'], $box['inner_width'], $box['inner_height'] );
			if ( $box['max_weight'] ) {
				$newbox->set_max_weight( $box['max_weight'] );
			}
			if ( $box['is_letter'] ) {
				$newbox->set_type( 'envelope' );
			}
		}

		if ( ! is_array( $this->package['contents'] ) ) {
			return $requests;
		}

		// Add items.
		foreach ( $this->package['contents'] as $values ) {
			if ( ! $values['data']->needs_shipping() ) {
				continue;
			}
			if ( $values['data']->get_length() && $values['data']->get_height() && $values['data']->get_width() ) {
				$dimensions = array(
					wc_get_dimension( $values['data']->get_length(), 'in' ),
					wc_get_dimension( $values['data']->get_width(), 'in' ),
					wc_get_dimension( $values['data']->get_height(), 'in' ),
				);
			} else {
				// translators: %1$d is a product ID and %2$s is a default product dimension unit.
				$this->shipping_method->debug( sprintf( __( 'Product #%1$d is missing dimensions! Using %2$s.', 'woocommerce-shipping-usps' ), $values['data']->get_id(), implode( 'x', $this->shipping_method->get_default_product_dimensions() ) ) );
				$dimensions = $this->shipping_method->get_default_product_dimensions();
			}

			$weight = $this->shipping_method->get_product_weight( $values['data'] );

			$declared_value = $values['data']->get_meta( WC_Shipping_USPS_Admin::META_KEY_DECLARED_VALUE );
			if ( '' === $declared_value ) {
				$declared_value = $values['data']->get_price();
			}
			for ( $i = 0; $i < $values['quantity']; $i++ ) {
				$boxpacker->add_item(
					$dimensions[0],
					$dimensions[1],
					$dimensions[2],
					$weight,
					$declared_value
				);
			}
		}
		/**
		 * Allow boxpack to be overriden by devs.
		 *
		 * @see   https://github.com/woocommerce/woocommerce-shipping-usps/issues/155
		 *
		 * @var Abstract_Packer $boxpacker Boxpacker object.
		 *
		 * @since 4.4.12
		 */
		$boxpacker = apply_filters( 'woocommerce_shipping_usps_boxpack_before_pack', $boxpacker );

		// Pack it.
		$boxpacker->pack();

		// Get packages.
		$box_packages = $boxpacker->get_packages();
		foreach ( $box_packages as $key => $box_package ) {
			if ( true === $box_package->unpacked ) {
				$this->shipping_method->debug( 'Unpacked Item' );

				switch ( $this->shipping_method->unpacked_item_handling ) {
					case 'fallback':
						// No request, just a fallback, if the fallback amount is set.
						if ( $this->shipping_method->fallback ) {
							$this->shipping_method->unpacked_item_costs += (float) $this->shipping_method->fallback;
						} else {
							$this->shipping_method->debug( __( 'Warning: The fallback amount is not set.', 'woocommerce-shipping-usps' ) );
						}
						continue 2;
					case 'ignore':
						// No request.
						continue 2;
					case 'abort':
						// No requests!
						return array();
				}
			} else {
				$this->shipping_method->debug( 'Packed ' . $box_package->id );
			}

			$weight     = $box_package->weight;
			$dimensions = array( $box_package->length, $box_package->width, $box_package->height );
			rsort( $dimensions, SORT_NUMERIC );

			$package_id = $this->generate_package_id( $key, 1, $dimensions[2], $dimensions[1], $dimensions[0], $weight, 'api' );

			$request = $this->build_api_request( $this->package['destination'], $dimensions, $weight, $box_package->value );

			$requests[ $package_id ] = $request;
		}

		return $requests;
	}

	/**
	 * Generate shipping request for weights only.
	 *
	 * @return array
	 */
	private function weight_based_shipping(): array {
		$requests = array();

		// Add requests for items.
		foreach ( $this->package['contents'] as $item_id => $values ) {

			$product = $values['data'];

			if ( ! $product instanceof WC_Product ) {
				continue;
			}

			if ( ! $product->needs_shipping() ) {
				// translators: %d is a product ID.
				$this->shipping_method->debug( sprintf( __( 'Product #%d is virtual. Skipping.', 'woocommerce-shipping-usps' ), $product->get_id() ) );
				continue;
			}

			$weight = $this->shipping_method->get_product_weight( $product );

			if ( $product->get_length() && $product->get_height() && $product->get_width() ) {
				$dimensions = array(
					wc_get_dimension( $product->get_length(), 'in' ),
					wc_get_dimension( $product->get_width(), 'in' ),
					wc_get_dimension( $product->get_height(), 'in' ),
				);
			} else {
				$dimensions = $this->shipping_method->get_default_product_dimensions();

				// Sort the dimensions so the largest dimension is key 0 and smallest is key 2.
				rsort( $dimensions, SORT_NUMERIC );

				// translators: %1$d is a product ID and %2$s is a default product dimension unit.
				$this->shipping_method->debug( sprintf( __( 'Product #%1$d is missing dimensions! Using %2$s.', 'woocommerce-shipping-usps' ), $product->get_id(), implode( 'x', $dimensions ) ) );
			}

			$declared_value = $product->get_meta( WC_Shipping_USPS_Admin::META_KEY_DECLARED_VALUE );
			if ( '' === $declared_value ) {
				$declared_value = $product->get_price();
			}

			// Sort the dimensions so the largest dimension is key 0 and smallest is key 2.
			rsort( $dimensions, SORT_NUMERIC );

			$package_id = $this->generate_package_id( $item_id, $values['quantity'], $dimensions[0], $dimensions[1], $dimensions[2], $weight, 'api' );
			$request    = $this->build_api_request( $this->package['destination'], $dimensions, $weight, $declared_value );

			$requests[ $package_id ] = $request;
		}

		return $requests;
	}

	/**
	 * Per item shipping.
	 *
	 * @return array
	 */
	private function per_item_shipping(): array {
		$requests = array();

		if ( ! is_array( $this->package['contents'] ) ) {
			return $requests;
		}

		// Get weight of order.
		foreach ( $this->package['contents'] as $item_id => $values ) {
			$product = $values['data'];

			if ( ! $product instanceof WC_Product ) {
				continue;
			}

			if ( ! $product->needs_shipping() ) {
				$this->shipping_method->debug( sprintf( __( 'Product # is virtual. Skipping.', 'woocommerce-shipping-usps' ), $product->get_id() ) );
				continue;
			}

			$weight = $this->shipping_method->get_product_weight( $product );

			if ( $product->get_length() && $product->get_height() && $product->get_width() ) {

				$dimensions = array(
					wc_get_dimension( $product->get_length(), 'in' ),
					wc_get_dimension( $product->get_height(), 'in' ),
					wc_get_dimension( $product->get_width(), 'in' ),
				);

				sort( $dimensions, SORT_NUMERIC );
			} else {
				$dimensions = array( 0, 0, 0 );
			}

			$declared_value = $product->get_meta( WC_Shipping_USPS_Admin::META_KEY_DECLARED_VALUE );
			if ( '' === $declared_value ) {
				$declared_value = $product->get_price();
			}

			$package_id = $this->generate_package_id( $item_id, $values['quantity'], $dimensions[2], $dimensions[1], $dimensions[0], $weight, 'api' );
			$request    = $this->build_api_request( $this->package['destination'], $dimensions, $weight, $declared_value );

			$requests[ $package_id ] = $request;
		}

		return $requests;
	}

	/**
	 * Build a USPS API request array for the package.
	 *
	 * @param array  $destination    Destination of the package.
	 * @param array  $dimensions     Package dimensions.
	 * @param float  $weight         Package weight.
	 * @param float  $declared_value Package Value.
	 * @param string $mail_class     Mail class.
	 *
	 * @return array
	 */
	public function build_api_request( array $destination, array $dimensions, float $weight, float $declared_value, string $mail_class = 'ALL' ): array {
		// Sort dimensions so that the largest dimension is key 0 and smallest is key 2.
		rsort( $dimensions, SORT_NUMERIC );

		$request = array(
			'pricingOptions'     => array(
				array(
					'priceType' => 'ONLINE' === $this->shipping_method->shippingrates ? 'COMMERCIAL' : 'RETAIL',
				),
			),
			'originZIPCode'      => $this->shipping_method->origin,
			'packageDescription' => array(
				'weight'       => $weight,
				'length'       => (float) $dimensions[0],
				'width'        => (float) $dimensions[1],
				'height'       => (float) $dimensions[2],
				'girth'        => (float) ( 2 * ( $dimensions[1] + $dimensions[2] ) ),
				'packageValue' => $declared_value,
				'mailClass'    => $mail_class,
				'mailingDate'  => wp_date( 'Y-m-d', strtotime( 'tomorrow' ) ),
			),
		);

		// Depending on the destination, we need to add different fields.
		if ( $this->is_domestic_shipment ) {
			$request['destinationZIPCode'] = $destination['postcode'];
		} else {
			$request['foreignPostalCode']      = $destination['postcode'];
			$request['destinationCountryCode'] = $destination['country'];
		}

		return $request;
	}

	/**
	 * Check if a service code represents a domestic flat rate service.
	 *
	 * @param string $service_code The service code to check.
	 *
	 * @return bool True if the service is domestic, false otherwise.
	 */
	private function is_domestic_flat_rate_service( string $service_code ): bool {
		return 'd' === substr( $service_code, 0, 1 );
	}

	/**
	 * Check if a service ID represents a domestic service.
	 *
	 * @param string $service_id The service ID to check.
	 *
	 * @return bool True if the service is domestic, false if it's international.
	 */
	private function is_domestic_service( string $service_id ): bool {
		return 0 === strpos( $service_id, 'D_' );
	}

	/**
	 * Map USPS REST 4-character SKU prefixes to legacy numeric service IDs.
	 *
	 * This keeps backward compatibility with merchants’ saved settings, which
	 * are keyed by legacy numeric IDs from data-services.php.
	 *
	 * @param string $sku_prefix The first 4 characters of the REST SKU.
	 * @return string Legacy numeric service ID (as string) or the original code if unmapped.
	 */
	private function map_rest_sku_to_legacy_code( string $sku_prefix ): string {
		$map = array(
			// Domestic.
			'DUXP' => '1058', // Ground Advantage.
			'DEXX' => '3',    // Priority Mail Express.
			'DMXX' => '6',    // Media Mail.
			'DLXX' => '7',    // Library Mail.
			'DPXX' => '1',    // Priority Mail.
			// International.
			'IEXX' => '1',    // Priority Mail Express International.
			'IPXX' => '2',    // Priority Mail International.
			'IFXP' => '15',   // First-Class Package International Service.
		);

		return isset( $map[ $sku_prefix ] ) ? $map[ $sku_prefix ] : $sku_prefix;
	}

	/**
	 * Get the flat rate box name from the service code.
	 *
	 * @param string $service_code The service code to get the box name for.
	 *
	 * @return string The flat rate box name or empty string if not found.
	 */
	public function get_flat_rate_box_name_from_service_code( string $service_code ): string {
		if ( empty( $this->shipping_method->flat_rate_boxes ) || ! isset( $this->shipping_method->flat_rate_boxes[ $service_code ] ) ) {
			return '';
		}

		return $this->shipping_method->flat_rate_boxes[ $service_code ]['name'];
	}

	/**
	 * Get the expected USPS rateIndicator for a flat rate box service ID.
	 *
	 * We prefer using mailClass + rateIndicator to match rates for flat rate boxes.
	 * If a service ID is not mapped, return an empty string.
	 *
	 * @param string $service_id Flat rate box service ID (e.g., d16, d29, d44, d17, d17b, d28, d22, d13, d30, d63).
	 * @return string Expected rateIndicator or empty string if unknown.
	 */
	private function get_rate_indicator_for_box( string $service_id ): string {
		if (
			! empty( $this->shipping_method->flat_rate_boxes )
			&& isset( $this->shipping_method->flat_rate_boxes[ $service_id ] )
			&& isset( $this->shipping_method->flat_rate_boxes[ $service_id ]['rate_indicator'] )
			&& '' !== (string) $this->shipping_method->flat_rate_boxes[ $service_id ]['rate_indicator']
		) {
			return strtoupper( (string) $this->shipping_method->flat_rate_boxes[ $service_id ]['rate_indicator'] );
		}

		return '';
	}

	/**
	 * Add flat rate boxes to the boxpacker object.
	 *
	 * @param Abstract_Packer $boxpacker         BoxPacker object.
	 * @param string          $flat_rate_service 'priority' or 'express'.
	 *
	 * @return void
	 */
	private function add_flat_rate_boxes_to_boxpacker( Abstract_Packer $boxpacker, string $flat_rate_service ): void {

		if ( empty( $this->shipping_method->flat_rate_boxes ) ) {
			return;
		}

		$added_boxes = array();

		// Define boxes.
		foreach ( $this->shipping_method->flat_rate_boxes as $service_code => $box ) {

			if ( $box['service'] !== $flat_rate_service ) {
				continue;
			}

			// Only add flat rate boxes that work for the intended destination country.
			if ( $this->is_domestic_shipment !== $this->is_domestic_flat_rate_service( $service_code ) ) {
				continue;
			}

			$newbox = $boxpacker->add_box(
				$box['length'],
				$box['width'],
				$box['height'],
				$this->shipping_method->get_empty_box_weight( $service_code, $box['weight'] ),
				$box['max_weight']
			);

			$newbox->set_id( $service_code );

			if ( isset( $box['volume'] ) && method_exists( $newbox, 'set_volume' ) ) {
				$newbox->set_volume( $box['volume'] );
			}

			if ( isset( $box['type'] ) && method_exists( $newbox, 'set_type' ) ) {
				$newbox->set_type( $box['type'] );
			}

			$added_boxes[] = $service_code . ' - ' . $box['name'] . ' (' . $box['length'] . 'x' . $box['width'] . 'x' . $box['height'] . ')';
		}

		$this->shipping_method->debug( 'Calculating USPS Flat Rate with boxes: ' . implode( ', ', $added_boxes ) );
	}

	/**
	 * Generate request xml for flat rate packages.
	 *
	 * @param string $flat_rate_service 'priority' or 'express'.
	 *
	 * @return array
	 */
	private function get_flat_rate_api_requests( string $flat_rate_service ): array {

		$boxpacker = ( new WC_Boxpack( 'in', 'lbs', $this->shipping_method->box_packer_library ) )->get_packer();

		$requests = array();

		$this->add_flat_rate_boxes_to_boxpacker( $boxpacker, $flat_rate_service );

		// Add items.
		foreach ( $this->package['contents'] as $values ) {

			$product = $values['data'];

			if ( ! $product instanceof WC_Product ) {
				continue;
			}

			if ( ! $product->needs_shipping() ) {
				continue;
			}

			$dimensions = $this->shipping_method->get_product_dimensions( $product );

			$weight = $this->shipping_method->get_product_weight( $product );

			$boxpacker->add_item(
				$dimensions[2],
				$dimensions[1],
				$dimensions[0],
				$weight,
				$product->get_price(),
				array(),
				$values['quantity']
			);
		}

		// Pack it.
		$boxpacker->pack();

		// Get packages.
		$packages = $boxpacker->get_packages();

		foreach ( $packages as $key => $package ) {

			if ( true === $package->unpacked ) {
				$this->shipping_method->debug( 'Unpacked Item, can\'t fit in any ' . $flat_rate_service . ' flat rate boxes. Disabling flat rate services.' );

				return array();
			}

			$this->shipping_method->debug( 'Packed ' . $this->get_flat_rate_box_name_from_service_code( $package->id ) );

			$dimensions = array(
				$package->length,
				$package->width,
				$package->height,
			);

			sort( $dimensions, SORT_NUMERIC );

			$weight = $package->weight;

			$mail_class = 'express' === $flat_rate_service ? 'PRIORITY_MAIL_EXPRESS' : 'PRIORITY_MAIL';

			if ( ! $this->is_domestic_shipment ) {
				$mail_class = 'express' === $flat_rate_service ? 'PRIORITY_MAIL_EXPRESS_INTERNATIONAL' : 'PRIORITY_MAIL_INTERNATIONAL';
			}

			$request = $this->build_api_request(
				$this->package['destination'],
				$dimensions,
				$weight,
				$package->value,
				$mail_class
			);

			$package_id = $this->generate_package_id( $key, 1, $dimensions[2], $dimensions[1], $dimensions[0], $weight, 'flatrate', $flat_rate_service, $package->id );

			$requests[ $package_id ] = $request;
		}

		return $requests;
	}

	/**
	 * Parse responses from USPS API.
	 *
	 * @since 4.4.40
	 *
	 * @param array $api_responses Array of API responses.
	 *
	 * @return array
	 */
	private function parse_rates_from_api_responses( array $api_responses ): array {

		$rates_to_prepare = array();
		foreach ( $api_responses as $request_id => $api_response ) {
			if ( ! $api_response || ! is_string( $api_response ) ) {
				continue;
			}

			// Extract rate options from the API response.
			$rate_options = $this->parse_rate_options_from_api_response( $api_response );
			if ( empty( $rate_options ) ) {
				$this->shipping_method->debug( 'No rate options found for request ID ' . $request_id );

				continue;
			}

			// Extract rates from rate options.
			$rates_to_prepare = array_merge( $rates_to_prepare, $this->extract_rates_from_rate_options( $rate_options, $request_id ) );
		}

		$rates = array();
		foreach ( $rates_to_prepare as $rate ) {
			$rate_code = $rate['code'];
			$rate_id   = $rate['id'];
			$rate_cost = $rate['cost'];
			$meta_data = $rate['meta_data'];
			$rate_name = $rate['label'];
			$sort      = $rate['sort'];

			// Name adjustment.
			if ( ! empty( $this->shipping_method->custom_services[ $rate_code ]['name'] ) ) {
				$rate_name = $this->shipping_method->custom_services[ $rate_code ]['name'];
			}

			// Merging.
			if ( isset( $rates[ $rate_id ] ) ) {
				$rate_cost = $rate_cost + $rates[ $rate_id ]['cost'];
				$packages  = 1 + $rates[ $rate_id ]['packages'];
			} else {
				$packages = 1;
			}

			// Package metadata.
			$meta_data_value = array();
			if ( $meta_data ) {
				// translators: %s is number of rates found.
				$meta_key = sprintf( __( 'Package %s', 'woocommerce-shipping-usps' ), $packages );

				if ( isset( $rates[ $rate_id ] ) && array_key_exists( 'meta_data', $rates[ $rate_id ] ) ) {
					$meta_data_value = $rates[ $rate_id ]['meta_data'];
				}

				$meta_data_value[ $meta_key ] = $meta_data['package_description'] ?? '';

				foreach ( array( 'length', 'width', 'height', 'weight' ) as $detail ) {
					// If no value, don't save anything.
					if ( empty( $meta_data[ 'package_' . $detail ] ) ) {
						continue;
					}

					// The new value to add to the JSON string.
					$new_value = $meta_data[ 'package_' . $detail ];

					// If this rate already has metadata, decode it and add the new value to the array.
					if ( ! empty( $meta_data_value[ '_package_' . $detail ] ) ) {
						$value                                    = json_decode( $meta_data_value[ '_package_' . $detail ], true );
						$value[ $meta_key ]                       = $new_value;
						$meta_data_value[ '_package_' . $detail ] = wp_json_encode( $value );
						continue;
					}

					$meta_data_value[ '_package_' . $detail ] = wp_json_encode( array( $meta_key => $new_value ) );
				}
			}

			// Weight based shipping doesn't have package information.
			if ( 'weight_based' === $this->shipping_method->packing_method ) {
				$meta_data_value = array( 'Packing' => 'Weight Based Shipping' );
			}

			// Sort.
			if ( isset( $this->shipping_method->custom_services[ $rate_code ]['order'] ) && is_numeric( $this->shipping_method->custom_services[ $rate_code ]['order'] ) ) {
				$sort = $this->shipping_method->custom_services[ $rate_code ]['order'];
			}

			$rates[ $rate_id ] = array(
				'id'        => $rate_id,
				'label'     => $rate_name,
				'cost'      => $rate_cost,
				'sort'      => $sort,
				'packages'  => $packages,
				'meta_data' => $meta_data_value,
			);
		}

		return $rates;
	}

	/**
	 * Parse rate options from API response.
	 *
	 * @param string $api_response API response (body) JSON string.
	 *
	 * @return array Rate options.
	 *
	 * @since   4.4.7
	 * @version 4.4.8
	 */
	private function parse_rate_options_from_api_response( string $api_response ): array {

		if ( empty( $api_response ) ) {
			return array();
		}

		$response_obj = json_decode( $api_response );

		$shipping_options = (
			isset( $response_obj->pricingOptions[0]->shippingOptions ) &&
			is_array( $response_obj->pricingOptions[0]->shippingOptions )
		) ? $response_obj->pricingOptions[0]->shippingOptions : array();

		// No shipping options, return.
		if ( empty( $shipping_options ) ) {
			$this->shipping_method->debug( 'Invalid request; no rates returned' );

			return array();
		}

		$results = array();
		foreach ( $shipping_options as $shipping_option ) {
			$rate_options = (
				isset( $shipping_option->rateOptions ) &&
				is_array( $shipping_option->rateOptions )
			) ? $shipping_option->rateOptions : array();

			if ( empty( $rate_options ) ) {
				continue;
			}

			foreach ( $rate_options as $rate_option ) {
				$rates = (
					isset( $rate_option->rates ) &&
					is_array( $rate_option->rates )
				) ? $rate_option->rates : array();

				if ( empty( $rates ) ) {
					continue;
				}

				$results[] = $rate_option;
			}
		}

		return $results;
	}

	/**
	 * Set up necessary properties before starting the rates calculations.
	 *
	 * @param array $package The shipment.
	 *
	 * @return void
	 */
	public function run_pre_calculation_setup( array $package ): void {
		// Set the package property.
		$this->package = $package;

		// Set the is_domestic_shipment property.
		$this->is_domestic_shipment = $this->shipping_method->is_domestic( $this->package['destination']['country'] );

		$this->shipping_method->unpacked_item_costs = 0;

		// Initialize found_rates as an empty array.
		$this->shipping_method->found_rates = array();
	}

	/**
	 * Prepare rate.
	 *
	 * @param mixed  $rate_code Rate code.
	 * @param mixed  $rate_id   Rate ID.
	 * @param mixed  $rate_name Rate name.
	 * @param mixed  $rate_cost Cost.
	 * @param string $meta_data Rate meta data.
	 * @param int    $sort      Sort order.
	 *
	 * @return void
	 */
	private function prepare_rate( $rate_code, $rate_id, $rate_name, $rate_cost, $meta_data = '', $sort = 999 ) {
		// Name adjustment.
		if ( ! empty( $this->shipping_method->custom_services[ $rate_code ]['name'] ) ) {
			$rate_name = $this->shipping_method->custom_services[ $rate_code ]['name'];
		}

		// Merging.
		if ( isset( $this->shipping_method->found_rates[ $rate_id ] ) ) {
			$rate_cost = $rate_cost + $this->shipping_method->found_rates[ $rate_id ]['cost'];
			$packages  = 1 + $this->shipping_method->found_rates[ $rate_id ]['packages'];
		} else {
			$packages = 1;
		}

		// Package metadata.
		$meta_data_value = array();
		if ( $meta_data ) {
			// translators: %s is number of rates found.
			$meta_key = sprintf( __( 'Package %s', 'woocommerce-shipping-usps' ), $packages );

			if ( isset( $this->shipping_method->found_rates[ $rate_id ] ) && array_key_exists( 'meta_data', $this->shipping_method->found_rates[ $rate_id ] ) ) {
				$meta_data_value = $this->shipping_method->found_rates[ $rate_id ]['meta_data'];
			}

			$meta_data_value[ $meta_key ] = $meta_data['package_description'] ?? '';

			foreach ( array( 'length', 'width', 'height', 'weight' ) as $detail ) {
				// If no value, don't save anything.
				if ( empty( $meta_data[ 'package_' . $detail ] ) ) {
					continue;
				}

				// The new value to add to the JSON string.
				$new_value = $meta_data[ 'package_' . $detail ];

				// If this rate already has metadata, decode it and add the new value to the array.
				if ( ! empty( $meta_data_value[ '_package_' . $detail ] ) ) {
					$value                                    = json_decode( $meta_data_value[ '_package_' . $detail ], true );
					$value[ $meta_key ]                       = $new_value;
					$meta_data_value[ '_package_' . $detail ] = wp_json_encode( $value );
					continue;
				}

				$meta_data_value[ '_package_' . $detail ] = wp_json_encode( array( $meta_key => $new_value ) );
			}
		}

		// Weight based shipping doesn't have package information.
		if ( 'weight_based' === $this->shipping_method->packing_method ) {
			$meta_data_value = array( 'Packing' => 'Weight Based Shipping' );
		}

		// Sort.
		if ( isset( $this->shipping_method->custom_services[ $rate_code ]['order'] ) ) {
			$sort = $this->shipping_method->custom_services[ $rate_code ]['order'];
		}

		$this->shipping_method->found_rates[ $rate_id ] = array(
			'id'        => $rate_id,
			'label'     => $rate_name,
			'cost'      => $rate_cost,
			'sort'      => $sort,
			'packages'  => $packages,
			'meta_data' => $meta_data_value,
		);
	}

	/**
	 * Check found rates.
	 *
	 * @version 4.4.7
	 */
	private function check_found_rates() {
		// Only offer one Priority Mail rate (standard vs flat rate).
		$found_rates = $this->shipping_method->found_rates;

		$priority_standard_key = '';
		if ( isset( $found_rates['usps:D_PRIORITY_MAIL'] ) ) {
			$priority_standard_key = 'usps:D_PRIORITY_MAIL';
		} elseif ( isset( $found_rates['usps:I_PRIORITY_MAIL'] ) ) {
			$priority_standard_key = 'usps:I_PRIORITY_MAIL';
		}

		$priority_flat_keys = array();
		foreach ( $found_rates as $key => $_rate ) {
			if ( 0 === strpos( $key, $this->shipping_method->get_rate_id() . ':flatrate:priority:' ) ) {
				$priority_flat_keys[] = $key;
			}
		}

		if ( $priority_standard_key && ! empty( $priority_flat_keys ) ) {
			$min_flat_key  = '';
			$min_flat_cost = null;
			foreach ( $priority_flat_keys as $key ) {
				$cost = isset( $found_rates[ $key ] ) ? $found_rates[ $key ]['cost'] : null;
				if ( null === $min_flat_cost || ( null !== $cost && $cost < $min_flat_cost ) ) {
					$min_flat_cost = $cost;
					$min_flat_key  = $key;
				}
			}

			if ( null !== $min_flat_cost && $min_flat_cost < $found_rates[ $priority_standard_key ]['cost'] ) {
				$this->shipping_method->debug( 'Unsetting PRIORITY MAIL api rate - flat rate box is cheaper.' );
				unset( $this->shipping_method->found_rates[ $priority_standard_key ] );
				// Keep only the cheapest flat-rate option.
				foreach ( $priority_flat_keys as $key ) {
					if ( $key !== $min_flat_key ) {
						unset( $this->shipping_method->found_rates[ $key ] );
					}
				}
			} else {
				$this->shipping_method->debug( 'Unsetting PRIORITY MAIL flat rate - api rate is cheaper.' );
				foreach ( $priority_flat_keys as $key ) {
					unset( $this->shipping_method->found_rates[ $key ] );
				}
			}
		}

		// Only offer one Priority Mail Express rate (standard vs flat rate).
		$express_standard_key = '';
		if ( isset( $found_rates['usps:D_EXPRESS_MAIL'] ) ) {
			$express_standard_key = 'usps:D_EXPRESS_MAIL';
		} elseif ( isset( $found_rates['usps:I_EXPRESS_MAIL'] ) ) {
			$express_standard_key = 'usps:I_EXPRESS_MAIL';
		}

		$express_flat_keys = array();
		foreach ( $found_rates as $key => $_rate ) {
			if ( 0 === strpos( $key, $this->shipping_method->get_rate_id() . ':flatrate:express:' ) ) {
				$express_flat_keys[] = $key;
			}
		}

		if ( $express_standard_key && ! empty( $express_flat_keys ) ) {
			$min_flat_key  = '';
			$min_flat_cost = null;
			foreach ( $express_flat_keys as $key ) {
				$cost = isset( $found_rates[ $key ] ) ? $found_rates[ $key ]['cost'] : null;
				if ( null === $min_flat_cost || ( null !== $cost && $cost < $min_flat_cost ) ) {
					$min_flat_cost = $cost;
					$min_flat_key  = $key;
				}
			}

			if ( null !== $min_flat_cost && $min_flat_cost < $found_rates[ $express_standard_key ]['cost'] ) {
				$this->shipping_method->debug( 'Unsetting PRIORITY MAIL EXPRESS api rate - flat rate box is cheaper.' );
				unset( $this->shipping_method->found_rates[ $express_standard_key ] );
				// Keep only the cheapest express flat-rate option.
				foreach ( $express_flat_keys as $key ) {
					if ( $key !== $min_flat_key ) {
						unset( $this->shipping_method->found_rates[ $key ] );
					}
				}
			} else {
				$this->shipping_method->debug( 'Unsetting PRIORITY MAIL EXPRESS flat rate - api rate is cheaper.' );
				foreach ( $express_flat_keys as $key ) {
					unset( $this->shipping_method->found_rates[ $key ] );
				}
			}
		}

		/**
		 * Filter to modify the found rates.
		 *
		 * @param array $found_rates List of found rates.
		 * @param array $raw_found_rates List of found rates before being processed.
		 * @param string $offer_rates Rates to offer. Valid values are "all" and "cheapest".
		 *
		 * @since 4.4.64
		 */
		$this->shipping_method->found_rates = apply_filters( 'woocommerce_shipping_usps_found_rates', $this->shipping_method->found_rates, $this->shipping_method->raw_found_rates, $this->shipping_method->offer_rates );

		if ( 'all' === $this->shipping_method->offer_rates ) {
			uasort( $this->shipping_method->found_rates, array( $this->shipping_method, 'sort_rates' ) );

			foreach ( $this->shipping_method->found_rates as $key => $rate ) {
				$this->shipping_method->add_rate( $rate );
			}
		} else {
			$cheapest_rate = '';

			foreach ( $this->shipping_method->found_rates as $key => $rate ) {
				if ( ! $cheapest_rate || $cheapest_rate['cost'] > $rate['cost'] ) {
					$cheapest_rate = $rate;

					/*
					 * Maybe get the custom label for the cheapest rate,
					 * otherwise use the specific rate label with (USPS) appended.
					 */
					$split_key = explode( ':', $key );
					if ( ! empty( $split_key[1] ) && array_key_exists( $split_key[1], $this->shipping_method->custom_services ) && ! empty( $this->shipping_method->custom_services[ $split_key[1] ]['name'] ) ) {
						$cheapest_rate['label'] = $this->shipping_method->custom_services[ $split_key[1] ]['name'];
					} else {
						// translators: %1$s is Label rate, %2$s is the shipping method title.
						$cheapest_rate['label'] = sprintf( __( '%1$s (%2$s)', 'woocommerce-shipping-usps' ), $cheapest_rate['label'], $this->shipping_method->title );
					}
				}
			}

			$this->shipping_method->add_rate( $cheapest_rate );
		}
	}

	/**
	 * Send the API request to USPS.
	 *
	 * @since 4.4.7
	 *
	 * @param array $api_request The API request to send.
	 *
	 * @return string|bool The response body or false if the request failed.
	 */
	private function send_api_request( array $api_request ) {

		// Log the request.
		$this->shipping_method->debug( 'USPS Rate REST Request:', $api_request );

		$headers = array(
			'Content-Type'  => 'application/json',
			'Authorization' => 'Bearer ' . $this->shipping_method->oauth->get_access_token(),
		);

		/**
		 * Filter to modify the USPS REST API request.
		 *
		 * @param array $api_request The api request.
		 * @param array $package The package to ship.
		 *
		 * @since 5.5.1
		 */
		$body = wp_json_encode( apply_filters( 'woocommerce_shipping_usps_rest_api_request', $api_request, $this->package ) );

		$response = wp_remote_post(
			$this->shipping_method::API_URL . '/shipments/v3/options/search',
			array(
				'headers' => $headers,
				'body'    => $body,
			)
		);

		if ( is_wp_error( $response ) ) {
			// phpcs:ignore --- print_r() only being used when on debug mode.
			$error_messages = array();
			if ( is_array( $response->get_error_messages() ) ) {
				foreach ( $response->get_error_messages() as $error_message ) {
					$error_messages[] = json_decode( $error_message, true );
				}
			}

			$this->shipping_method->debug( 'USPS REQUEST FAILED. Error message(s):', $error_messages );

			return false;
		}

		$response_code = wp_remote_retrieve_response_code( $response );
		$response_body = wp_remote_retrieve_body( $response );
		$response_msg  = wp_remote_retrieve_response_message( $response );

		$this->shipping_method->debug(
			'USPS Rate REST Response:',
			array(
				'CODE'    => $response_code,
				'MESSAGE' => $response_msg,
				'BODY'    => $this->maybe_simplify_response_body( $response_body ),
			)
		);

		return $response_body;
	}

	/**
	 * Maybe simplify the response body.
	 *
	 * @param string $response_body The response body.
	 *
	 * @return mixed
	 */
	private function maybe_simplify_response_body( string $response_body ) {
		$response_body = json_decode( $response_body, true );

		if ( ! isset( $response_body['rateOptions'] ) || ! is_array( $response_body['rateOptions'] ) ) {
			return $response_body;
		}

		foreach ( $response_body['rateOptions'] as $key => $rate ) {
			if ( ! isset( $rate['extraServices'] ) ) {
				continue;
			}
			unset( $response_body['rateOptions'][ $key ]['extraServices'] );
		}

		return $response_body;
	}

	/**
	 * Generate a package ID for the request.
	 *
	 * Contains qty and dimension info so we can look at it again later when it
	 * comes back from USPS if needed.
	 *
	 * @param string $id           Package ID.
	 * @param int    $qty          Quantity.
	 * @param float  $l            Length.
	 * @param float  $w            Width.
	 * @param float  $h            Height.
	 * @param float  $weight       Weight.
	 * @param string $request_type 'flatrate' or 'api'.
	 * @param string $service      'express' or 'priority'.
	 * @param string $service_id   Used by international flat rate requests to define which box to use.
	 *
	 * @return string
	 */
	public function generate_package_id( $id, $qty, $l, $w, $h, $weight, $request_type = '', $service = '', $service_id = '' ) {
		return implode( ':', array( $id, $qty, $l, $w, $h, $weight, $request_type, $service, $service_id ) );
	}

	/**
	 * Get the USPS API responses.
	 *
	 * @param array $api_requests API requests.
	 *
	 * @return array
	 */
	private function get_api_responses( array $api_requests ): array {
		$api_responses = array();

		foreach ( $api_requests as $request_id => $api_request ) {
			if ( empty( $api_request ) ) {
				continue;
			}

			$transient_key   = 'usps_quote_' . md5( wp_json_encode( $api_request ) );
			$cached_response = get_transient( $transient_key );

			/**
			 * Filter to enable or disable API response caching.
			 *
			 * @param bool  $enable_caching Whether to enable API response caching. Default true.
			 * @param array $api_request    The API request being processed.
			 * @param array $package        The package being shipped.
			 *
			 * @since 4.4.10
			 */
			$enable_caching = apply_filters( 'woocommerce_shipping_usps_enable_api_response_caching', true, $api_request, $this->package );

			// If caching is disabled, force a new API request.
			if ( ! $enable_caching ) {
				$cached_response = false;
			}

			// If there's a cached response, use it.
			if ( false !== $cached_response ) {
				$this->shipping_method->debug(
					'USPS Rate REST Response (Cached)',
					array( json_decode( $cached_response ) )
				);

				$api_responses[ $request_id ] = $cached_response;

				continue;
			}

			$response = $this->send_api_request( $api_request );
			if ( ! $response ) {
				$this->shipping_method->debug(
					'USPS Rate REST Response (Failed)',
					array(
						'request' => $api_request,
					)
				);

				continue;
			}

			/**
			 * Cache the response for one week if response contains rates.
			 *
			 * @var int $transient_expiration Transient expiration in seconds.
			 *
			 * @since 4.4.9
			 */
			$transient_expiration = apply_filters( 'woocommerce_shipping_usps_transient_expiration', DAY_IN_SECONDS * 7 );
			set_transient( $transient_key, $response, $transient_expiration );

			$api_responses[ $request_id ] = $response;
		}

		return $api_responses;
	}

	/**
	 * Parse rates from rate options.
	 *
	 * @param array  $rate_options Rate options.
	 * @param string $request_id   Request ID.
	 *
	 * @return array
	 */
	private function extract_rates_from_rate_options( array $rate_options, string $request_id ): array {
		$rates = array();

		// Get request ID parts.
		$request_id_parts = explode( ':', $request_id );
		if ( count( $request_id_parts ) < 6 ) {
			return array();
		}

		list( $package_item_id, $cart_item_qty, $package_length, $package_width, $package_height, $package_weight, $request_type, $service_type, $service_id ) = $request_id_parts;

		// Use this array to pass metadata to the order item.
		$meta_data                   = array();
		$meta_data['package_length'] = $package_length;
		$meta_data['package_width']  = $package_width;
		$meta_data['package_height'] = $package_height;
		$meta_data['package_weight'] = $package_weight;

		if ( 'flatrate' === $request_type ) {

			$box_rate_indicator = $this->get_rate_indicator_for_box( $service_id );

			foreach ( $rate_options as $rate_option ) {
				$rate = isset( $rate_option->rates[0] ) ? $rate_option->rates[0] : null;

				// If the rate indicator doesn't match, skip this rate.
				if (
					! $rate
					|| ! isset( $rate->rateIndicator )
					|| $box_rate_indicator !== (string) $rate->rateIndicator
				) {
					continue;
				}

				$rate_id = implode(
					':',
					array(
						$this->shipping_method->get_rate_id(),
						$request_type,
						$service_type,
						$service_id,
					)
				);

				if ( 'express' === $service_type ) {
					$label = $this->shipping_method->get_option( 'flat_rate_express_title', ( $this->is_domestic_shipment ? '' : 'International ' ) . 'Priority Mail Express Flat Rate&#0174;' );
					$sort  = - 1;
				} else {
					$label = $this->shipping_method->get_option( 'flat_rate_priority_title', ( $this->is_domestic_shipment ? '' : 'International ' ) . 'Priority Mail Flat Rate&#0174;' );
					$sort  = - 2;
				}

				$rate_cost = (float) $rate_option->totalBasePrice * (int) $cart_item_qty;

				// Fees.
				if ( ! empty( $this->shipping_method->flat_rate_fee ) ) {
					$sym = substr( $this->shipping_method->flat_rate_fee, 0, 1 );
					$fee = '-' === $sym ? substr( $this->shipping_method->flat_rate_fee, 1 ) : $this->shipping_method->flat_rate_fee;
					if ( strstr( $fee, '%' ) ) {
						$fee = str_replace( '%', '', $fee );
						if ( '-' === $sym ) {
							$rate_cost = $rate_cost - ( $rate_cost * ( floatval( $fee ) / 100 ) );
						} else {
							$rate_cost = $rate_cost + ( $rate_cost * ( floatval( $fee ) / 100 ) );
						}
					} else {
						$rate_cost = ( '-' === $sym ) ? ( $rate_cost - floatval( $fee ) ) : ( $rate_cost + floatval( $fee ) );
					}

					if ( $rate_cost < 0 ) {
						$rate_cost = 0;
					}
				}

				$meta_data['package_description'] = wp_strip_all_tags( htmlspecialchars_decode( (string) $rate->description, ENT_COMPAT ) );

				$rates[] = array(
					'code'      => $rate->rateIndicator,
					'id'        => $rate_id,
					'label'     => $label,
					'cost'      => $rate_cost,
					'meta_data' => $meta_data,
					'sort'      => $sort,
				);
			}
		} else {
			// Loop defined services.
			foreach ( $this->shipping_method->services as $service_id => $service ) {

				if ( $this->is_domestic_shipment !== $this->is_domestic_service( $service_id ) ) {
					continue;
				}

				$service_name = trim( str_replace( 'USPS', '', $service['name'] ) );

				$rate_code           = (string) $service_id;
				$rate_id             = $this->shipping_method->get_rate_id() . ':' . $rate_code;
				$rate_name           = $service_name . " ({$this->shipping_method->title})";
				$rate_cost           = null;
				$svc_commitment      = null;
				$quoted_package_name = null;

				// Loop through rate quotes returned from USPS.
				foreach ( $rate_options as $rate_option ) {
					$quoted_service_name = sanitize_title( wp_strip_all_tags( htmlspecialchars_decode( (string) $rate_option->rates[0]->description, ENT_COMPAT ) ) );

					if ( false !== stripos( $quoted_service_name, 'flat-rate' ) ) {
						continue; // skip all flat rate, handled above.
					}

					$code = substr( (string) $rate_option->rates[0]->SKU, 0, 4 );
					$code = $this->map_rest_sku_to_legacy_code( $code );
					$cost = null;

					$service_codes = array_map( 'strval', array_keys( $service['services'] ) );

					if ( '' === $code || ! in_array( $code, $service_codes, true ) ) {
						continue;
					}

					$cost = (float) $rate_option->totalBasePrice * (int) $cart_item_qty;

					// Process sub sub services.
					if ( '0' === $code ) {
						if ( array_key_exists( $quoted_service_name, $this->shipping_method->custom_services[ $rate_code ][ $code ] ) ) {
							// Enabled check.
							if ( ! empty( $this->shipping_method->custom_services[ $rate_code ][ $code ][ $quoted_service_name ] ) && ( true !== $this->shipping_method->custom_services[ $rate_code ][ $code ][ $quoted_service_name ]['enabled'] || empty( $this->shipping_method->custom_services[ $rate_code ][ $code ][ $quoted_service_name ]['enabled'] ) ) ) {
								continue;
							}

							// Cost adjustment %.
							if ( ! empty( $this->shipping_method->custom_services[ $rate_code ][ $code ][ $quoted_service_name ]['adjustment_percent'] ) ) {
								$cost = round( $cost + ( $cost * ( floatval( $this->shipping_method->custom_services[ $rate_code ][ $code ][ $quoted_service_name ]['adjustment_percent'] ) / 100 ) ), wc_get_price_decimals() );
							}

							// Cost adjustment.
							if ( ! empty( $this->shipping_method->custom_services[ $rate_code ][ $code ][ $quoted_service_name ]['adjustment'] ) ) {
								$cost = round( $cost + floatval( $this->shipping_method->custom_services[ $rate_code ][ $code ][ $quoted_service_name ]['adjustment'] ), wc_get_price_decimals() );
							}
						}
					} else {
						// Enabled check.
						if ( ! empty( $this->shipping_method->custom_services[ $rate_code ][ $code ] ) && ( true !== $this->shipping_method->custom_services[ $rate_code ][ $code ]['enabled'] || empty( $this->shipping_method->custom_services[ $rate_code ][ $code ]['enabled'] ) ) ) {

							continue;
						}

						// Cost adjustment %.
						if ( ! empty( $this->shipping_method->custom_services[ $rate_code ][ $code ]['adjustment_percent'] ) ) {
							$cost = round( $cost + ( $cost * ( floatval( $this->shipping_method->custom_services[ $rate_code ][ $code ]['adjustment_percent'] ) / 100 ) ), wc_get_price_decimals() );
						}

						// Cost adjustment.
						if ( ! empty( $this->shipping_method->custom_services[ $rate_code ][ $code ]['adjustment'] ) ) {
							$cost = round( $cost + floatval( $this->shipping_method->custom_services[ $rate_code ][ $code ]['adjustment'] ), wc_get_price_decimals() );
						}
					}

					if ( $this->is_domestic_shipment ) {
						switch ( $code ) {
							// Handle first class - there are multiple d0 rates and we need to handle size retrictions because the API doesn't do this for us!
							case '0':
								$service_name = wp_strip_all_tags( htmlspecialchars_decode( (string) $rate_option->rates[0]->description, ENT_COMPAT ) );

								/**
								 * Filter to disable the first-class rate.
								 *
								 * @param bool $should_disable_first_class Whether to disable the first-class rate.
								 *
								 * @since 3.7.3
								 */
								if ( apply_filters( 'usps_disable_first_class_rate_' . sanitize_title( $service_name ), false ) ) {
									continue 2;
								}
								break;
							// Media mail has restrictions - check here.
							case '6':
								if ( ! empty( $this->shipping_method->mediamail_restriction ) && is_array( $this->shipping_method->mediamail_restriction ) ) {
									$invalid = false;

									foreach ( $this->package['contents'] as $package_item ) {
										if ( ! in_array( $package_item['data']->get_shipping_class_id(), array_map( 'intval', $this->shipping_method->mediamail_restriction ), true ) ) {
											$invalid = true;
										}
									}

									if ( $invalid ) {
										$this->shipping_method->debug( 'Skipping media mail' );
										continue 2;
									}
								}
								break;
						}
					}

					if ( $this->is_domestic_shipment && $package_length && $package_width && $package_height ) {
						switch ( $code ) {
							case '58':
							case 'DUXP':
								if ( $package_length > 14.75 || $package_width > 11.75 || $package_height > 11.5 ) {
									continue 2;
								} else {
									// Valid.
									break;
								}
								break;
							// Handle first class - there are multiple d0 rates and we need to handle size restrictions because the API doesn't do this for us!
							// Apply the same checks for the rate: 78 - First-Class Mail® Metered Letter.
							//
							// See https://www.usps.com/ship/preparing-domestic-shipments.htm.
							case '0':
							case '78':
								$service_name = wp_strip_all_tags( htmlspecialchars_decode( (string) $rate_option->rates[0]->description, ENT_COMPAT ) );

								if ( strstr( $service_name, 'Postcards' ) ) {

									if ( $package_length > 6 || $package_length < 5 ) {
										continue 2;
									}
									if ( $package_width > 4.25 || $package_width < 3.5 ) {
										continue 2;
									}
									if ( $package_height > 0.016 || $package_height < 0.007 ) {
										continue 2;
									}
								} elseif ( strstr( $service_name, 'Large Envelope' ) ) {
									if ( ! $this->shipping_method->is_large_envelope( $package_length, $package_width, $package_height ) ) {
										continue 2;
									}
								} elseif ( strstr( $service_name, 'Letter' ) ) {
									if ( ! $this->shipping_method->is_letter( $package_length, $package_width, $package_height ) ) {
										continue 2;
									}
								} elseif ( strstr( $service_name, 'Parcel' ) ) {
									$girth = $this->shipping_method->get_girth( array( $package_width, $package_height ) );

									if ( ( $girth + (float) $package_length ) > 108 ) {
										continue 2;
									}
								} elseif ( strstr( $service_name, 'Package' ) ) {
									$girth = $this->shipping_method->get_girth( array( $package_width, $package_height ) );

									if ( ( $girth + (float) $package_length ) > 108 ) {
										continue 2;
									}
								} else {
									continue 2;
								}
								break;
						}
					}

					/**
					 * Check for USPS Non-Standard fees incorrectly applied to
					 * USPS medium/small tubes and subtract from the total rate.
					 *
					 * Background:
					 * USPS has begun implementing fees for packages that have
					 * lengths/volumes exceeding what they deem standard dimensions.
					 *
					 * @see   https://www.usps.com/business/web-tools-apis/2022-web-tools-release-notes.pdf section 2.3.1
					 *
					 * These new USPS Non-Standard fees are automatically applied to all
					 * non-standard packages and returned in the total postage rate in the
					 * API response.
					 *
					 * These fees are not supposed to be applied to USPS provided boxes/tubes,
					 * but because we don't have a way to indicate that we are using USPS
					 * packaging in the API request, the fees are currently (and wrongly)
					 * being applied in cases where merchants are using USPS small/medium
					 * tubes. These tubes qualify as non-standard because the lengths are
					 * over 22".
					 *
					 * Hopefully USPS will provide some way to indicate a USPS provided
					 * package in the API request at some point. But until then, in order to
					 * provide a temporary fix, we are checking if package dimensions
					 * match USPS tube dimensions and removing any corresponding fees.
					 *
					 * @see   https://github.com/woocommerce/woocommerce-shipping-usps/issues/350
					 *
					 * @since 4.5.0
					 */
					if ( ! empty( $rate_option->{'Fees'} ) && $package_length && $package_width && $package_height && apply_filters( 'woocommmerce_shipping_usps_tubes_remove_non_standard_fees', true ) ) {
						if ( $this->shipping_method->package_has_usps_tube_dimensions( $package_length, $package_width, $package_height ) ) {

							$total_non_standard_fees = 0;
							foreach ( $rate_option->{'Fees'} as $non_standard_fee ) {
								if ( empty( $non_standard_fee->{'Fee'} ) || empty( $non_standard_fee->{'Fee'}->{'FeePrice'} ) ) {
									continue;
								}

								foreach ( $non_standard_fee->{'Fee'}->{'FeePrice'} as $fee_price ) {
									$total_non_standard_fees += (float) $fee_price;
								}
							}

							$cost -= $total_non_standard_fees;
						}
					}

					if ( is_null( $rate_cost ) ) {
						$rate_cost           = $cost;
						$svc_commitment      = $rate_option->commitment->name ?? null;
						$quoted_package_name = wp_strip_all_tags( htmlspecialchars_decode( (string) $rate_option->rates[0]->description, ENT_COMPAT ) );
					} elseif ( $cost < $rate_cost ) {
						$rate_cost           = $cost;
						$svc_commitment      = $rate_option->commitment->name ?? null;
						$quoted_package_name = wp_strip_all_tags( htmlspecialchars_decode( (string) $rate_option->rates[0]->description, ENT_COMPAT ) );
					}

					/**
					 * Allow merchants to show/hide service commitment information in the rate label.
					 *
					 * @param bool   $should_show_service_commitment Should the service commitment be displayed?
					 * @param object $rate_option                    The current rate option we're processing.
					 *
					 * @return bool
					 *
					 * @since 5.3.0
					 */
					$should_show_service_commitment = apply_filters( 'woocommmerce_shipping_usps_should_show_service_commitment', true, $rate_option );

					if ( ! $should_show_service_commitment ) {
						$svc_commitment = null;
					}
				}

				if ( ! is_null( $rate_cost ) ) {

					if ( ! empty( $svc_commitment ) && stristr( $svc_commitment, 'days' ) ) {
						$svc_commitment = strtolower( $svc_commitment );
						$rate_name     .= ' (' . $svc_commitment . ')';
					}

					$meta_data['package_description'] = $this->shipping_method->get_rate_package_description(
						array(
							'length' => $package_length,
							'width'  => $package_width,
							'height' => $package_height,
							'weight' => $package_weight,
							'qty'    => 'per_item' === $this->shipping_method->packing_method ? $cart_item_qty : 0,
							'name'   => $quoted_package_name,
						)
					);

					/**
					 * Filter to modify the rate name.
					 *
					 * @param string $rate_name Rate name.
					 * @param string $rate_id The rate ID.
					 *
					 * @since 4.4.48
					 */
					$rate_name = apply_filters( 'woocommmerce_shipping_usps_custom_service_rate_name', $rate_name, $rate_id );

					$rates[] = array(
						'code'      => $rate_code,
						'id'        => $rate_id,
						'label'     => $rate_name,
						'cost'      => $rate_cost,
						'meta_data' => $meta_data,
						'sort'      => 999,
					);
				}
			}
		}

		return $rates;
	}
}
