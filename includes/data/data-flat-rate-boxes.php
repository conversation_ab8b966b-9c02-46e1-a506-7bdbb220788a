<?php
/**
 * An array of flat rate boxes sizes for USPS
 *
 * @package WC_Shipping_USPS
 */

/**
 * Filter to modify the flat rate box list.
 *
 * @var array List of flat rate box.
 *
 * @since 4.4.2
 */
return apply_filters(
	'wc_usps_flat_rate_boxes',
	array(
		// Priority Mail Express.
		'd13'  => array(
			'id'             => 'FLAT RATE ENVELOPE',
			'name'           => 'Priority Mail Express Flat Rate Envelope',
			'length'         => '12.5',
			'width'          => '9.5',
			'height'         => '0.5',
			'weight'         => '0.10',
			'max_weight'     => '70',
			'type'           => 'envelope',
			'service'        => 'express',
			'rate_indicator' => 'E4',
		),
		'd30'  => array(
			'id'             => 'LEGAL FLAT RATE ENVELOPE',
			'name'           => 'Priority Mail Express Legal Flat Rate Envelope',
			'length'         => '15',
			'width'          => '9.5',
			'height'         => '0.5',
			'weight'         => '0.11',
			'max_weight'     => '70',
			'type'           => 'envelope',
			'service'        => 'express',
			'rate_indicator' => 'E6',
		),
		'd63'  => array(
			'id'             => 'PADDED FLAT RATE ENVELOPE',
			'name'           => 'Priority Mail Express Padded Flat Rate Envelope',
			'length'         => '12.5',
			'width'          => '9.5',
			'height'         => '0.5',
			'weight'         => '0.05',
			'max_weight'     => '70',
			'type'           => 'envelope',
			'service'        => 'express',
			'rate_indicator' => 'FP',
		),

		// Priority Mail.
		'd16'  => array(
			'id'             => 'FLAT RATE ENVELOPE',
			'name'           => 'Priority Mail Flat Rate Envelope',
			'length'         => '12.5',
			'width'          => '9.5',
			'height'         => '0.5',
			'weight'         => '0.10',
			'max_weight'     => '70',
			'type'           => 'envelope',
			'service'        => 'priority',
			'rate_indicator' => 'FE',
		),
		'd17'  => array(
			'id'             => 'MD FLAT RATE BOX',
			'name'           => 'Priority Mail Medium Flat Rate Box - 2',
			'length'         => '13.625',
			'width'          => '11.875',
			'height'         => '3.375',
			'weight'         => '0.66',
			'max_weight'     => '70',
			'service'        => 'priority',
			'rate_indicator' => 'FB',
		),
		'd17b' => array(
			'id'             => 'MD FLAT RATE BOX',
			'name'           => 'Priority Mail Medium Flat Rate Box - 1',
			'length'         => '11',
			'width'          => '8.5',
			'height'         => '5.5',
			'weight'         => '0.49',
			'max_weight'     => '70',
			'service'        => 'priority',
			'rate_indicator' => 'FB',
		),
		'd22'  => array(
			'id'             => 'LG FLAT RATE BOX',
			'name'           => 'Priority Mail Large Flat Rate Box',
			'length'         => '12',
			'width'          => '12',
			'height'         => '5.5',
			'weight'         => '0.73',
			'max_weight'     => '70',
			'service'        => 'priority',
			'rate_indicator' => 'PL',
		),
		'd28'  => array(
			'id'             => 'SM FLAT RATE BOX',
			'name'           => 'Priority Mail Small Flat Rate Box',
			'length'         => '8.625',
			'width'          => '5.375',
			'height'         => '1.625',
			'weight'         => '0.18',
			'max_weight'     => '70',
			'service'        => 'priority',
			'rate_indicator' => 'FS',
		),
		'd29'  => array(
			'id'             => 'PADDED FLAT RATE ENVELOPE',
			'name'           => 'Priority Mail Padded Flat Rate Envelope',
			'length'         => '12.5',
			'width'          => '9.5',
			'height'         => '0.5',
			'weight'         => '0.05',
			'max_weight'     => '70',
			'type'           => 'envelope',
			'service'        => 'priority',
			'rate_indicator' => 'FP',
		),
		'd38'  => array(
			'id'             => 'GIFT CARD FLAT RATE ENVELOPE',
			'name'           => 'Priority Mail Gift Card Flat Rate Envelope',
			'length'         => '10',
			'width'          => '7',
			'height'         => '0.5',
			'weight'         => '0.05',
			'max_weight'     => '70',
			'type'           => 'envelope',
			'service'        => 'priority',
			'rate_indicator' => 'FE',
		),
		'd40'  => array(
			'id'             => 'WINDOW FLAT RATE ENVELOPE',
			'name'           => 'Priority Mail Window Flat Rate Envelope',
			'length'         => '10',
			'width'          => '5',
			'height'         => '0.25',
			'weight'         => '0.02',
			'max_weight'     => '70',
			'type'           => 'envelope',
			'service'        => 'priority',
			'rate_indicator' => 'FE',
		),
		'd42'  => array(
			'id'             => 'SM FLAT RATE ENVELOPE',
			'name'           => 'Priority Mail Small Flat Rate Envelope',
			'length'         => '10',
			'width'          => '6',
			'height'         => '0.5',
			'weight'         => '0.05',
			'max_weight'     => '70',
			'type'           => 'envelope',
			'service'        => 'priority',
			'rate_indicator' => 'FE',
		),
		'd44'  => array(
			'id'             => 'LEGAL FLAT RATE ENVELOPE',
			'name'           => 'Priority Mail Legal Flat Rate Envelope',
			'length'         => '15',
			'width'          => '9.5',
			'height'         => '0.5',
			'weight'         => '0.11',
			'max_weight'     => '70',
			'type'           => 'envelope',
			'service'        => 'priority',
			'rate_indicator' => 'FA',
		),

		// International Priority Mail Express.
		'i13'  => array(
			'id'             => '10',
			'name'           => 'Priority Mail Express Flat Rate Envelope',
			'length'         => '12.5',
			'width'          => '9.5',
			'height'         => '0.5',
			'weight'         => '0.10',
			'max_weight'     => '4',
			'type'           => 'envelope',
			'service'        => 'express',
			'rate_indicator' => 'E4',
		),
		'i30'  => array(
			'id'             => '17',
			'name'           => 'Priority Mail Express Legal Flat Rate Envelope',
			'length'         => '15',
			'width'          => '9.5',
			'height'         => '0.5',
			'weight'         => '0.11',
			'max_weight'     => '4',
			'type'           => 'envelope',
			'service'        => 'express',
			'rate_indicator' => 'E6',
		),
		'i63'  => array(
			'id'             => '27',
			'name'           => 'Priority Mail Express Padded Flat Rate Envelope',
			'length'         => '12.5',
			'width'          => '9.5',
			'height'         => '0.5',
			'weight'         => '0.05',
			'max_weight'     => '4',
			'type'           => 'envelope',
			'service'        => 'express',
			'rate_indicator' => 'FP',
		),

		// International Priority Mail.
		'i8'   => array(
			'id'             => '8',
			'name'           => 'Priority Mail Flat Rate Envelope',
			'length'         => '12.5',
			'width'          => '9.5',
			'height'         => '0.5',
			'weight'         => '0.10',
			'max_weight'     => '4',
			'type'           => 'envelope',
			'service'        => 'priority',
			'rate_indicator' => 'FE',
		),
		'i29'  => array(
			'id'             => '23',
			'name'           => 'Priority Mail Padded Flat Rate Envelope',
			'length'         => '12.5',
			'width'          => '9.5',
			'height'         => '0.5',
			'weight'         => '0.05',
			'max_weight'     => '4',
			'type'           => 'envelope',
			'service'        => 'priority',
			'rate_indicator' => 'FP',
		),
		'i16'  => array(
			'id'             => '16',
			'name'           => 'Priority Mail Flat Rate Small Box',
			'length'         => '8.625',
			'width'          => '5.375',
			'height'         => '1.625',
			'weight'         => '0.18',
			'max_weight'     => '4',
			'service'        => 'priority',
			'rate_indicator' => 'FS',
		),
		'i9'   => array(
			'id'             => '9',
			'name'           => 'Priority Mail Flat Rate Medium Box',
			'length'         => '13.625',
			'width'          => '11.875',
			'height'         => '3.375',
			'weight'         => '0.66',
			'max_weight'     => '20',
			'service'        => 'priority',
			'rate_indicator' => 'FB',
		),
		'i9b'  => array(
			'id'             => '9',
			'name'           => 'Priority Mail Flat Rate Medium Box',
			'length'         => '11',
			'width'          => '8.5',
			'height'         => '5.5',
			'weight'         => '0.49',
			'max_weight'     => '70',
			'service'        => 'priority',
			'rate_indicator' => 'FB',
		),
		'i11'  => array(
			'id'             => '11',
			'name'           => 'Priority Mail Flat Rate Large Box',
			'length'         => '12',
			'width'          => '12',
			'height'         => '5.5',
			'weight'         => '0.73',
			'max_weight'     => '20',
			'service'        => 'priority',
			'rate_indicator' => 'PL',
		),
	)
);
