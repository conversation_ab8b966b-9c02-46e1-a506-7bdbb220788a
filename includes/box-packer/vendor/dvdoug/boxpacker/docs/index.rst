Welcome to BoxPacker's documentation!
=====================================

BoxPacker is an implementation of the "4D" bin packing/knapsack problem i.e. given a list of items, how many boxes do you need to
fit them all in.

Especially useful for e.g. e-commerce contexts when you need to know box size/weight to calculate shipping costs, or
even just want to know the right number of labels to print.

License
-------
BoxPacker is licensed under the `MIT license`_.

.. _MIT license: https://github.com/dvdoug/BoxPacker/blob/master/license.txt


.. toctree::
    :maxdepth: 1
    :caption: Contents

    installation
    principles
    getting-started
    rotation
    weight-distribution
    too-large-items
    advanced-usage
    whatsnew
