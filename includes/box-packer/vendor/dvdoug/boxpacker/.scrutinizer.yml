checks:
  php: true

tools:
  external_code_coverage:
    runs: 2    # Scrutinizer will wait for this many code coverage submissions
    timeout: 600    # Timeout in seconds.

filter:
  excluded_paths:
    - 'features/'
    - 'tests/'

build:
  environment:
    # Languages
    php:
      version: "7.4"
      ini:
        memory_limit: "-1"

  dependencies:
    override:
      - 'composer update --no-interaction --prefer-dist --ignore-platform-reqs'

  tests:
    override:
      - php-scrutinizer-run
