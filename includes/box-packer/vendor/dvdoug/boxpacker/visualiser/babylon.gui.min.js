!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("babylonjs")):"function"==typeof define&&define.amd?define("babylonjs-gui",["babylonjs"],e):"object"==typeof exports?exports["babylonjs-gui"]=e(require("babylonjs")):(t.BABYLON=t.BABYLON||{},t.BABYLON.GUI=e(t.BABYLON))}("undefined"!=typeof self?self:"undefined"!=typeof global?global:this,(function(t){return function(t){var e={};function i(r){if(e[r])return e[r].exports;var o=e[r]={i:r,l:!1,exports:{}};return t[r].call(o.exports,o,o.exports,i),o.l=!0,o.exports}return i.m=t,i.c=e,i.d=function(t,e,r){i.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},i.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i.t=function(t,e){if(1&e&&(t=i(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(i.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)i.d(r,o,function(e){return t[e]}.bind(null,o));return r},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=2)}([function(e,i){e.exports=t},function(t,e,i){"use strict";i.r(e),i.d(e,"Button",(function(){return O})),i.d(e,"Checkbox",(function(){return x})),i.d(e,"ColorPicker",(function(){return M})),i.d(e,"Container",(function(){return g})),i.d(e,"Control",(function(){return f})),i.d(e,"Ellipse",(function(){return I})),i.d(e,"FocusableButton",(function(){return k})),i.d(e,"Grid",(function(){return w})),i.d(e,"Image",(function(){return v})),i.d(e,"InputText",(function(){return T})),i.d(e,"InputPassword",(function(){return B})),i.d(e,"Line",(function(){return S})),i.d(e,"MultiLine",(function(){return E})),i.d(e,"RadioButton",(function(){return D})),i.d(e,"StackPanel",(function(){return C})),i.d(e,"SelectorGroup",(function(){return F})),i.d(e,"CheckboxGroup",(function(){return N})),i.d(e,"RadioGroup",(function(){return z})),i.d(e,"SliderGroup",(function(){return V})),i.d(e,"SelectionPanel",(function(){return j})),i.d(e,"ScrollViewer",(function(){return U})),i.d(e,"TextWrapping",(function(){return b})),i.d(e,"TextBlock",(function(){return y})),i.d(e,"TextWrapper",(function(){return P})),i.d(e,"ToggleButton",(function(){return X})),i.d(e,"KeyPropertySet",(function(){return Y})),i.d(e,"VirtualKeyboard",(function(){return Q})),i.d(e,"Rectangle",(function(){return m})),i.d(e,"DisplayGrid",(function(){return K})),i.d(e,"BaseSlider",(function(){return L})),i.d(e,"Slider",(function(){return R})),i.d(e,"ImageBasedSlider",(function(){return q})),i.d(e,"ScrollBar",(function(){return W})),i.d(e,"ImageScrollBar",(function(){return G})),i.d(e,"name",(function(){return Z})),i.d(e,"AdvancedDynamicTexture",(function(){return $})),i.d(e,"AdvancedDynamicTextureInstrumentation",(function(){return tt})),i.d(e,"Vector2WithInfo",(function(){return d})),i.d(e,"Matrix2D",(function(){return p})),i.d(e,"Measure",(function(){return _})),i.d(e,"MultiLinePoint",(function(){return A})),i.d(e,"Style",(function(){return J})),i.d(e,"ValueAndUnit",(function(){return a})),i.d(e,"XmlLoader",(function(){return et})),i.d(e,"AbstractButton3D",(function(){return ot})),i.d(e,"Button3D",(function(){return nt})),i.d(e,"Container3D",(function(){return st})),i.d(e,"Control3D",(function(){return rt})),i.d(e,"CylinderPanel",(function(){return ht})),i.d(e,"HolographicButton",(function(){return pt})),i.d(e,"MeshButton3D",(function(){return ft})),i.d(e,"PlanePanel",(function(){return gt})),i.d(e,"ScatterPanel",(function(){return bt})),i.d(e,"SpherePanel",(function(){return mt})),i.d(e,"StackPanel3D",(function(){return yt})),i.d(e,"ButtonState",(function(){return dt})),i.d(e,"TouchButton3D",(function(){return vt})),i.d(e,"TouchMeshButton3D",(function(){return Ot})),i.d(e,"TouchHolographicButton",(function(){return wt})),i.d(e,"TouchToggleButton3D",(function(){return Mt})),i.d(e,"VolumeBasedPanel",(function(){return at})),i.d(e,"FluentMaterialDefines",(function(){return ut})),i.d(e,"FluentMaterial",(function(){return _t})),i.d(e,"FluentButtonMaterial",(function(){return Tt})),i.d(e,"GUI3DManager",(function(){return It})),i.d(e,"Vector3WithInfo",(function(){return it}));
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)};function o(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}function n(t,e,i,r){var o,n=arguments.length,s=n<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,i):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,i,r);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(n<3?o(s):n>3?o(e,i,s):o(e,i))||s);return n>3&&s&&Object.defineProperty(e,i,s),s}Object.create;Object.create;var s=i(0),a=function(){function t(e,i,r){void 0===i&&(i=t.UNITMODE_PIXEL),void 0===r&&(r=!0),this.unit=i,this.negativeValueAllowed=r,this._value=1,this.ignoreAdaptiveScaling=!1,this._value=e,this._originalUnit=i}return Object.defineProperty(t.prototype,"isPercentage",{get:function(){return this.unit===t.UNITMODE_PERCENTAGE},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isPixel",{get:function(){return this.unit===t.UNITMODE_PIXEL},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"internalValue",{get:function(){return this._value},enumerable:!1,configurable:!0}),t.prototype.getValueInPixel=function(t,e){return this.isPixel?this.getValue(t):this.getValue(t)*e},t.prototype.updateInPlace=function(e,i){return void 0===i&&(i=t.UNITMODE_PIXEL),this._value=e,this.unit=i,this},t.prototype.getValue=function(e){if(e&&!this.ignoreAdaptiveScaling&&this.unit!==t.UNITMODE_PERCENTAGE){var i=0,r=0;if(e.idealWidth&&(i=this._value*e.getSize().width/e.idealWidth),e.idealHeight&&(r=this._value*e.getSize().height/e.idealHeight),e.useSmallestIdeal&&e.idealWidth&&e.idealHeight)return window.innerWidth<window.innerHeight?i:r;if(e.idealWidth)return i;if(e.idealHeight)return r}return this._value},t.prototype.toString=function(e,i){switch(this.unit){case t.UNITMODE_PERCENTAGE:var r=100*this.getValue(e);return(i?r.toFixed(i):r)+"%";case t.UNITMODE_PIXEL:var o=this.getValue(e);return(i?o.toFixed(i):o)+"px"}return this.unit.toString()},t.prototype.fromString=function(e){var i=t._Regex.exec(e.toString());if(!i||0===i.length)return!1;var r=parseFloat(i[1]),o=this._originalUnit;if(this.negativeValueAllowed||r<0&&(r=0),4===i.length)switch(i[3]){case"px":o=t.UNITMODE_PIXEL;break;case"%":o=t.UNITMODE_PERCENTAGE,r/=100}return(r!==this._value||o!==this.unit)&&(this._value=r,this.unit=o,!0)},Object.defineProperty(t,"UNITMODE_PERCENTAGE",{get:function(){return t._UNITMODE_PERCENTAGE},enumerable:!1,configurable:!0}),Object.defineProperty(t,"UNITMODE_PIXEL",{get:function(){return t._UNITMODE_PIXEL},enumerable:!1,configurable:!0}),t._Regex=/(^-?\d*(\.\d+)?)(%|px)?/,t._UNITMODE_PERCENTAGE=0,t._UNITMODE_PIXEL=1,t}(),h=[new s.Vector2(0,0),new s.Vector2(0,0),new s.Vector2(0,0),new s.Vector2(0,0)],l=[new s.Vector2(0,0),new s.Vector2(0,0),new s.Vector2(0,0),new s.Vector2(0,0)],c=new s.Vector2(0,0),u=new s.Vector2(0,0),_=function(){function t(t,e,i,r){this.left=t,this.top=e,this.width=i,this.height=r}return t.prototype.copyFrom=function(t){this.left=t.left,this.top=t.top,this.width=t.width,this.height=t.height},t.prototype.copyFromFloats=function(t,e,i,r){this.left=t,this.top=e,this.width=i,this.height=r},t.CombineToRef=function(t,e,i){var r=Math.min(t.left,e.left),o=Math.min(t.top,e.top),n=Math.max(t.left+t.width,e.left+e.width),s=Math.max(t.top+t.height,e.top+e.height);i.left=r,i.top=o,i.width=n-r,i.height=s-o},t.prototype.addAndTransformToRef=function(t,e,i,r,o,n){var s=this.left+e,a=this.top+i,_=this.width+r,d=this.height+o;h[0].copyFromFloats(s,a),h[1].copyFromFloats(s+_,a),h[2].copyFromFloats(s+_,a+d),h[3].copyFromFloats(s,a+d),c.copyFromFloats(Number.MAX_VALUE,Number.MAX_VALUE),u.copyFromFloats(0,0);for(var p=0;p<4;p++)t.transformCoordinates(h[p].x,h[p].y,l[p]),c.x=Math.floor(Math.min(c.x,l[p].x)),c.y=Math.floor(Math.min(c.y,l[p].y)),u.x=Math.ceil(Math.max(u.x,l[p].x)),u.y=Math.ceil(Math.max(u.y,l[p].y));n.left=c.x,n.top=c.y,n.width=u.x-c.x,n.height=u.y-c.y},t.prototype.transformToRef=function(t,e){this.addAndTransformToRef(t,0,0,0,0,e)},t.prototype.isEqualsTo=function(t){return this.left===t.left&&(this.top===t.top&&(this.width===t.width&&this.height===t.height))},t.Empty=function(){return new t(0,0,0,0)},t}(),d=function(t){function e(e,i){void 0===i&&(i=0);var r=t.call(this,e.x,e.y)||this;return r.buttonIndex=i,r}return o(e,t),e}(s.Vector2),p=function(){function t(t,e,i,r,o,n){this.m=new Float32Array(6),this.fromValues(t,e,i,r,o,n)}return t.prototype.fromValues=function(t,e,i,r,o,n){return this.m[0]=t,this.m[1]=e,this.m[2]=i,this.m[3]=r,this.m[4]=o,this.m[5]=n,this},t.prototype.determinant=function(){return this.m[0]*this.m[3]-this.m[1]*this.m[2]},t.prototype.invertToRef=function(t){var e=this.m[0],i=this.m[1],r=this.m[2],o=this.m[3],n=this.m[4],a=this.m[5],h=this.determinant();if(h<s.Epsilon*s.Epsilon)return t.m[0]=0,t.m[1]=0,t.m[2]=0,t.m[3]=0,t.m[4]=0,t.m[5]=0,this;var l=1/h,c=r*a-o*n,u=i*n-e*a;return t.m[0]=o*l,t.m[1]=-i*l,t.m[2]=-r*l,t.m[3]=e*l,t.m[4]=c*l,t.m[5]=u*l,this},t.prototype.multiplyToRef=function(t,e){var i=this.m[0],r=this.m[1],o=this.m[2],n=this.m[3],s=this.m[4],a=this.m[5],h=t.m[0],l=t.m[1],c=t.m[2],u=t.m[3],_=t.m[4],d=t.m[5];return e.m[0]=i*h+r*c,e.m[1]=i*l+r*u,e.m[2]=o*h+n*c,e.m[3]=o*l+n*u,e.m[4]=s*h+a*c+_,e.m[5]=s*l+a*u+d,this},t.prototype.transformCoordinates=function(t,e,i){return i.x=t*this.m[0]+e*this.m[2]+this.m[4],i.y=t*this.m[1]+e*this.m[3]+this.m[5],this},t.Identity=function(){return new t(1,0,0,1,0,0)},t.TranslationToRef=function(t,e,i){i.fromValues(1,0,0,1,t,e)},t.ScalingToRef=function(t,e,i){i.fromValues(t,0,0,e,0,0)},t.RotationToRef=function(t,e){var i=Math.sin(t),r=Math.cos(t);e.fromValues(r,i,-i,r,0,0)},t.ComposeToRef=function(e,i,r,o,n,s,a){t.TranslationToRef(e,i,t._TempPreTranslationMatrix),t.ScalingToRef(o,n,t._TempScalingMatrix),t.RotationToRef(r,t._TempRotationMatrix),t.TranslationToRef(-e,-i,t._TempPostTranslationMatrix),t._TempPreTranslationMatrix.multiplyToRef(t._TempScalingMatrix,t._TempCompose0),t._TempCompose0.multiplyToRef(t._TempRotationMatrix,t._TempCompose1),s?(t._TempCompose1.multiplyToRef(t._TempPostTranslationMatrix,t._TempCompose2),t._TempCompose2.multiplyToRef(s,a)):t._TempCompose1.multiplyToRef(t._TempPostTranslationMatrix,a)},t._TempPreTranslationMatrix=t.Identity(),t._TempPostTranslationMatrix=t.Identity(),t._TempRotationMatrix=t.Identity(),t._TempScalingMatrix=t.Identity(),t._TempCompose0=t.Identity(),t._TempCompose1=t.Identity(),t._TempCompose2=t.Identity(),t}(),f=function(){function t(e){this.name=e,this._alpha=1,this._alphaSet=!1,this._zIndex=0,this._currentMeasure=_.Empty(),this._fontFamily="Arial",this._fontStyle="",this._fontWeight="",this._fontSize=new a(18,a.UNITMODE_PIXEL,!1),this._width=new a(1,a.UNITMODE_PERCENTAGE,!1),this._height=new a(1,a.UNITMODE_PERCENTAGE,!1),this._color="",this._style=null,this._horizontalAlignment=t.HORIZONTAL_ALIGNMENT_CENTER,this._verticalAlignment=t.VERTICAL_ALIGNMENT_CENTER,this._isDirty=!0,this._wasDirty=!1,this._tempParentMeasure=_.Empty(),this._prevCurrentMeasureTransformedIntoGlobalSpace=_.Empty(),this._cachedParentMeasure=_.Empty(),this._paddingLeft=new a(0),this._paddingRight=new a(0),this._paddingTop=new a(0),this._paddingBottom=new a(0),this._left=new a(0),this._top=new a(0),this._scaleX=1,this._scaleY=1,this._rotation=0,this._transformCenterX=.5,this._transformCenterY=.5,this._transformMatrix=p.Identity(),this._invertTransformMatrix=p.Identity(),this._transformedPosition=s.Vector2.Zero(),this._isMatrixDirty=!0,this._isVisible=!0,this._isHighlighted=!1,this._fontSet=!1,this._dummyVector2=s.Vector2.Zero(),this._downCount=0,this._enterCount=-1,this._doNotRender=!1,this._downPointerIds={},this._isEnabled=!0,this._disabledColor="#9a9a9a",this._disabledColorItem="#6a6a6a",this._rebuildLayout=!1,this._customData={},this._isClipped=!1,this._automaticSize=!1,this.metadata=null,this.isHitTestVisible=!0,this.isPointerBlocker=!1,this.isFocusInvisible=!1,this.clipChildren=!0,this.clipContent=!0,this.useBitmapCache=!1,this._shadowOffsetX=0,this._shadowOffsetY=0,this._shadowBlur=0,this._previousShadowBlur=0,this._shadowColor="black",this.hoverCursor="",this._linkOffsetX=new a(0),this._linkOffsetY=new a(0),this.onWheelObservable=new s.Observable,this.onPointerMoveObservable=new s.Observable,this.onPointerOutObservable=new s.Observable,this.onPointerDownObservable=new s.Observable,this.onPointerUpObservable=new s.Observable,this.onPointerClickObservable=new s.Observable,this.onPointerEnterObservable=new s.Observable,this.onDirtyObservable=new s.Observable,this.onBeforeDrawObservable=new s.Observable,this.onAfterDrawObservable=new s.Observable,this.onDisposeObservable=new s.Observable,this.fixedRatio=0,this._fixedRatioMasterIsWidth=!0,this._tmpMeasureA=new _(0,0,0,0)}return Object.defineProperty(t.prototype,"shadowOffsetX",{get:function(){return this._shadowOffsetX},set:function(t){this._shadowOffsetX!==t&&(this._shadowOffsetX=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"shadowOffsetY",{get:function(){return this._shadowOffsetY},set:function(t){this._shadowOffsetY!==t&&(this._shadowOffsetY=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"shadowBlur",{get:function(){return this._shadowBlur},set:function(t){this._shadowBlur!==t&&(this._previousShadowBlur=this._shadowBlur,this._shadowBlur=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"shadowColor",{get:function(){return this._shadowColor},set:function(t){this._shadowColor!==t&&(this._shadowColor=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"typeName",{get:function(){return this._getTypeName()},enumerable:!1,configurable:!0}),t.prototype.getClassName=function(){return this._getTypeName()},Object.defineProperty(t.prototype,"host",{get:function(){return this._host},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontOffset",{get:function(){return this._fontOffset},set:function(t){this._fontOffset=t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"alpha",{get:function(){return this._alpha},set:function(t){this._alpha!==t&&(this._alphaSet=!0,this._alpha=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isHighlighted",{get:function(){return this._isHighlighted},set:function(t){this._isHighlighted!==t&&(this._isHighlighted=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"scaleX",{get:function(){return this._scaleX},set:function(t){this._scaleX!==t&&(this._scaleX=t,this._markAsDirty(),this._markMatrixAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"scaleY",{get:function(){return this._scaleY},set:function(t){this._scaleY!==t&&(this._scaleY=t,this._markAsDirty(),this._markMatrixAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"rotation",{get:function(){return this._rotation},set:function(t){this._rotation!==t&&(this._rotation=t,this._markAsDirty(),this._markMatrixAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"transformCenterY",{get:function(){return this._transformCenterY},set:function(t){this._transformCenterY!==t&&(this._transformCenterY=t,this._markAsDirty(),this._markMatrixAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"transformCenterX",{get:function(){return this._transformCenterX},set:function(t){this._transformCenterX!==t&&(this._transformCenterX=t,this._markAsDirty(),this._markMatrixAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"horizontalAlignment",{get:function(){return this._horizontalAlignment},set:function(t){this._horizontalAlignment!==t&&(this._horizontalAlignment=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"verticalAlignment",{get:function(){return this._verticalAlignment},set:function(t){this._verticalAlignment!==t&&(this._verticalAlignment=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"width",{get:function(){return this._width.toString(this._host)},set:function(t){this._fixedRatioMasterIsWidth=!0,this._width.toString(this._host)!==t&&this._width.fromString(t)&&this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"widthInPixels",{get:function(){return this._width.getValueInPixel(this._host,this._cachedParentMeasure.width)},set:function(t){isNaN(t)||(this._fixedRatioMasterIsWidth=!0,this.width=t+"px")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"height",{get:function(){return this._height.toString(this._host)},set:function(t){this._fixedRatioMasterIsWidth=!1,this._height.toString(this._host)!==t&&this._height.fromString(t)&&this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"heightInPixels",{get:function(){return this._height.getValueInPixel(this._host,this._cachedParentMeasure.height)},set:function(t){isNaN(t)||(this._fixedRatioMasterIsWidth=!1,this.height=t+"px")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontFamily",{get:function(){return this._fontSet?this._fontFamily:""},set:function(t){this._fontFamily!==t&&(this._fontFamily=t,this._resetFontCache())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontStyle",{get:function(){return this._fontStyle},set:function(t){this._fontStyle!==t&&(this._fontStyle=t,this._resetFontCache())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontWeight",{get:function(){return this._fontWeight},set:function(t){this._fontWeight!==t&&(this._fontWeight=t,this._resetFontCache())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"style",{get:function(){return this._style},set:function(t){var e=this;this._style&&(this._style.onChangedObservable.remove(this._styleObserver),this._styleObserver=null),this._style=t,this._style&&(this._styleObserver=this._style.onChangedObservable.add((function(){e._markAsDirty(),e._resetFontCache()}))),this._markAsDirty(),this._resetFontCache()},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"_isFontSizeInPercentage",{get:function(){return this._fontSize.isPercentage},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontSizeInPixels",{get:function(){var t=this._style?this._style._fontSize:this._fontSize;return t.isPixel?t.getValue(this._host):t.getValueInPixel(this._host,this._tempParentMeasure.height||this._cachedParentMeasure.height)},set:function(t){isNaN(t)||(this.fontSize=t+"px")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontSize",{get:function(){return this._fontSize.toString(this._host)},set:function(t){this._fontSize.toString(this._host)!==t&&this._fontSize.fromString(t)&&(this._markAsDirty(),this._resetFontCache())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"color",{get:function(){return this._color},set:function(t){this._color!==t&&(this._color=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"zIndex",{get:function(){return this._zIndex},set:function(t){this.zIndex!==t&&(this._zIndex=t,this.parent&&this.parent._reOrderControl(this))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"notRenderable",{get:function(){return this._doNotRender},set:function(t){this._doNotRender!==t&&(this._doNotRender=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isVisible",{get:function(){return this._isVisible},set:function(t){this._isVisible!==t&&(this._isVisible=t,this._markAsDirty(!0))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isDirty",{get:function(){return this._isDirty},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"linkedMesh",{get:function(){return this._linkedMesh},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"paddingLeft",{get:function(){return this._paddingLeft.toString(this._host)},set:function(t){this._paddingLeft.fromString(t)&&this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"paddingLeftInPixels",{get:function(){return this._paddingLeft.getValueInPixel(this._host,this._cachedParentMeasure.width)},set:function(t){isNaN(t)||(this.paddingLeft=t+"px")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"paddingRight",{get:function(){return this._paddingRight.toString(this._host)},set:function(t){this._paddingRight.fromString(t)&&this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"paddingRightInPixels",{get:function(){return this._paddingRight.getValueInPixel(this._host,this._cachedParentMeasure.width)},set:function(t){isNaN(t)||(this.paddingRight=t+"px")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"paddingTop",{get:function(){return this._paddingTop.toString(this._host)},set:function(t){this._paddingTop.fromString(t)&&this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"paddingTopInPixels",{get:function(){return this._paddingTop.getValueInPixel(this._host,this._cachedParentMeasure.height)},set:function(t){isNaN(t)||(this.paddingTop=t+"px")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"paddingBottom",{get:function(){return this._paddingBottom.toString(this._host)},set:function(t){this._paddingBottom.fromString(t)&&this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"paddingBottomInPixels",{get:function(){return this._paddingBottom.getValueInPixel(this._host,this._cachedParentMeasure.height)},set:function(t){isNaN(t)||(this.paddingBottom=t+"px")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"left",{get:function(){return this._left.toString(this._host)},set:function(t){this._left.fromString(t)&&this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"leftInPixels",{get:function(){return this._left.getValueInPixel(this._host,this._cachedParentMeasure.width)},set:function(t){isNaN(t)||(this.left=t+"px")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"top",{get:function(){return this._top.toString(this._host)},set:function(t){this._top.fromString(t)&&this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"topInPixels",{get:function(){return this._top.getValueInPixel(this._host,this._cachedParentMeasure.height)},set:function(t){isNaN(t)||(this.top=t+"px")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"linkOffsetX",{get:function(){return this._linkOffsetX.toString(this._host)},set:function(t){this._linkOffsetX.fromString(t)&&this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"linkOffsetXInPixels",{get:function(){return this._linkOffsetX.getValueInPixel(this._host,this._cachedParentMeasure.width)},set:function(t){isNaN(t)||(this.linkOffsetX=t+"px")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"linkOffsetY",{get:function(){return this._linkOffsetY.toString(this._host)},set:function(t){this._linkOffsetY.fromString(t)&&this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"linkOffsetYInPixels",{get:function(){return this._linkOffsetY.getValueInPixel(this._host,this._cachedParentMeasure.height)},set:function(t){isNaN(t)||(this.linkOffsetY=t+"px")},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"centerX",{get:function(){return this._currentMeasure.left+this._currentMeasure.width/2},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"centerY",{get:function(){return this._currentMeasure.top+this._currentMeasure.height/2},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isEnabled",{get:function(){return this._isEnabled},set:function(t){this._isEnabled!==t&&(this._isEnabled=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"disabledColor",{get:function(){return this._disabledColor},set:function(t){this._disabledColor!==t&&(this._disabledColor=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"disabledColorItem",{get:function(){return this._disabledColorItem},set:function(t){this._disabledColorItem!==t&&(this._disabledColorItem=t,this._markAsDirty())},enumerable:!1,configurable:!0}),t.prototype._getTypeName=function(){return"Control"},t.prototype.getAscendantOfClass=function(t){return this.parent?this.parent.getClassName()===t?this.parent:this.parent.getAscendantOfClass(t):null},t.prototype._resetFontCache=function(){this._fontSet=!0,this._markAsDirty()},t.prototype.isAscendant=function(t){return!!this.parent&&(this.parent===t||this.parent.isAscendant(t))},t.prototype.getLocalCoordinates=function(t){var e=s.Vector2.Zero();return this.getLocalCoordinatesToRef(t,e),e},t.prototype.getLocalCoordinatesToRef=function(t,e){return e.x=t.x-this._currentMeasure.left,e.y=t.y-this._currentMeasure.top,this},t.prototype.getParentLocalCoordinates=function(t){var e=s.Vector2.Zero();return e.x=t.x-this._cachedParentMeasure.left,e.y=t.y-this._cachedParentMeasure.top,e},t.prototype.moveToVector3=function(e,i){if(this._host&&this.parent===this._host._rootContainer){this.horizontalAlignment=t.HORIZONTAL_ALIGNMENT_LEFT,this.verticalAlignment=t.VERTICAL_ALIGNMENT_TOP;var r=this._host._getGlobalViewport(i),o=s.Vector3.Project(e,s.Matrix.Identity(),i.getTransformMatrix(),r);this._moveToProjectedPosition(o),o.z<0||o.z>1?this.notRenderable=!0:this.notRenderable=!1}else s.Tools.Error("Cannot move a control to a vector3 if the control is not at root level")},t.prototype.getDescendantsToRef=function(t,e,i){void 0===e&&(e=!1)},t.prototype.getDescendants=function(t,e){var i=new Array;return this.getDescendantsToRef(i,t,e),i},t.prototype.linkWithMesh=function(e){if(!this._host||this.parent&&this.parent!==this._host._rootContainer)e&&s.Tools.Error("Cannot link a control to a mesh if the control is not at root level");else{var i=this._host._linkedControls.indexOf(this);if(-1!==i)return this._linkedMesh=e,void(e||this._host._linkedControls.splice(i,1));e&&(this.horizontalAlignment=t.HORIZONTAL_ALIGNMENT_LEFT,this.verticalAlignment=t.VERTICAL_ALIGNMENT_TOP,this._linkedMesh=e,this._host._linkedControls.push(this))}},t.prototype.setPadding=function(t,e,i,r){var o=t,n=null!=e?e:o,s=null!=i?i:o,a=null!=r?r:n;this.paddingTop=o,this.paddingRight=n,this.paddingBottom=s,this.paddingLeft=a},t.prototype.setPaddingInPixels=function(t,e,i,r){var o=t,n=null!=e?e:o,s=null!=i?i:o,a=null!=r?r:n;this.paddingTopInPixels=o,this.paddingRightInPixels=n,this.paddingBottomInPixels=s,this.paddingLeftInPixels=a},t.prototype._moveToProjectedPosition=function(t){var e=this._left.getValue(this._host),i=this._top.getValue(this._host),r=t.x+this._linkOffsetX.getValue(this._host)-this._currentMeasure.width/2,o=t.y+this._linkOffsetY.getValue(this._host)-this._currentMeasure.height/2;this._left.ignoreAdaptiveScaling&&this._top.ignoreAdaptiveScaling&&(Math.abs(r-e)<.5&&(r=e),Math.abs(o-i)<.5&&(o=i)),this.left=r+"px",this.top=o+"px",this._left.ignoreAdaptiveScaling=!0,this._top.ignoreAdaptiveScaling=!0,this._markAsDirty()},t.prototype._offsetLeft=function(t){this._isDirty=!0,this._currentMeasure.left+=t},t.prototype._offsetTop=function(t){this._isDirty=!0,this._currentMeasure.top+=t},t.prototype._markMatrixAsDirty=function(){this._isMatrixDirty=!0,this._flagDescendantsAsMatrixDirty()},t.prototype._flagDescendantsAsMatrixDirty=function(){},t.prototype._intersectsRect=function(t){return this._currentMeasure.transformToRef(this._transformMatrix,this._tmpMeasureA),!(this._tmpMeasureA.left>=t.left+t.width)&&(!(this._tmpMeasureA.top>=t.top+t.height)&&(!(this._tmpMeasureA.left+this._tmpMeasureA.width<=t.left)&&!(this._tmpMeasureA.top+this._tmpMeasureA.height<=t.top)))},t.prototype.invalidateRect=function(){if(this._transform(),this.host&&this.host.useInvalidateRectOptimization){this._currentMeasure.transformToRef(this._transformMatrix,this._tmpMeasureA),_.CombineToRef(this._tmpMeasureA,this._prevCurrentMeasureTransformedIntoGlobalSpace,this._tmpMeasureA);var t=this.shadowOffsetX,e=this.shadowOffsetY,i=Math.max(this._previousShadowBlur,this.shadowBlur),r=Math.min(Math.min(t,0)-2*i,0),o=Math.max(Math.max(t,0)+2*i,0),n=Math.min(Math.min(e,0)-2*i,0),s=Math.max(Math.max(e,0)+2*i,0);this.host.invalidateRect(Math.floor(this._tmpMeasureA.left+r),Math.floor(this._tmpMeasureA.top+n),Math.ceil(this._tmpMeasureA.left+this._tmpMeasureA.width+o),Math.ceil(this._tmpMeasureA.top+this._tmpMeasureA.height+s))}},t.prototype._markAsDirty=function(t){void 0===t&&(t=!1),(this._isVisible||t)&&(this._isDirty=!0,this._host&&this._host.markAsDirty())},t.prototype._markAllAsDirty=function(){this._markAsDirty(),this._font&&this._prepareFont()},t.prototype._link=function(t){this._host=t,this._host&&(this.uniqueId=this._host.getScene().getUniqueId())},t.prototype._transform=function(t){if(this._isMatrixDirty||1!==this._scaleX||1!==this._scaleY||0!==this._rotation){var e=this._currentMeasure.width*this._transformCenterX+this._currentMeasure.left,i=this._currentMeasure.height*this._transformCenterY+this._currentMeasure.top;t&&(t.translate(e,i),t.rotate(this._rotation),t.scale(this._scaleX,this._scaleY),t.translate(-e,-i)),(this._isMatrixDirty||this._cachedOffsetX!==e||this._cachedOffsetY!==i)&&(this._cachedOffsetX=e,this._cachedOffsetY=i,this._isMatrixDirty=!1,this._flagDescendantsAsMatrixDirty(),p.ComposeToRef(-e,-i,this._rotation,this._scaleX,this._scaleY,this.parent?this.parent._transformMatrix:null,this._transformMatrix),this._transformMatrix.invertToRef(this._invertTransformMatrix))}},t.prototype._renderHighlight=function(t){this.isHighlighted&&(t.save(),t.strokeStyle="#4affff",t.lineWidth=2,this._renderHighlightSpecific(t),t.restore())},t.prototype._renderHighlightSpecific=function(t){t.strokeRect(this._currentMeasure.left,this._currentMeasure.top,this._currentMeasure.width,this._currentMeasure.height)},t.prototype._applyStates=function(e){this._isFontSizeInPercentage&&(this._fontSet=!0),this._fontSet&&(this._prepareFont(),this._fontSet=!1),this._font&&(e.font=this._font),this._color&&(e.fillStyle=this._color),t.AllowAlphaInheritance?e.globalAlpha*=this._alpha:this._alphaSet&&(e.globalAlpha=this.parent?this.parent.alpha*this._alpha:this._alpha)},t.prototype._layout=function(t,e){if(!this.isDirty&&(!this.isVisible||this.notRenderable))return!1;if(this._isDirty||!this._cachedParentMeasure.isEqualsTo(t)){this.host._numLayoutCalls++,this._currentMeasure.addAndTransformToRef(this._transformMatrix,0|-this.paddingLeftInPixels,0|-this.paddingTopInPixels,0|this.paddingRightInPixels,0|this.paddingBottomInPixels,this._prevCurrentMeasureTransformedIntoGlobalSpace),e.save(),this._applyStates(e);var i=0;do{this._rebuildLayout=!1,this._processMeasures(t,e),i++}while(this._rebuildLayout&&i<3);i>=3&&s.Logger.Error("Layout cycle detected in GUI (Control name="+this.name+", uniqueId="+this.uniqueId+")"),e.restore(),this.invalidateRect(),this._evaluateClippingState(t)}return this._wasDirty=this._isDirty,this._isDirty=!1,!0},t.prototype._processMeasures=function(t,e){this._currentMeasure.copyFrom(t),this._preMeasure(t,e),this._measure(),this._computeAlignment(t,e),this._currentMeasure.left=0|this._currentMeasure.left,this._currentMeasure.top=0|this._currentMeasure.top,this._currentMeasure.width=0|this._currentMeasure.width,this._currentMeasure.height=0|this._currentMeasure.height,this._additionalProcessing(t,e),this._cachedParentMeasure.copyFrom(t),this.onDirtyObservable.hasObservers()&&this.onDirtyObservable.notifyObservers(this)},t.prototype._evaluateClippingState=function(t){if(this.parent&&this.parent.clipChildren){if(this._currentMeasure.left>t.left+t.width)return void(this._isClipped=!0);if(this._currentMeasure.left+this._currentMeasure.width<t.left)return void(this._isClipped=!0);if(this._currentMeasure.top>t.top+t.height)return void(this._isClipped=!0);if(this._currentMeasure.top+this._currentMeasure.height<t.top)return void(this._isClipped=!0)}this._isClipped=!1},t.prototype._measure=function(){this._width.isPixel?this._currentMeasure.width=this._width.getValue(this._host):this._currentMeasure.width*=this._width.getValue(this._host),this._height.isPixel?this._currentMeasure.height=this._height.getValue(this._host):this._currentMeasure.height*=this._height.getValue(this._host),0!==this.fixedRatio&&(this._fixedRatioMasterIsWidth?this._currentMeasure.height=this._currentMeasure.width*this.fixedRatio:this._currentMeasure.width=this._currentMeasure.height*this.fixedRatio)},t.prototype._computeAlignment=function(e,i){var r=this._currentMeasure.width,o=this._currentMeasure.height,n=e.width,s=e.height,a=0,h=0;switch(this.horizontalAlignment){case t.HORIZONTAL_ALIGNMENT_LEFT:a=0;break;case t.HORIZONTAL_ALIGNMENT_RIGHT:a=n-r;break;case t.HORIZONTAL_ALIGNMENT_CENTER:a=(n-r)/2}switch(this.verticalAlignment){case t.VERTICAL_ALIGNMENT_TOP:h=0;break;case t.VERTICAL_ALIGNMENT_BOTTOM:h=s-o;break;case t.VERTICAL_ALIGNMENT_CENTER:h=(s-o)/2}this._paddingLeft.isPixel?(this._currentMeasure.left+=this._paddingLeft.getValue(this._host),this._currentMeasure.width-=this._paddingLeft.getValue(this._host)):(this._currentMeasure.left+=n*this._paddingLeft.getValue(this._host),this._currentMeasure.width-=n*this._paddingLeft.getValue(this._host)),this._paddingRight.isPixel?this._currentMeasure.width-=this._paddingRight.getValue(this._host):this._currentMeasure.width-=n*this._paddingRight.getValue(this._host),this._paddingTop.isPixel?(this._currentMeasure.top+=this._paddingTop.getValue(this._host),this._currentMeasure.height-=this._paddingTop.getValue(this._host)):(this._currentMeasure.top+=s*this._paddingTop.getValue(this._host),this._currentMeasure.height-=s*this._paddingTop.getValue(this._host)),this._paddingBottom.isPixel?this._currentMeasure.height-=this._paddingBottom.getValue(this._host):this._currentMeasure.height-=s*this._paddingBottom.getValue(this._host),this._left.isPixel?this._currentMeasure.left+=this._left.getValue(this._host):this._currentMeasure.left+=n*this._left.getValue(this._host),this._top.isPixel?this._currentMeasure.top+=this._top.getValue(this._host):this._currentMeasure.top+=s*this._top.getValue(this._host),this._currentMeasure.left+=a,this._currentMeasure.top+=h},t.prototype._preMeasure=function(t,e){},t.prototype._additionalProcessing=function(t,e){},t.prototype._clipForChildren=function(t){},t.prototype._clip=function(e,i){if(e.beginPath(),t._ClipMeasure.copyFrom(this._currentMeasure),i){i.transformToRef(this._invertTransformMatrix,this._tmpMeasureA);var r=new _(0,0,0,0);r.left=Math.max(this._tmpMeasureA.left,this._currentMeasure.left),r.top=Math.max(this._tmpMeasureA.top,this._currentMeasure.top),r.width=Math.min(this._tmpMeasureA.left+this._tmpMeasureA.width,this._currentMeasure.left+this._currentMeasure.width)-r.left,r.height=Math.min(this._tmpMeasureA.top+this._tmpMeasureA.height,this._currentMeasure.top+this._currentMeasure.height)-r.top,t._ClipMeasure.copyFrom(r)}if(this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY){var o=this.shadowOffsetX,n=this.shadowOffsetY,s=this.shadowBlur,a=Math.min(Math.min(o,0)-2*s,0),h=Math.max(Math.max(o,0)+2*s,0),l=Math.min(Math.min(n,0)-2*s,0),c=Math.max(Math.max(n,0)+2*s,0);e.rect(t._ClipMeasure.left+a,t._ClipMeasure.top+l,t._ClipMeasure.width+h-a,t._ClipMeasure.height+c-l)}else e.rect(t._ClipMeasure.left,t._ClipMeasure.top,t._ClipMeasure.width,t._ClipMeasure.height);e.clip()},t.prototype._render=function(t,e){return!this.isVisible||this.notRenderable||this._isClipped?(this._isDirty=!1,!1):(this.host._numRenderCalls++,t.save(),this._applyStates(t),this._transform(t),this.clipContent&&this._clip(t,e),this.onBeforeDrawObservable.hasObservers()&&this.onBeforeDrawObservable.notifyObservers(this),this.useBitmapCache&&!this._wasDirty&&this._cacheData?t.putImageData(this._cacheData,this._currentMeasure.left,this._currentMeasure.top):this._draw(t,e),this.useBitmapCache&&this._wasDirty&&(this._cacheData=t.getImageData(this._currentMeasure.left,this._currentMeasure.top,this._currentMeasure.width,this._currentMeasure.height)),this._renderHighlight(t),this.onAfterDrawObservable.hasObservers()&&this.onAfterDrawObservable.notifyObservers(this),t.restore(),!0)},t.prototype._draw=function(t,e){},t.prototype.contains=function(t,e){return this._invertTransformMatrix.transformCoordinates(t,e,this._transformedPosition),t=this._transformedPosition.x,e=this._transformedPosition.y,!(t<this._currentMeasure.left)&&(!(t>this._currentMeasure.left+this._currentMeasure.width)&&(!(e<this._currentMeasure.top)&&(!(e>this._currentMeasure.top+this._currentMeasure.height)&&(this.isPointerBlocker&&(this._host._shouldBlockPointer=!0),!0))))},t.prototype._processPicking=function(t,e,i,r,o,n,s,a){return!!this._isEnabled&&(!(!this.isHitTestVisible||!this.isVisible||this._doNotRender)&&(!!this.contains(t,e)&&(this._processObservables(r,t,e,i,o,n,s,a),!0)))},t.prototype._onPointerMove=function(t,e,i,r){this.onPointerMoveObservable.notifyObservers(e,-1,t,this,r)&&null!=this.parent&&this.parent._onPointerMove(t,e,i,r)},t.prototype._onPointerEnter=function(t,e){return!!this._isEnabled&&(!(this._enterCount>0)&&(-1===this._enterCount&&(this._enterCount=0),this._enterCount++,this.onPointerEnterObservable.notifyObservers(this,-1,t,this,e)&&null!=this.parent&&this.parent._onPointerEnter(t,e),!0))},t.prototype._onPointerOut=function(t,e,i){if(void 0===i&&(i=!1),i||this._isEnabled&&t!==this){this._enterCount=0;var r=!0;t.isAscendant(this)||(r=this.onPointerOutObservable.notifyObservers(this,-1,t,this,e)),r&&null!=this.parent&&this.parent._onPointerOut(t,e,i)}},t.prototype._onPointerDown=function(t,e,i,r,o){return this._onPointerEnter(this,o),0===this._downCount&&(this._downCount++,this._downPointerIds[i]=!0,this.onPointerDownObservable.notifyObservers(new d(e,r),-1,t,this,o)&&null!=this.parent&&this.parent._onPointerDown(t,e,i,r,o),!0)},t.prototype._onPointerUp=function(t,e,i,r,o,n){if(this._isEnabled){this._downCount=0,delete this._downPointerIds[i];var s=o;o&&(this._enterCount>0||-1===this._enterCount)&&(s=this.onPointerClickObservable.notifyObservers(new d(e,r),-1,t,this,n)),this.onPointerUpObservable.notifyObservers(new d(e,r),-1,t,this,n)&&null!=this.parent&&this.parent._onPointerUp(t,e,i,r,s,n)}},t.prototype._forcePointerUp=function(t){if(void 0===t&&(t=null),null!==t)this._onPointerUp(this,s.Vector2.Zero(),t,0,!0);else for(var e in this._downPointerIds)this._onPointerUp(this,s.Vector2.Zero(),+e,0,!0)},t.prototype._onWheelScroll=function(t,e){this._isEnabled&&(this.onWheelObservable.notifyObservers(new s.Vector2(t,e))&&null!=this.parent&&this.parent._onWheelScroll(t,e))},t.prototype._onCanvasBlur=function(){},t.prototype._processObservables=function(t,e,i,r,o,n,a,h){if(!this._isEnabled)return!1;if(this._dummyVector2.copyFromFloats(e,i),t===s.PointerEventTypes.POINTERMOVE){this._onPointerMove(this,this._dummyVector2,o,r);var l=this._host._lastControlOver[o];return l&&l!==this&&l._onPointerOut(this,r),l!==this&&this._onPointerEnter(this,r),this._host._lastControlOver[o]=this,!0}return t===s.PointerEventTypes.POINTERDOWN?(this._onPointerDown(this,this._dummyVector2,o,n,r),this._host._registerLastControlDown(this,o),this._host._lastPickedControl=this,!0):t===s.PointerEventTypes.POINTERUP?(this._host._lastControlDown[o]&&this._host._lastControlDown[o]._onPointerUp(this,this._dummyVector2,o,n,!0,r),delete this._host._lastControlDown[o],!0):!(t!==s.PointerEventTypes.POINTERWHEEL||!this._host._lastControlOver[o])&&(this._host._lastControlOver[o]._onWheelScroll(a,h),!0)},t.prototype._prepareFont=function(){(this._font||this._fontSet)&&(this._style?this._font=this._style.fontStyle+" "+this._style.fontWeight+" "+this.fontSizeInPixels+"px "+this._style.fontFamily:this._font=this._fontStyle+" "+this._fontWeight+" "+this.fontSizeInPixels+"px "+this._fontFamily,this._fontOffset=t._GetFontOffset(this._font))},t.prototype.serialize=function(t){s.SerializationHelper.Serialize(this,t),t.name=this.name,t.className=this.getClassName(),this._font&&(t.fontFamily=this.fontFamily,t.fontSize=this.fontSize,t.fontWeight=this.fontWeight,t.fontStyle=this.fontStyle)},t.prototype._parseFromContent=function(t,e){t.fontFamily&&(this.fontFamily=t.fontFamily),t.fontSize&&(this.fontSize=t.fontSize),t.fontWeight&&(this.fontWeight=t.fontWeight),t.fontStyle&&(this.fontStyle=t.fontStyle)},t.prototype.dispose=function(){(this.onDirtyObservable.clear(),this.onBeforeDrawObservable.clear(),this.onAfterDrawObservable.clear(),this.onPointerDownObservable.clear(),this.onPointerEnterObservable.clear(),this.onPointerMoveObservable.clear(),this.onPointerOutObservable.clear(),this.onPointerUpObservable.clear(),this.onPointerClickObservable.clear(),this.onWheelObservable.clear(),this._styleObserver&&this._style&&(this._style.onChangedObservable.remove(this._styleObserver),this._styleObserver=null),this.parent&&(this.parent.removeControl(this),this.parent=null),this._host)&&(this._host._linkedControls.indexOf(this)>-1&&this.linkWithMesh(null));this.onDisposeObservable.notifyObservers(this),this.onDisposeObservable.clear()},Object.defineProperty(t,"HORIZONTAL_ALIGNMENT_LEFT",{get:function(){return t._HORIZONTAL_ALIGNMENT_LEFT},enumerable:!1,configurable:!0}),Object.defineProperty(t,"HORIZONTAL_ALIGNMENT_RIGHT",{get:function(){return t._HORIZONTAL_ALIGNMENT_RIGHT},enumerable:!1,configurable:!0}),Object.defineProperty(t,"HORIZONTAL_ALIGNMENT_CENTER",{get:function(){return t._HORIZONTAL_ALIGNMENT_CENTER},enumerable:!1,configurable:!0}),Object.defineProperty(t,"VERTICAL_ALIGNMENT_TOP",{get:function(){return t._VERTICAL_ALIGNMENT_TOP},enumerable:!1,configurable:!0}),Object.defineProperty(t,"VERTICAL_ALIGNMENT_BOTTOM",{get:function(){return t._VERTICAL_ALIGNMENT_BOTTOM},enumerable:!1,configurable:!0}),Object.defineProperty(t,"VERTICAL_ALIGNMENT_CENTER",{get:function(){return t._VERTICAL_ALIGNMENT_CENTER},enumerable:!1,configurable:!0}),t._GetFontOffset=function(e){if(t._FontHeightSizes[e])return t._FontHeightSizes[e];var i=document.createElement("span");i.innerHTML="Hg",i.setAttribute("style","font: "+e+" !important");var r=document.createElement("div");r.style.display="inline-block",r.style.width="1px",r.style.height="0px",r.style.verticalAlign="bottom";var o=document.createElement("div");o.style.whiteSpace="nowrap",o.appendChild(i),o.appendChild(r),document.body.appendChild(o);var n=0,s=0;try{s=r.getBoundingClientRect().top-i.getBoundingClientRect().top,r.style.verticalAlign="baseline",n=r.getBoundingClientRect().top-i.getBoundingClientRect().top}finally{document.body.removeChild(o)}var a={ascent:n,height:s,descent:s-n};return t._FontHeightSizes[e]=a,a},t.Parse=function(t,e){var i=s.Tools.Instantiate("BABYLON.GUI."+t.className),r=s.SerializationHelper.Parse((function(){return new i}),t,null);return r.name=t.name,r._parseFromContent(t,e),r},t.drawEllipse=function(t,e,i,r,o){o.translate(t,e),o.scale(i,r),o.beginPath(),o.arc(0,0,1,0,2*Math.PI),o.closePath(),o.scale(1/i,1/r),o.translate(-t,-e)},t.AllowAlphaInheritance=!1,t._ClipMeasure=new _(0,0,0,0),t._HORIZONTAL_ALIGNMENT_LEFT=0,t._HORIZONTAL_ALIGNMENT_RIGHT=1,t._HORIZONTAL_ALIGNMENT_CENTER=2,t._VERTICAL_ALIGNMENT_TOP=0,t._VERTICAL_ALIGNMENT_BOTTOM=1,t._VERTICAL_ALIGNMENT_CENTER=2,t._FontHeightSizes={},t.AddHeader=function(){},n([Object(s.serialize)()],t.prototype,"metadata",void 0),n([Object(s.serialize)()],t.prototype,"isHitTestVisible",void 0),n([Object(s.serialize)()],t.prototype,"isPointerBlocker",void 0),n([Object(s.serialize)()],t.prototype,"isFocusInvisible",void 0),n([Object(s.serialize)()],t.prototype,"clipChildren",void 0),n([Object(s.serialize)()],t.prototype,"clipContent",void 0),n([Object(s.serialize)()],t.prototype,"useBitmapCache",void 0),n([Object(s.serialize)()],t.prototype,"shadowOffsetX",null),n([Object(s.serialize)()],t.prototype,"shadowOffsetY",null),n([Object(s.serialize)()],t.prototype,"shadowBlur",null),n([Object(s.serialize)()],t.prototype,"shadowColor",null),n([Object(s.serialize)()],t.prototype,"hoverCursor",void 0),n([Object(s.serialize)()],t.prototype,"fontOffset",null),n([Object(s.serialize)()],t.prototype,"alpha",null),n([Object(s.serialize)()],t.prototype,"scaleX",null),n([Object(s.serialize)()],t.prototype,"scaleY",null),n([Object(s.serialize)()],t.prototype,"rotation",null),n([Object(s.serialize)()],t.prototype,"transformCenterY",null),n([Object(s.serialize)()],t.prototype,"transformCenterX",null),n([Object(s.serialize)()],t.prototype,"horizontalAlignment",null),n([Object(s.serialize)()],t.prototype,"verticalAlignment",null),n([Object(s.serialize)()],t.prototype,"fixedRatio",void 0),n([Object(s.serialize)()],t.prototype,"width",null),n([Object(s.serialize)()],t.prototype,"height",null),n([Object(s.serialize)()],t.prototype,"style",null),n([Object(s.serialize)()],t.prototype,"color",null),n([Object(s.serialize)()],t.prototype,"zIndex",null),n([Object(s.serialize)()],t.prototype,"notRenderable",null),n([Object(s.serialize)()],t.prototype,"isVisible",null),n([Object(s.serialize)()],t.prototype,"paddingLeft",null),n([Object(s.serialize)()],t.prototype,"paddingRight",null),n([Object(s.serialize)()],t.prototype,"paddingTop",null),n([Object(s.serialize)()],t.prototype,"paddingBottom",null),n([Object(s.serialize)()],t.prototype,"left",null),n([Object(s.serialize)()],t.prototype,"top",null),n([Object(s.serialize)()],t.prototype,"linkOffsetX",null),n([Object(s.serialize)()],t.prototype,"linkOffsetY",null),n([Object(s.serialize)()],t.prototype,"isEnabled",null),n([Object(s.serialize)()],t.prototype,"disabledColor",null),n([Object(s.serialize)()],t.prototype,"disabledColorItem",null),t}();s._TypeStore.RegisteredTypes["BABYLON.GUI.Control"]=f;var g=function(t){function e(e){var i=t.call(this,e)||this;return i.name=e,i._children=new Array,i._measureForChildren=_.Empty(),i._background="",i._adaptWidthToChildren=!1,i._adaptHeightToChildren=!1,i.logLayoutCycleErrors=!1,i.maxLayoutCycle=3,i}return o(e,t),Object.defineProperty(e.prototype,"adaptHeightToChildren",{get:function(){return this._adaptHeightToChildren},set:function(t){this._adaptHeightToChildren!==t&&(this._adaptHeightToChildren=t,t&&(this.height="100%"),this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"adaptWidthToChildren",{get:function(){return this._adaptWidthToChildren},set:function(t){this._adaptWidthToChildren!==t&&(this._adaptWidthToChildren=t,t&&(this.width="100%"),this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"background",{get:function(){return this._background},set:function(t){this._background!==t&&(this._background=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"children",{get:function(){return this._children},enumerable:!1,configurable:!0}),e.prototype._getTypeName=function(){return"Container"},e.prototype._flagDescendantsAsMatrixDirty=function(){for(var t=0,e=this.children;t<e.length;t++){e[t]._markMatrixAsDirty()}},e.prototype.getChildByName=function(t){for(var e=0,i=this.children;e<i.length;e++){var r=i[e];if(r.name===t)return r}return null},e.prototype.getChildByType=function(t,e){for(var i=0,r=this.children;i<r.length;i++){var o=r[i];if(o.typeName===e)return o}return null},e.prototype.containsControl=function(t){return-1!==this.children.indexOf(t)},e.prototype.addControl=function(t){return t?(-1!==this._children.indexOf(t)||(t._link(this._host),t._markAllAsDirty(),this._reOrderControl(t),this._markAsDirty()),this):this},e.prototype.clearControls=function(){for(var t=0,e=this.children.slice();t<e.length;t++){var i=e[t];this.removeControl(i)}return this},e.prototype.removeControl=function(t){var e=this._children.indexOf(t);return-1!==e&&(this._children.splice(e,1),t.parent=null),t.linkWithMesh(null),this._host&&this._host._cleanControlAfterRemoval(t),this._markAsDirty(),this},e.prototype._reOrderControl=function(t){this.removeControl(t);for(var e=!1,i=0;i<this._children.length;i++)if(this._children[i].zIndex>t.zIndex){this._children.splice(i,0,t),e=!0;break}e||this._children.push(t),t.parent=this,this._markAsDirty()},e.prototype._offsetLeft=function(e){t.prototype._offsetLeft.call(this,e);for(var i=0,r=this._children;i<r.length;i++){r[i]._offsetLeft(e)}},e.prototype._offsetTop=function(e){t.prototype._offsetTop.call(this,e);for(var i=0,r=this._children;i<r.length;i++){r[i]._offsetTop(e)}},e.prototype._markAllAsDirty=function(){t.prototype._markAllAsDirty.call(this);for(var e=0;e<this._children.length;e++)this._children[e]._markAllAsDirty()},e.prototype._localDraw=function(t){this._background&&(t.save(),(this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY)&&(t.shadowColor=this.shadowColor,t.shadowBlur=this.shadowBlur,t.shadowOffsetX=this.shadowOffsetX,t.shadowOffsetY=this.shadowOffsetY),t.fillStyle=this._background,t.fillRect(this._currentMeasure.left,this._currentMeasure.top,this._currentMeasure.width,this._currentMeasure.height),t.restore())},e.prototype._link=function(e){t.prototype._link.call(this,e);for(var i=0,r=this._children;i<r.length;i++){r[i]._link(e)}},e.prototype._beforeLayout=function(){},e.prototype._processMeasures=function(e,i){!this._isDirty&&this._cachedParentMeasure.isEqualsTo(e)||(t.prototype._processMeasures.call(this,e,i),this._evaluateClippingState(e))},e.prototype._layout=function(t,e){if(!this.isDirty&&(!this.isVisible||this.notRenderable))return!1;this.host._numLayoutCalls++,this._isDirty&&this._currentMeasure.transformToRef(this._transformMatrix,this._prevCurrentMeasureTransformedIntoGlobalSpace);var i=0;e.save(),this._applyStates(e),this._beforeLayout();do{var r=-1,o=-1;if(this._rebuildLayout=!1,this._processMeasures(t,e),!this._isClipped){for(var n=0,a=this._children;n<a.length;n++){var h=a[n];h._tempParentMeasure.copyFrom(this._measureForChildren),h._layout(this._measureForChildren,e)&&(this.adaptWidthToChildren&&h._width.isPixel&&(r=Math.max(r,h._currentMeasure.width+h.paddingLeftInPixels+h.paddingRightInPixels)),this.adaptHeightToChildren&&h._height.isPixel&&(o=Math.max(o,h._currentMeasure.height+h.paddingTopInPixels+h.paddingBottomInPixels)))}this.adaptWidthToChildren&&r>=0&&(r+=this.paddingLeftInPixels+this.paddingRightInPixels,this.width!==r+"px"&&(this.width=r+"px",this._rebuildLayout=!0)),this.adaptHeightToChildren&&o>=0&&(o+=this.paddingTopInPixels+this.paddingBottomInPixels,this.height!==o+"px"&&(this.height=o+"px",this._rebuildLayout=!0)),this._postMeasure()}i++}while(this._rebuildLayout&&i<this.maxLayoutCycle);return i>=3&&this.logLayoutCycleErrors&&s.Logger.Error("Layout cycle detected in GUI (Container name="+this.name+", uniqueId="+this.uniqueId+")"),e.restore(),this._isDirty&&(this.invalidateRect(),this._isDirty=!1),!0},e.prototype._postMeasure=function(){},e.prototype._draw=function(t,e){this._localDraw(t),this.clipChildren&&this._clipForChildren(t);for(var i=0,r=this._children;i<r.length;i++){var o=r[i];e&&!o._intersectsRect(e)||o._render(t,e)}},e.prototype.getDescendantsToRef=function(t,e,i){if(void 0===e&&(e=!1),this.children)for(var r=0;r<this.children.length;r++){var o=this.children[r];i&&!i(o)||t.push(o),e||o.getDescendantsToRef(t,!1,i)}},e.prototype._processPicking=function(e,i,r,o,n,s,a,h){if(!this._isEnabled||!this.isVisible||this.notRenderable)return!1;if(!t.prototype.contains.call(this,e,i))return!1;for(var l=this._children.length-1;l>=0;l--){var c=this._children[l];if(c._processPicking(e,i,r,o,n,s,a,h))return c.hoverCursor&&this._host._changeCursor(c.hoverCursor),!0}return!!this.isHitTestVisible&&this._processObservables(o,e,i,r,n,s,a,h)},e.prototype._additionalProcessing=function(e,i){t.prototype._additionalProcessing.call(this,e,i),this._measureForChildren.copyFrom(this._currentMeasure)},e.prototype.serialize=function(e){if(t.prototype.serialize.call(this,e),this.children.length){e.children=[];for(var i=0,r=this.children;i<r.length;i++){var o={};r[i].serialize(o),e.children.push(o)}}},e.prototype.dispose=function(){t.prototype.dispose.call(this);for(var e=this.children.length-1;e>=0;e--)this.children[e].dispose()},e.prototype._parseFromContent=function(e,i){if(t.prototype._parseFromContent.call(this,e,i),this._link(i),e.children)for(var r=0,o=e.children;r<o.length;r++){var n=o[r];this.addControl(f.Parse(n,i))}},n([Object(s.serialize)()],e.prototype,"maxLayoutCycle",void 0),n([Object(s.serialize)()],e.prototype,"adaptHeightToChildren",null),n([Object(s.serialize)()],e.prototype,"adaptWidthToChildren",null),n([Object(s.serialize)()],e.prototype,"background",null),e}(f);s._TypeStore.RegisteredTypes["BABYLON.GUI.Container"]=g;var b,m=function(t){function e(e){var i=t.call(this,e)||this;return i.name=e,i._thickness=1,i._cornerRadius=0,i}return o(e,t),Object.defineProperty(e.prototype,"thickness",{get:function(){return this._thickness},set:function(t){this._thickness!==t&&(this._thickness=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"cornerRadius",{get:function(){return this._cornerRadius},set:function(t){t<0&&(t=0),this._cornerRadius!==t&&(this._cornerRadius=t,this._markAsDirty())},enumerable:!1,configurable:!0}),e.prototype._getTypeName=function(){return"Rectangle"},e.prototype._localDraw=function(t){t.save(),(this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY)&&(t.shadowColor=this.shadowColor,t.shadowBlur=this.shadowBlur,t.shadowOffsetX=this.shadowOffsetX,t.shadowOffsetY=this.shadowOffsetY),this._background&&(t.fillStyle=this._background,this._cornerRadius?(this._drawRoundedRect(t,this._thickness/2),t.fill()):t.fillRect(this._currentMeasure.left,this._currentMeasure.top,this._currentMeasure.width,this._currentMeasure.height)),this._thickness&&((this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY)&&(t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0),this.color&&(t.strokeStyle=this.color),t.lineWidth=this._thickness,this._cornerRadius?(this._drawRoundedRect(t,this._thickness/2),t.stroke()):t.strokeRect(this._currentMeasure.left+this._thickness/2,this._currentMeasure.top+this._thickness/2,this._currentMeasure.width-this._thickness,this._currentMeasure.height-this._thickness)),t.restore()},e.prototype._additionalProcessing=function(e,i){t.prototype._additionalProcessing.call(this,e,i),this._measureForChildren.width-=2*this._thickness,this._measureForChildren.height-=2*this._thickness,this._measureForChildren.left+=this._thickness,this._measureForChildren.top+=this._thickness},e.prototype._drawRoundedRect=function(t,e){void 0===e&&(e=0);var i=this._currentMeasure.left+e,r=this._currentMeasure.top+e,o=this._currentMeasure.width-2*e,n=this._currentMeasure.height-2*e,s=Math.min(n/2-2,Math.min(o/2-2,this._cornerRadius));t.beginPath(),t.moveTo(i+s,r),t.lineTo(i+o-s,r),t.quadraticCurveTo(i+o,r,i+o,r+s),t.lineTo(i+o,r+n-s),t.quadraticCurveTo(i+o,r+n,i+o-s,r+n),t.lineTo(i+s,r+n),t.quadraticCurveTo(i,r+n,i,r+n-s),t.lineTo(i,r+s),t.quadraticCurveTo(i,r,i+s,r),t.closePath()},e.prototype._clipForChildren=function(t){this._cornerRadius&&(this._drawRoundedRect(t,this._thickness),t.clip())},n([Object(s.serialize)()],e.prototype,"thickness",null),n([Object(s.serialize)()],e.prototype,"cornerRadius",null),e}(g);s._TypeStore.RegisteredTypes["BABYLON.GUI.Rectangle"]=m,function(t){t[t.Clip=0]="Clip",t[t.WordWrap=1]="WordWrap",t[t.Ellipsis=2]="Ellipsis"}(b||(b={}));var y=function(t){function e(e,i){void 0===i&&(i="");var r=t.call(this,e)||this;return r.name=e,r._text="",r._textWrapping=b.Clip,r._textHorizontalAlignment=f.HORIZONTAL_ALIGNMENT_CENTER,r._textVerticalAlignment=f.VERTICAL_ALIGNMENT_CENTER,r._resizeToFit=!1,r._lineSpacing=new a(0),r._outlineWidth=0,r._outlineColor="white",r._underline=!1,r._lineThrough=!1,r.onTextChangedObservable=new s.Observable,r.onLinesReadyObservable=new s.Observable,r.text=i,r}return o(e,t),Object.defineProperty(e.prototype,"lines",{get:function(){return this._lines},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"resizeToFit",{get:function(){return this._resizeToFit},set:function(t){this._resizeToFit!==t&&(this._resizeToFit=t,this._resizeToFit&&(this._width.ignoreAdaptiveScaling=!0,this._height.ignoreAdaptiveScaling=!0),this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"textWrapping",{get:function(){return this._textWrapping},set:function(t){this._textWrapping!==t&&(this._textWrapping=+t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"text",{get:function(){return this._text},set:function(t){this._text!==t&&(this._text=t,this._markAsDirty(),this.onTextChangedObservable.notifyObservers(this))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"textHorizontalAlignment",{get:function(){return this._textHorizontalAlignment},set:function(t){this._textHorizontalAlignment!==t&&(this._textHorizontalAlignment=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"textVerticalAlignment",{get:function(){return this._textVerticalAlignment},set:function(t){this._textVerticalAlignment!==t&&(this._textVerticalAlignment=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"lineSpacing",{get:function(){return this._lineSpacing.toString(this._host)},set:function(t){this._lineSpacing.fromString(t)&&this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"outlineWidth",{get:function(){return this._outlineWidth},set:function(t){this._outlineWidth!==t&&(this._outlineWidth=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"underline",{get:function(){return this._underline},set:function(t){this._underline!==t&&(this._underline=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"lineThrough",{get:function(){return this._lineThrough},set:function(t){this._lineThrough!==t&&(this._lineThrough=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"outlineColor",{get:function(){return this._outlineColor},set:function(t){this._outlineColor!==t&&(this._outlineColor=t,this._markAsDirty())},enumerable:!1,configurable:!0}),e.prototype._getTypeName=function(){return"TextBlock"},e.prototype._processMeasures=function(e,i){this._fontOffset||(this._fontOffset=f._GetFontOffset(i.font)),t.prototype._processMeasures.call(this,e,i),this._lines=this._breakLines(this._currentMeasure.width,i),this.onLinesReadyObservable.notifyObservers(this);for(var r=0,o=0;o<this._lines.length;o++){var n=this._lines[o];n.width>r&&(r=n.width)}if(this._resizeToFit){if(this._textWrapping===b.Clip){var s=this.paddingLeftInPixels+this.paddingRightInPixels+r|0;s!==this._width.internalValue&&(this._width.updateInPlace(s,a.UNITMODE_PIXEL),this._rebuildLayout=!0)}var h=this.paddingTopInPixels+this.paddingBottomInPixels+this._fontOffset.height*this._lines.length|0;if(this._lines.length>0&&0!==this._lineSpacing.internalValue){var l=0;l=this._lineSpacing.isPixel?this._lineSpacing.getValue(this._host):this._lineSpacing.getValue(this._host)*this._height.getValueInPixel(this._host,this._cachedParentMeasure.height),h+=(this._lines.length-1)*l}h!==this._height.internalValue&&(this._height.updateInPlace(h,a.UNITMODE_PIXEL),this._rebuildLayout=!0)}},e.prototype._drawText=function(t,e,i,r){var o=this._currentMeasure.width,n=0;switch(this._textHorizontalAlignment){case f.HORIZONTAL_ALIGNMENT_LEFT:n=0;break;case f.HORIZONTAL_ALIGNMENT_RIGHT:n=o-e;break;case f.HORIZONTAL_ALIGNMENT_CENTER:n=(o-e)/2}(this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY)&&(r.shadowColor=this.shadowColor,r.shadowBlur=this.shadowBlur,r.shadowOffsetX=this.shadowOffsetX,r.shadowOffsetY=this.shadowOffsetY),this.outlineWidth&&r.strokeText(t,this._currentMeasure.left+n,i),r.fillText(t,this._currentMeasure.left+n,i),this._underline&&(r.beginPath(),r.lineWidth=Math.round(.05*this.fontSizeInPixels),r.moveTo(this._currentMeasure.left+n,i+3),r.lineTo(this._currentMeasure.left+n+e,i+3),r.stroke(),r.closePath()),this._lineThrough&&(r.beginPath(),r.lineWidth=Math.round(.05*this.fontSizeInPixels),r.moveTo(this._currentMeasure.left+n,i-this.fontSizeInPixels/3),r.lineTo(this._currentMeasure.left+n+e,i-this.fontSizeInPixels/3),r.stroke(),r.closePath())},e.prototype._draw=function(t,e){t.save(),this._applyStates(t),this._renderLines(t),t.restore()},e.prototype._applyStates=function(e){t.prototype._applyStates.call(this,e),this.outlineWidth&&(e.lineWidth=this.outlineWidth,e.strokeStyle=this.outlineColor,e.lineJoin="miter",e.miterLimit=2)},e.prototype._breakLines=function(t,e){var i=[],r=this.text.split("\n");if(this._textWrapping===b.Ellipsis)for(var o=0,n=r;o<n.length;o++){var s=n[o];i.push(this._parseLineEllipsis(s,t,e))}else if(this._textWrapping===b.WordWrap)for(var a=0,h=r;a<h.length;a++){s=h[a];i.push.apply(i,this._parseLineWordWrap(s,t,e))}else for(var l=0,c=r;l<c.length;l++){s=c[l];i.push(this._parseLine(s,e))}return i},e.prototype._parseLine=function(t,e){return void 0===t&&(t=""),{text:t,width:e.measureText(t).width}},e.prototype._parseLineEllipsis=function(t,e,i){void 0===t&&(t="");var r=i.measureText(t).width;r>e&&(t+="…");var o=Array.from&&Array.from(t);if(o)for(;o.length&&r>e;)o.pop(),t=o.join("")+"...",r=i.measureText(t).width;else for(;t.length>2&&r>e;)t=t.slice(0,-2)+"…",r=i.measureText(t).width;return{text:t,width:r}},e.prototype._parseLineWordWrap=function(t,e,i){void 0===t&&(t="");for(var r=[],o=this.wordSplittingFunction?this.wordSplittingFunction(t):t.split(" "),n=0,s=0;s<o.length;s++){var a=s>0?t+" "+o[s]:o[0],h=i.measureText(a).width;h>e&&s>0?(r.push({text:t,width:n}),t=o[s],n=i.measureText(t).width):(n=h,t=a)}return r.push({text:t,width:n}),r},e.prototype._renderLines=function(t){var e=this._currentMeasure.height,i=0;switch(this._textVerticalAlignment){case f.VERTICAL_ALIGNMENT_TOP:i=this._fontOffset.ascent;break;case f.VERTICAL_ALIGNMENT_BOTTOM:i=e-this._fontOffset.height*(this._lines.length-1)-this._fontOffset.descent;break;case f.VERTICAL_ALIGNMENT_CENTER:i=this._fontOffset.ascent+(e-this._fontOffset.height*this._lines.length)/2}i+=this._currentMeasure.top;for(var r=0;r<this._lines.length;r++){var o=this._lines[r];0!==r&&0!==this._lineSpacing.internalValue&&(this._lineSpacing.isPixel?i+=this._lineSpacing.getValue(this._host):i+=this._lineSpacing.getValue(this._host)*this._height.getValueInPixel(this._host,this._cachedParentMeasure.height)),this._drawText(o.text,o.width,i,t),i+=this._fontOffset.height}},e.prototype.computeExpectedHeight=function(){if(this.text&&this.widthInPixels){var t=document.createElement("canvas").getContext("2d");if(t){this._applyStates(t),this._fontOffset||(this._fontOffset=f._GetFontOffset(t.font));var e=this._lines?this._lines:this._breakLines(this.widthInPixels-this.paddingLeftInPixels-this.paddingRightInPixels,t),i=this.paddingTopInPixels+this.paddingBottomInPixels+this._fontOffset.height*e.length;if(e.length>0&&0!==this._lineSpacing.internalValue){var r=0;r=this._lineSpacing.isPixel?this._lineSpacing.getValue(this._host):this._lineSpacing.getValue(this._host)*this._height.getValueInPixel(this._host,this._cachedParentMeasure.height),i+=(e.length-1)*r}return i}}return 0},e.prototype.dispose=function(){t.prototype.dispose.call(this),this.onTextChangedObservable.clear()},n([Object(s.serialize)()],e.prototype,"resizeToFit",null),n([Object(s.serialize)()],e.prototype,"textWrapping",null),n([Object(s.serialize)()],e.prototype,"text",null),n([Object(s.serialize)()],e.prototype,"textHorizontalAlignment",null),n([Object(s.serialize)()],e.prototype,"textVerticalAlignment",null),n([Object(s.serialize)()],e.prototype,"lineSpacing",null),n([Object(s.serialize)()],e.prototype,"outlineWidth",null),n([Object(s.serialize)()],e.prototype,"underline",null),n([Object(s.serialize)()],e.prototype,"lineThrough",null),n([Object(s.serialize)()],e.prototype,"outlineColor",null),e}(f);s._TypeStore.RegisteredTypes["BABYLON.GUI.TextBlock"]=y;var v=function(t){function e(i,r){void 0===r&&(r=null);var o=t.call(this,i)||this;return o.name=i,o._workingCanvas=null,o._loaded=!1,o._stretch=e.STRETCH_FILL,o._autoScale=!1,o._sourceLeft=0,o._sourceTop=0,o._sourceWidth=0,o._sourceHeight=0,o._svgAttributesComputationCompleted=!1,o._isSVG=!1,o._cellWidth=0,o._cellHeight=0,o._cellId=-1,o._populateNinePatchSlicesFromImage=!1,o._imageDataCache={data:null,key:""},o.onImageLoadedObservable=new s.Observable,o.onSVGAttributesComputedObservable=new s.Observable,o.source=r,o}return o(e,t),Object.defineProperty(e.prototype,"isLoaded",{get:function(){return this._loaded},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"detectPointerOnOpaqueOnly",{get:function(){return this._detectPointerOnOpaqueOnly},set:function(t){this._detectPointerOnOpaqueOnly!==t&&(this._detectPointerOnOpaqueOnly=t)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"sliceLeft",{get:function(){return this._sliceLeft},set:function(t){this._sliceLeft!==t&&(this._sliceLeft=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"sliceRight",{get:function(){return this._sliceRight},set:function(t){this._sliceRight!==t&&(this._sliceRight=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"sliceTop",{get:function(){return this._sliceTop},set:function(t){this._sliceTop!==t&&(this._sliceTop=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"sliceBottom",{get:function(){return this._sliceBottom},set:function(t){this._sliceBottom!==t&&(this._sliceBottom=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"sourceLeft",{get:function(){return this._sourceLeft},set:function(t){this._sourceLeft!==t&&(this._sourceLeft=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"sourceTop",{get:function(){return this._sourceTop},set:function(t){this._sourceTop!==t&&(this._sourceTop=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"sourceWidth",{get:function(){return this._sourceWidth},set:function(t){this._sourceWidth!==t&&(this._sourceWidth=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"sourceHeight",{get:function(){return this._sourceHeight},set:function(t){this._sourceHeight!==t&&(this._sourceHeight=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"imageWidth",{get:function(){return this._imageWidth},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"imageHeight",{get:function(){return this._imageHeight},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"populateNinePatchSlicesFromImage",{get:function(){return this._populateNinePatchSlicesFromImage},set:function(t){this._populateNinePatchSlicesFromImage!==t&&(this._populateNinePatchSlicesFromImage=t,this._populateNinePatchSlicesFromImage&&this._loaded&&this._extractNinePatchSliceDataFromImage())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isSVG",{get:function(){return this._isSVG},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"svgAttributesComputationCompleted",{get:function(){return this._svgAttributesComputationCompleted},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"autoScale",{get:function(){return this._autoScale},set:function(t){this._autoScale!==t&&(this._autoScale=t,t&&this._loaded&&this.synchronizeSizeWithContent())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"stretch",{get:function(){return this._stretch},set:function(t){this._stretch!==t&&(this._stretch=t,this._markAsDirty())},enumerable:!1,configurable:!0}),e.prototype._rotate90=function(t,i){void 0===i&&(i=!1);var r=document.createElement("canvas"),o=r.getContext("2d"),n=this._domImage.width,s=this._domImage.height;r.width=s,r.height=n,o.translate(r.width/2,r.height/2),o.rotate(t*Math.PI/2),o.drawImage(this._domImage,0,0,n,s,-n/2,-s/2,n,s);var a=r.toDataURL("image/jpg"),h=new e(this.name+"rotated",a);return i&&(h._stretch=this._stretch,h._autoScale=this._autoScale,h._cellId=this._cellId,h._cellWidth=t%1?this._cellHeight:this._cellWidth,h._cellHeight=t%1?this._cellWidth:this._cellHeight),this._handleRotationForSVGImage(this,h,t),this._imageDataCache.data=null,h},e.prototype._handleRotationForSVGImage=function(t,e,i){var r=this;t._isSVG&&(t._svgAttributesComputationCompleted?(this._rotate90SourceProperties(t,e,i),this._markAsDirty()):t.onSVGAttributesComputedObservable.addOnce((function(){r._rotate90SourceProperties(t,e,i),r._markAsDirty()})))},e.prototype._rotate90SourceProperties=function(t,e,i){var r,o,n=t.sourceLeft,s=t.sourceTop,a=t.domImage.width,h=t.domImage.height,l=n,c=s,u=t.sourceWidth,_=t.sourceHeight;if(0!=i){var d=i<0?-1:1;i%=4;for(var p=0;p<Math.abs(i);++p)l=-(s-h/2)*d+h/2,c=(n-a/2)*d+a/2,u=(r=[_,u])[0],_=r[1],i<0?c-=_:l-=u,n=l,s=c,a=(o=[h,a])[0],h=o[1]}e.sourceLeft=l,e.sourceTop=c,e.sourceWidth=u,e.sourceHeight=_},e.prototype._extractNinePatchSliceDataFromImage=function(){this._workingCanvas||(this._workingCanvas=document.createElement("canvas"));var t=this._workingCanvas,e=t.getContext("2d"),i=this._domImage.width,r=this._domImage.height;t.width=i,t.height=r,e.drawImage(this._domImage,0,0,i,r);var o=e.getImageData(0,0,i,r);this._sliceLeft=-1,this._sliceRight=-1;for(var n=0;n<i;n++){if((a=o.data[4*n+3])>127&&-1===this._sliceLeft)this._sliceLeft=n;else if(a<127&&this._sliceLeft>-1){this._sliceRight=n;break}}this._sliceTop=-1,this._sliceBottom=-1;for(var s=0;s<r;s++){var a;if((a=o.data[s*i*4+3])>127&&-1===this._sliceTop)this._sliceTop=s;else if(a<127&&this._sliceTop>-1){this._sliceBottom=s;break}}},Object.defineProperty(e.prototype,"domImage",{get:function(){return this._domImage},set:function(t){var e=this;this._domImage=t,this._loaded=!1,this._imageDataCache.data=null,this._domImage.width?this._onImageLoaded():this._domImage.onload=function(){e._onImageLoaded()}},enumerable:!1,configurable:!0}),e.prototype._onImageLoaded=function(){this._imageDataCache.data=null,this._imageWidth=this._domImage.width,this._imageHeight=this._domImage.height,this._loaded=!0,this._populateNinePatchSlicesFromImage&&this._extractNinePatchSliceDataFromImage(),this._autoScale&&this.synchronizeSizeWithContent(),this.onImageLoadedObservable.notifyObservers(this),this._markAsDirty()},Object.defineProperty(e.prototype,"source",{get:function(){return this._source},set:function(t){var e=this;this._source!==t&&(this._loaded=!1,this._source=t,this._imageDataCache.data=null,t&&(t=this._svgCheck(t)),this._domImage=document.createElement("img"),this._domImage.onload=function(){e._onImageLoaded()},t&&(s.Tools.SetCorsBehavior(t,this._domImage),this._domImage.src=t))},enumerable:!1,configurable:!0}),e.prototype._svgCheck=function(t){var e=this;if(window.SVGSVGElement&&-1!==t.search(/.svg#/gi)&&t.indexOf("#")===t.lastIndexOf("#")){this._isSVG=!0;var i=t.split("#")[0],r=t.split("#")[1],o=document.body.querySelector('object[data="'+i+'"]');if(o){var n=o.contentDocument;if(n&&n.documentElement){var s=n.documentElement.getAttribute("viewBox"),a=Number(n.documentElement.getAttribute("width")),h=Number(n.documentElement.getAttribute("height"));if(n.getElementById(r)&&s&&a&&h)return this._getSVGAttribs(o,r),t}o.addEventListener("load",(function(){e._getSVGAttribs(o,r)}))}else{var l=document.createElement("object");l.data=i,l.type="image/svg+xml",l.width="0%",l.height="0%",document.body.appendChild(l),l.onload=function(){var t=document.body.querySelector('object[data="'+i+'"]');t&&e._getSVGAttribs(t,r)}}return i}return t},e.prototype._getSVGAttribs=function(t,e){var i=t.contentDocument;if(i&&i.documentElement){var r=i.documentElement.getAttribute("viewBox"),o=Number(i.documentElement.getAttribute("width")),n=Number(i.documentElement.getAttribute("height")),s=i.getElementById(e);if(r&&o&&n&&s){var a=Number(r.split(" ")[2]),h=Number(r.split(" ")[3]),l=s.getBBox(),c=1,u=1,_=0,d=0;s.transform&&s.transform.baseVal.consolidate()&&(c=s.transform.baseVal.consolidate().matrix.a,u=s.transform.baseVal.consolidate().matrix.d,_=s.transform.baseVal.consolidate().matrix.e,d=s.transform.baseVal.consolidate().matrix.f),this.sourceLeft=(c*l.x+_)*o/a,this.sourceTop=(u*l.y+d)*n/h,this.sourceWidth=l.width*c*(o/a),this.sourceHeight=l.height*u*(n/h),this._svgAttributesComputationCompleted=!0,this.onSVGAttributesComputedObservable.notifyObservers(this)}}},Object.defineProperty(e.prototype,"cellWidth",{get:function(){return this._cellWidth},set:function(t){this._cellWidth!==t&&(this._cellWidth=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"cellHeight",{get:function(){return this._cellHeight},set:function(t){this._cellHeight!==t&&(this._cellHeight=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"cellId",{get:function(){return this._cellId},set:function(t){this._cellId!==t&&(this._cellId=t,this._markAsDirty())},enumerable:!1,configurable:!0}),e.prototype.contains=function(e,i){if(!t.prototype.contains.call(this,e,i))return!1;if(!this._detectPointerOnOpaqueOnly||!this._workingCanvas)return!0;var r=0|this._currentMeasure.width,o=0|this._currentMeasure.height,n=r+"_"+o,s=this._imageDataCache.data;if(!s||this._imageDataCache.key!==n){var a=this._workingCanvas.getContext("2d");this._imageDataCache.data=s=a.getImageData(0,0,r,o).data,this._imageDataCache.key=n}return s[4*((e=e-this._currentMeasure.left|0)+(i=i-this._currentMeasure.top|0)*r)+3]>0},e.prototype._getTypeName=function(){return"Image"},e.prototype.synchronizeSizeWithContent=function(){this._loaded&&(this.width=this._domImage.width+"px",this.height=this._domImage.height+"px")},e.prototype._processMeasures=function(i,r){if(this._loaded)switch(this._stretch){case e.STRETCH_NONE:case e.STRETCH_FILL:case e.STRETCH_UNIFORM:case e.STRETCH_NINE_PATCH:break;case e.STRETCH_EXTEND:this._autoScale&&this.synchronizeSizeWithContent(),this.parent&&this.parent.parent&&(this.parent.adaptWidthToChildren=!0,this.parent.adaptHeightToChildren=!0)}t.prototype._processMeasures.call(this,i,r)},e.prototype._prepareWorkingCanvasForOpaqueDetection=function(){if(this._detectPointerOnOpaqueOnly){this._workingCanvas||(this._workingCanvas=document.createElement("canvas"));var t=this._workingCanvas,e=this._currentMeasure.width,i=this._currentMeasure.height,r=t.getContext("2d");t.width=e,t.height=i,r.clearRect(0,0,e,i)}},e.prototype._drawImage=function(t,e,i,r,o,n,s,a,h){(t.drawImage(this._domImage,e,i,r,o,n,s,a,h),this._detectPointerOnOpaqueOnly)&&(t=this._workingCanvas.getContext("2d")).drawImage(this._domImage,e,i,r,o,n-this._currentMeasure.left,s-this._currentMeasure.top,a,h)},e.prototype._draw=function(t){var i,r,o,n;if(t.save(),(this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY)&&(t.shadowColor=this.shadowColor,t.shadowBlur=this.shadowBlur,t.shadowOffsetX=this.shadowOffsetX,t.shadowOffsetY=this.shadowOffsetY),-1==this.cellId)i=this._sourceLeft,r=this._sourceTop,o=this._sourceWidth?this._sourceWidth:this._imageWidth,n=this._sourceHeight?this._sourceHeight:this._imageHeight;else{var s=this._domImage.naturalWidth/this.cellWidth,a=this.cellId/s>>0,h=this.cellId%s;i=this.cellWidth*h,r=this.cellHeight*a,o=this.cellWidth,n=this.cellHeight}if(this._prepareWorkingCanvasForOpaqueDetection(),this._applyStates(t),this._loaded)switch(this._stretch){case e.STRETCH_NONE:case e.STRETCH_FILL:this._drawImage(t,i,r,o,n,this._currentMeasure.left,this._currentMeasure.top,this._currentMeasure.width,this._currentMeasure.height);break;case e.STRETCH_UNIFORM:var l=this._currentMeasure.width/o,c=this._currentMeasure.height/n,u=Math.min(l,c),_=(this._currentMeasure.width-o*u)/2,d=(this._currentMeasure.height-n*u)/2;this._drawImage(t,i,r,o,n,this._currentMeasure.left+_,this._currentMeasure.top+d,o*u,n*u);break;case e.STRETCH_EXTEND:this._drawImage(t,i,r,o,n,this._currentMeasure.left,this._currentMeasure.top,this._currentMeasure.width,this._currentMeasure.height);break;case e.STRETCH_NINE_PATCH:this._renderNinePatch(t)}t.restore()},e.prototype._renderNinePatch=function(t){var e=this._sliceLeft,i=this._sliceTop,r=this._imageHeight-this._sliceBottom,o=this._imageWidth-this._sliceRight,n=this._sliceRight-this._sliceLeft,s=this._sliceBottom-this._sliceTop,a=this._currentMeasure.width-o-e+2,h=this._currentMeasure.height-r-i+2,l=this._currentMeasure.left+e-1,c=this._currentMeasure.top+i-1,u=this._currentMeasure.left+this._currentMeasure.width-o,_=this._currentMeasure.top+this._currentMeasure.height-r;this._drawImage(t,0,0,e,i,this._currentMeasure.left,this._currentMeasure.top,e,i),this._drawImage(t,this._sliceLeft,0,n,i,l,this._currentMeasure.top,a,i),this._drawImage(t,this.sliceRight,0,o,i,u,this._currentMeasure.top,o,i),this._drawImage(t,0,this._sliceTop,e,s,this._currentMeasure.left,c,e,h),this._drawImage(t,this._sliceLeft,this._sliceTop,n,s,l,c,a,h),this._drawImage(t,this._sliceRight,this._sliceTop,o,s,u,c,o,h),this._drawImage(t,0,this._sliceBottom,e,r,this._currentMeasure.left,_,e,r),this._drawImage(t,this.sliceLeft,this._sliceBottom,n,r,l,_,a,r),this._drawImage(t,this._sliceRight,this._sliceBottom,o,r,u,_,o,r)},e.prototype.dispose=function(){t.prototype.dispose.call(this),this.onImageLoadedObservable.clear(),this.onSVGAttributesComputedObservable.clear()},e.STRETCH_NONE=0,e.STRETCH_FILL=1,e.STRETCH_UNIFORM=2,e.STRETCH_EXTEND=3,e.STRETCH_NINE_PATCH=4,n([Object(s.serialize)()],e.prototype,"detectPointerOnOpaqueOnly",null),n([Object(s.serialize)()],e.prototype,"sliceLeft",null),n([Object(s.serialize)()],e.prototype,"sliceRight",null),n([Object(s.serialize)()],e.prototype,"sliceTop",null),n([Object(s.serialize)()],e.prototype,"sliceBottom",null),n([Object(s.serialize)()],e.prototype,"sourceLeft",null),n([Object(s.serialize)()],e.prototype,"sourceTop",null),n([Object(s.serialize)()],e.prototype,"sourceWidth",null),n([Object(s.serialize)()],e.prototype,"sourceHeight",null),n([Object(s.serialize)()],e.prototype,"populateNinePatchSlicesFromImage",null),n([Object(s.serialize)()],e.prototype,"autoScale",null),n([Object(s.serialize)()],e.prototype,"stretch",null),n([Object(s.serialize)()],e.prototype,"source",null),e}(f);s._TypeStore.RegisteredTypes["BABYLON.GUI.Image"]=v;var O=function(t){function e(e){var i=t.call(this,e)||this;i.name=e,i.delegatePickingToChildren=!1,i.thickness=1,i.isPointerBlocker=!0;var r=null;return i.pointerEnterAnimation=function(){r=i.alpha,i.alpha-=.1},i.pointerOutAnimation=function(){null!==r&&(i.alpha=r)},i.pointerDownAnimation=function(){i.scaleX-=.05,i.scaleY-=.05},i.pointerUpAnimation=function(){i.scaleX+=.05,i.scaleY+=.05},i}return o(e,t),Object.defineProperty(e.prototype,"image",{get:function(){return this._image},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"textBlock",{get:function(){return this._textBlock},enumerable:!1,configurable:!0}),e.prototype._getTypeName=function(){return"Button"},e.prototype._processPicking=function(e,i,r,o,n,s,a,h){if(!this._isEnabled||!this.isHitTestVisible||!this.isVisible||this.notRenderable)return!1;if(!t.prototype.contains.call(this,e,i))return!1;if(this.delegatePickingToChildren){for(var l=!1,c=this._children.length-1;c>=0;c--){var u=this._children[c];if(u.isEnabled&&u.isHitTestVisible&&u.isVisible&&!u.notRenderable&&u.contains(e,i)){l=!0;break}}if(!l)return!1}return this._processObservables(o,e,i,r,n,s,a,h),!0},e.prototype._onPointerEnter=function(e,i){return!!t.prototype._onPointerEnter.call(this,e,i)&&(this.pointerEnterAnimation&&this.pointerEnterAnimation(),!0)},e.prototype._onPointerOut=function(e,i,r){void 0===r&&(r=!1),this.pointerOutAnimation&&this.pointerOutAnimation(),t.prototype._onPointerOut.call(this,e,i,r)},e.prototype._onPointerDown=function(e,i,r,o,n){return!!t.prototype._onPointerDown.call(this,e,i,r,o,n)&&(this.pointerDownAnimation&&this.pointerDownAnimation(),!0)},e.prototype._onPointerUp=function(e,i,r,o,n,s){this.pointerUpAnimation&&this.pointerUpAnimation(),t.prototype._onPointerUp.call(this,e,i,r,o,n,s)},e.CreateImageButton=function(t,e,i){var r=new this(t),o=new y(t+"_button",e);o.textWrapping=!0,o.textHorizontalAlignment=f.HORIZONTAL_ALIGNMENT_CENTER,o.paddingLeft="20%",r.addControl(o);var n=new v(t+"_icon",i);return n.width="20%",n.stretch=v.STRETCH_UNIFORM,n.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,r.addControl(n),r._image=n,r._textBlock=o,r},e.CreateImageOnlyButton=function(t,e){var i=new this(t),r=new v(t+"_icon",e);return r.stretch=v.STRETCH_FILL,r.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,i.addControl(r),i._image=r,i},e.CreateSimpleButton=function(t,e){var i=new this(t),r=new y(t+"_button",e);return r.textWrapping=!0,r.textHorizontalAlignment=f.HORIZONTAL_ALIGNMENT_CENTER,i.addControl(r),i._textBlock=r,i},e.CreateImageWithCenterTextButton=function(t,e,i){var r=new this(t),o=new v(t+"_icon",i);o.stretch=v.STRETCH_FILL,r.addControl(o);var n=new y(t+"_button",e);return n.textWrapping=!0,n.textHorizontalAlignment=f.HORIZONTAL_ALIGNMENT_CENTER,r.addControl(n),r._image=o,r._textBlock=n,r},e}(m);s._TypeStore.RegisteredTypes["BABYLON.GUI.Button"]=O;var C=function(t){function e(e){var i=t.call(this,e)||this;return i.name=e,i._isVertical=!0,i._manualWidth=!1,i._manualHeight=!1,i._doNotTrackManualChanges=!1,i.ignoreLayoutWarnings=!1,i}return o(e,t),Object.defineProperty(e.prototype,"isVertical",{get:function(){return this._isVertical},set:function(t){this._isVertical!==t&&(this._isVertical=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"width",{get:function(){return this._width.toString(this._host)},set:function(t){this._doNotTrackManualChanges||(this._manualWidth=!0),this._width.toString(this._host)!==t&&this._width.fromString(t)&&this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"height",{get:function(){return this._height.toString(this._host)},set:function(t){this._doNotTrackManualChanges||(this._manualHeight=!0),this._height.toString(this._host)!==t&&this._height.fromString(t)&&this._markAsDirty()},enumerable:!1,configurable:!0}),e.prototype._getTypeName=function(){return"StackPanel"},e.prototype._preMeasure=function(e,i){for(var r=0,o=this._children;r<o.length;r++){var n=o[r];this._isVertical?n.verticalAlignment=f.VERTICAL_ALIGNMENT_TOP:n.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT}t.prototype._preMeasure.call(this,e,i)},e.prototype._additionalProcessing=function(e,i){t.prototype._additionalProcessing.call(this,e,i),this._measureForChildren.copyFrom(e),this._measureForChildren.left=this._currentMeasure.left,this._measureForChildren.top=this._currentMeasure.top,this.isVertical&&!this._manualWidth||(this._measureForChildren.width=this._currentMeasure.width),(this.isVertical||this._manualHeight)&&(this._measureForChildren.height=this._currentMeasure.height)},e.prototype._postMeasure=function(){for(var e=0,i=0,r=0,o=this._children;r<o.length;r++){var n=o[r];n.isVisible&&!n.notRenderable&&(this._isVertical?(n.top!==i+"px"&&(n.top=i+"px",this._rebuildLayout=!0,n._top.ignoreAdaptiveScaling=!0),n._height.isPercentage&&!n._automaticSize?this.ignoreLayoutWarnings||s.Tools.Warn("Control (Name:"+n.name+", UniqueId:"+n.uniqueId+") is using height in percentage mode inside a vertical StackPanel"):i+=n._currentMeasure.height+n.paddingTopInPixels+n.paddingBottomInPixels):(n.left!==e+"px"&&(n.left=e+"px",this._rebuildLayout=!0,n._left.ignoreAdaptiveScaling=!0),n._width.isPercentage&&!n._automaticSize?this.ignoreLayoutWarnings||s.Tools.Warn("Control (Name:"+n.name+", UniqueId:"+n.uniqueId+") is using width in percentage mode inside a horizontal StackPanel"):e+=n._currentMeasure.width+n.paddingLeftInPixels+n.paddingRightInPixels))}e+=this.paddingLeftInPixels+this.paddingRightInPixels,i+=this.paddingTopInPixels+this.paddingBottomInPixels,this._doNotTrackManualChanges=!0;var a=!1,h=!1;if(!this._manualHeight&&this._isVertical){var l=this.height;this.height=i+"px",h=l!==this.height||!this._height.ignoreAdaptiveScaling}if(!this._manualWidth&&!this._isVertical){var c=this.width;this.width=e+"px",a=c!==this.width||!this._width.ignoreAdaptiveScaling}h&&(this._height.ignoreAdaptiveScaling=!0),a&&(this._width.ignoreAdaptiveScaling=!0),this._doNotTrackManualChanges=!1,(a||h)&&(this._rebuildLayout=!0),t.prototype._postMeasure.call(this)},e.prototype.serialize=function(e){t.prototype.serialize.call(this,e),e.manualWidth=this._manualWidth,e.manualHeight=this._manualHeight},e.prototype._parseFromContent=function(e,i){this._manualWidth=e.manualWidth,this._manualHeight=e.manualHeight,t.prototype._parseFromContent.call(this,e,i)},n([Object(s.serialize)()],e.prototype,"ignoreLayoutWarnings",void 0),n([Object(s.serialize)()],e.prototype,"isVertical",null),n([Object(s.serialize)()],e.prototype,"width",null),n([Object(s.serialize)()],e.prototype,"height",null),e}(g);s._TypeStore.RegisteredTypes["BABYLON.GUI.StackPanel"]=C;var x=function(t){function e(e){var i=t.call(this,e)||this;return i.name=e,i._isChecked=!1,i._background="black",i._checkSizeRatio=.8,i._thickness=1,i.onIsCheckedChangedObservable=new s.Observable,i.isPointerBlocker=!0,i}return o(e,t),Object.defineProperty(e.prototype,"thickness",{get:function(){return this._thickness},set:function(t){this._thickness!==t&&(this._thickness=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"checkSizeRatio",{get:function(){return this._checkSizeRatio},set:function(t){t=Math.max(Math.min(1,t),0),this._checkSizeRatio!==t&&(this._checkSizeRatio=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"background",{get:function(){return this._background},set:function(t){this._background!==t&&(this._background=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isChecked",{get:function(){return this._isChecked},set:function(t){this._isChecked!==t&&(this._isChecked=t,this._markAsDirty(),this.onIsCheckedChangedObservable.notifyObservers(t))},enumerable:!1,configurable:!0}),e.prototype._getTypeName=function(){return"Checkbox"},e.prototype._draw=function(t,e){t.save(),this._applyStates(t);var i=this._currentMeasure.width-this._thickness,r=this._currentMeasure.height-this._thickness;if((this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY)&&(t.shadowColor=this.shadowColor,t.shadowBlur=this.shadowBlur,t.shadowOffsetX=this.shadowOffsetX,t.shadowOffsetY=this.shadowOffsetY),t.fillStyle=this._isEnabled?this._background:this._disabledColor,t.fillRect(this._currentMeasure.left+this._thickness/2,this._currentMeasure.top+this._thickness/2,i,r),(this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY)&&(t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0),this._isChecked){t.fillStyle=this._isEnabled?this.color:this._disabledColorItem;var o=i*this._checkSizeRatio,n=r*this._checkSizeRatio;t.fillRect(this._currentMeasure.left+this._thickness/2+(i-o)/2,this._currentMeasure.top+this._thickness/2+(r-n)/2,o,n)}t.strokeStyle=this.color,t.lineWidth=this._thickness,t.strokeRect(this._currentMeasure.left+this._thickness/2,this._currentMeasure.top+this._thickness/2,i,r),t.restore()},e.prototype._onPointerDown=function(e,i,r,o,n){return!!t.prototype._onPointerDown.call(this,e,i,r,o,n)&&(this.isChecked=!this.isChecked,!0)},e.AddCheckBoxWithHeader=function(t,i){var r=new C;r.isVertical=!1,r.height="30px";var o=new e;o.width="20px",o.height="20px",o.isChecked=!0,o.color="green",o.onIsCheckedChangedObservable.add(i),r.addControl(o);var n=new y;return n.text=t,n.width="180px",n.paddingLeft="5px",n.textHorizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,n.color="white",r.addControl(n),r},n([Object(s.serialize)()],e.prototype,"thickness",null),n([Object(s.serialize)()],e.prototype,"checkSizeRatio",null),n([Object(s.serialize)()],e.prototype,"background",null),n([Object(s.serialize)()],e.prototype,"isChecked",null),e}(f);s._TypeStore.RegisteredTypes["BABYLON.GUI.Checkbox"]=x;var P=function(){function t(){}return Object.defineProperty(t.prototype,"text",{get:function(){return this._characters?this._characters.join(""):this._text},set:function(t){this._text=t,this._characters=Array.from&&Array.from(t)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"length",{get:function(){return this._characters?this._characters.length:this._text.length},enumerable:!1,configurable:!0}),t.prototype.removePart=function(t,e,i){var r;if(this._text=this._text.slice(0,t)+(i||"")+this._text.slice(e),this._characters){var o=i?Array.from(i):[];(r=this._characters).splice.apply(r,function(t,e){for(var i=0,r=e.length,o=t.length;i<r;i++,o++)t[o]=e[i];return t}([t,e-t],o))}},t.prototype.charAt=function(t){return this._characters?this._characters[t]:this._text.charAt(t)},t.prototype.substr=function(t,e){if(this._characters){t=isNaN(t)?0:t>=0?Math.min(t,this._characters.length):this._characters.length+Math.max(t,-this._characters.length),void 0===e?e=this._characters.length-t:(isNaN(e)||e<0)&&(e=0);for(var i=[];--e>=0;)i[e]=this._characters[t+e];return i.join("")}return this._text.substr(t,e)},t.prototype.substring=function(t,e){if(this._characters){isNaN(t)?t=0:t>this._characters.length?t=this._characters.length:t<0&&(t=0),void 0===e?e=this._characters.length:isNaN(e)?e=0:e>this._characters.length?e=this._characters.length:e<0&&(e=0);for(var i=[],r=0;t<e;)i[r++]=this._characters[t++];return i.join("")}return this._text.substring(t,e)},t.prototype.isWord=function(t){var e=/\w/g;return this._characters?-1!==this._characters[t].search(e):-1!==this._text.search(e)},t}(),T=function(t){function e(e,i){void 0===i&&(i="");var r=t.call(this,e)||this;return r.name=e,r._placeholderText="",r._background="#222222",r._focusedBackground="#000000",r._focusedColor="white",r._placeholderColor="gray",r._thickness=1,r._margin=new a(10,a.UNITMODE_PIXEL),r._autoStretchWidth=!0,r._maxWidth=new a(1,a.UNITMODE_PERCENTAGE,!1),r._isFocused=!1,r._blinkIsEven=!1,r._cursorOffset=0,r._deadKey=!1,r._addKey=!0,r._currentKey="",r._isTextHighlightOn=!1,r._textHighlightColor="#d5e0ff",r._highligherOpacity=.4,r._highlightedText="",r._startHighlightIndex=0,r._endHighlightIndex=0,r._cursorIndex=-1,r._onFocusSelectAll=!1,r._isPointerDown=!1,r.promptMessage="Please enter text:",r.disableMobilePrompt=!1,r.onTextChangedObservable=new s.Observable,r.onBeforeKeyAddObservable=new s.Observable,r.onFocusObservable=new s.Observable,r.onBlurObservable=new s.Observable,r.onTextHighlightObservable=new s.Observable,r.onTextCopyObservable=new s.Observable,r.onTextCutObservable=new s.Observable,r.onTextPasteObservable=new s.Observable,r.onKeyboardEventProcessedObservable=new s.Observable,r.text=i,r.isPointerBlocker=!0,r}return o(e,t),Object.defineProperty(e.prototype,"maxWidth",{get:function(){return this._maxWidth.toString(this._host)},set:function(t){this._maxWidth.toString(this._host)!==t&&this._maxWidth.fromString(t)&&this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"maxWidthInPixels",{get:function(){return this._maxWidth.getValueInPixel(this._host,this._cachedParentMeasure.width)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"highligherOpacity",{get:function(){return this._highligherOpacity},set:function(t){this._highligherOpacity!==t&&(this._highligherOpacity=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onFocusSelectAll",{get:function(){return this._onFocusSelectAll},set:function(t){this._onFocusSelectAll!==t&&(this._onFocusSelectAll=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"textHighlightColor",{get:function(){return this._textHighlightColor},set:function(t){this._textHighlightColor!==t&&(this._textHighlightColor=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"margin",{get:function(){return this._margin.toString(this._host)},set:function(t){this._margin.toString(this._host)!==t&&this._margin.fromString(t)&&this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"marginInPixels",{get:function(){return this._margin.getValueInPixel(this._host,this._cachedParentMeasure.width)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"autoStretchWidth",{get:function(){return this._autoStretchWidth},set:function(t){this._autoStretchWidth!==t&&(this._autoStretchWidth=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"thickness",{get:function(){return this._thickness},set:function(t){this._thickness!==t&&(this._thickness=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"focusedBackground",{get:function(){return this._focusedBackground},set:function(t){this._focusedBackground!==t&&(this._focusedBackground=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"focusedColor",{get:function(){return this._focusedColor},set:function(t){this._focusedColor!==t&&(this._focusedColor=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"background",{get:function(){return this._background},set:function(t){this._background!==t&&(this._background=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"placeholderColor",{get:function(){return this._placeholderColor},set:function(t){this._placeholderColor!==t&&(this._placeholderColor=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"placeholderText",{get:function(){return this._placeholderText},set:function(t){this._placeholderText!==t&&(this._placeholderText=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"deadKey",{get:function(){return this._deadKey},set:function(t){this._deadKey=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"highlightedText",{get:function(){return this._highlightedText},set:function(t){this._highlightedText!==t&&(this._highlightedText=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"addKey",{get:function(){return this._addKey},set:function(t){this._addKey=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"currentKey",{get:function(){return this._currentKey},set:function(t){this._currentKey=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"text",{get:function(){return this._textWrapper.text},set:function(t){var e=t.toString();this._textWrapper||(this._textWrapper=new P),this._textWrapper.text!==e&&(this._textWrapper.text=e,this._textHasChanged())},enumerable:!1,configurable:!0}),e.prototype._textHasChanged=function(){this._markAsDirty(),this.onTextChangedObservable.notifyObservers(this)},Object.defineProperty(e.prototype,"width",{get:function(){return this._width.toString(this._host)},set:function(t){this._width.toString(this._host)!==t&&(this._width.fromString(t)&&this._markAsDirty(),this.autoStretchWidth=!1)},enumerable:!1,configurable:!0}),e.prototype.onBlur=function(){this._isFocused=!1,this._scrollLeft=null,this._cursorOffset=0,clearTimeout(this._blinkTimeout),this._markAsDirty(),this.onBlurObservable.notifyObservers(this),this._host.unRegisterClipboardEvents(),this._onClipboardObserver&&this._host.onClipboardObservable.remove(this._onClipboardObserver);var t=this._host.getScene();this._onPointerDblTapObserver&&t&&t.onPointerObservable.remove(this._onPointerDblTapObserver)},e.prototype.onFocus=function(){var t=this;if(this._isEnabled){if(this._scrollLeft=null,this._isFocused=!0,this._blinkIsEven=!1,this._cursorOffset=0,this._markAsDirty(),this.onFocusObservable.notifyObservers(this),-1!==navigator.userAgent.indexOf("Mobile")&&!this.disableMobilePrompt){var e=prompt(this.promptMessage);return null!==e&&(this.text=e),void(this._host.focusedControl=null)}this._host.registerClipboardEvents(),this._onClipboardObserver=this._host.onClipboardObservable.add((function(e){switch(e.type){case s.ClipboardEventTypes.COPY:t._onCopyText(e.event),t.onTextCopyObservable.notifyObservers(t);break;case s.ClipboardEventTypes.CUT:t._onCutText(e.event),t.onTextCutObservable.notifyObservers(t);break;case s.ClipboardEventTypes.PASTE:t._onPasteText(e.event),t.onTextPasteObservable.notifyObservers(t);break;default:return}}));var i=this._host.getScene();i&&(this._onPointerDblTapObserver=i.onPointerObservable.add((function(e){t._isFocused&&e.type===s.PointerEventTypes.POINTERDOUBLETAP&&t._processDblClick(e)}))),this._onFocusSelectAll&&this._selectAllText()}},e.prototype.focus=function(){this._host.moveFocusToControl(this)},e.prototype.blur=function(){this._host.focusedControl=null},e.prototype._getTypeName=function(){return"InputText"},e.prototype.keepsFocusWith=function(){return this._connectedVirtualKeyboard?[this._connectedVirtualKeyboard]:null},e.prototype.processKey=function(t,e,i){if(!i||!i.ctrlKey&&!i.metaKey||67!==t&&86!==t&&88!==t){if(i&&(i.ctrlKey||i.metaKey)&&65===t)return this._selectAllText(),void i.preventDefault();switch(t){case 32:e=" ";break;case 191:i&&i.preventDefault();break;case 8:if(this._textWrapper.text&&this._textWrapper.length>0){if(this._isTextHighlightOn)return this._textWrapper.removePart(this._startHighlightIndex,this._endHighlightIndex),this._textHasChanged(),this._isTextHighlightOn=!1,this._cursorOffset=this._textWrapper.length-this._startHighlightIndex,this._blinkIsEven=!1,void(i&&i.preventDefault());if(0===this._cursorOffset)this.text=this._textWrapper.substr(0,this._textWrapper.length-1);else(r=this._textWrapper.length-this._cursorOffset)>0&&(this._textWrapper.removePart(r-1,r),this._textHasChanged())}return void(i&&i.preventDefault());case 46:if(this._isTextHighlightOn)return this._textWrapper.removePart(this._startHighlightIndex,this._endHighlightIndex),this._textHasChanged(),this._isTextHighlightOn=!1,this._cursorOffset=this._textWrapper.length-this._startHighlightIndex,void(i&&i.preventDefault());if(this._textWrapper.text&&this._textWrapper.length>0&&this._cursorOffset>0){var r=this._textWrapper.length-this._cursorOffset;this._textWrapper.removePart(r,r+1),this._textHasChanged(),this._cursorOffset--}return void(i&&i.preventDefault());case 13:return this._host.focusedControl=null,void(this._isTextHighlightOn=!1);case 35:return this._cursorOffset=0,this._blinkIsEven=!1,this._isTextHighlightOn=!1,void this._markAsDirty();case 36:return this._cursorOffset=this._textWrapper.length,this._blinkIsEven=!1,this._isTextHighlightOn=!1,void this._markAsDirty();case 37:if(this._cursorOffset++,this._cursorOffset>this._textWrapper.length&&(this._cursorOffset=this._textWrapper.length),i&&i.shiftKey){if(this._blinkIsEven=!1,i.ctrlKey||i.metaKey){if(!this._isTextHighlightOn){if(this._textWrapper.length===this._cursorOffset)return;this._endHighlightIndex=this._textWrapper.length-this._cursorOffset+1}return this._startHighlightIndex=0,this._cursorIndex=this._textWrapper.length-this._endHighlightIndex,this._cursorOffset=this._textWrapper.length,this._isTextHighlightOn=!0,void this._markAsDirty()}return this._isTextHighlightOn?-1===this._cursorIndex&&(this._cursorIndex=this._textWrapper.length-this._endHighlightIndex,this._cursorOffset=0===this._startHighlightIndex?this._textWrapper.length:this._textWrapper.length-this._startHighlightIndex+1):(this._isTextHighlightOn=!0,this._cursorIndex=this._cursorOffset>=this._textWrapper.length?this._textWrapper.length:this._cursorOffset-1),this._cursorIndex<this._cursorOffset?(this._endHighlightIndex=this._textWrapper.length-this._cursorIndex,this._startHighlightIndex=this._textWrapper.length-this._cursorOffset):this._cursorIndex>this._cursorOffset?(this._endHighlightIndex=this._textWrapper.length-this._cursorOffset,this._startHighlightIndex=this._textWrapper.length-this._cursorIndex):this._isTextHighlightOn=!1,void this._markAsDirty()}return this._isTextHighlightOn&&(this._cursorOffset=this._textWrapper.length-this._startHighlightIndex,this._isTextHighlightOn=!1),i&&(i.ctrlKey||i.metaKey)&&(this._cursorOffset=this._textWrapper.length,i.preventDefault()),this._blinkIsEven=!1,this._isTextHighlightOn=!1,this._cursorIndex=-1,void this._markAsDirty();case 39:if(this._cursorOffset--,this._cursorOffset<0&&(this._cursorOffset=0),i&&i.shiftKey){if(this._blinkIsEven=!1,i.ctrlKey||i.metaKey){if(!this._isTextHighlightOn){if(0===this._cursorOffset)return;this._startHighlightIndex=this._textWrapper.length-this._cursorOffset-1}return this._endHighlightIndex=this._textWrapper.length,this._isTextHighlightOn=!0,this._cursorIndex=this._textWrapper.length-this._startHighlightIndex,this._cursorOffset=0,void this._markAsDirty()}return this._isTextHighlightOn?-1===this._cursorIndex&&(this._cursorIndex=this._textWrapper.length-this._startHighlightIndex,this._cursorOffset=this._textWrapper.length===this._endHighlightIndex?0:this._textWrapper.length-this._endHighlightIndex-1):(this._isTextHighlightOn=!0,this._cursorIndex=this._cursorOffset<=0?0:this._cursorOffset+1),this._cursorIndex<this._cursorOffset?(this._endHighlightIndex=this._textWrapper.length-this._cursorIndex,this._startHighlightIndex=this._textWrapper.length-this._cursorOffset):this._cursorIndex>this._cursorOffset?(this._endHighlightIndex=this._textWrapper.length-this._cursorOffset,this._startHighlightIndex=this._textWrapper.length-this._cursorIndex):this._isTextHighlightOn=!1,void this._markAsDirty()}return this._isTextHighlightOn&&(this._cursorOffset=this._textWrapper.length-this._endHighlightIndex,this._isTextHighlightOn=!1),i&&(i.ctrlKey||i.metaKey)&&(this._cursorOffset=0,i.preventDefault()),this._blinkIsEven=!1,this._isTextHighlightOn=!1,this._cursorIndex=-1,void this._markAsDirty();case 222:i&&i.preventDefault(),this._cursorIndex=-1,this.deadKey=!0}if(e&&(-1===t||32===t||t>47&&t<64||t>64&&t<91||t>159&&t<193||t>218&&t<223||t>95&&t<112)&&(this._currentKey=e,this.onBeforeKeyAddObservable.notifyObservers(this),e=this._currentKey,this._addKey))if(this._isTextHighlightOn)this._textWrapper.removePart(this._startHighlightIndex,this._endHighlightIndex,e),this._textHasChanged(),this._cursorOffset=this._textWrapper.length-(this._startHighlightIndex+1),this._isTextHighlightOn=!1,this._blinkIsEven=!1,this._markAsDirty();else if(0===this._cursorOffset)this.text+=e;else{var o=this._textWrapper.length-this._cursorOffset;this._textWrapper.removePart(o,o,e),this._textHasChanged()}}},e.prototype._updateValueFromCursorIndex=function(t){if(this._blinkIsEven=!1,-1===this._cursorIndex)this._cursorIndex=t;else if(this._cursorIndex<this._cursorOffset)this._endHighlightIndex=this._textWrapper.length-this._cursorIndex,this._startHighlightIndex=this._textWrapper.length-this._cursorOffset;else{if(!(this._cursorIndex>this._cursorOffset))return this._isTextHighlightOn=!1,void this._markAsDirty();this._endHighlightIndex=this._textWrapper.length-this._cursorOffset,this._startHighlightIndex=this._textWrapper.length-this._cursorIndex}this._isTextHighlightOn=!0,this._markAsDirty()},e.prototype._processDblClick=function(t){var e,i;this._startHighlightIndex=this._textWrapper.length-this._cursorOffset,this._endHighlightIndex=this._startHighlightIndex;do{i=this._endHighlightIndex<this._textWrapper.length&&this._textWrapper.isWord(this._endHighlightIndex)?++this._endHighlightIndex:0,e=this._startHighlightIndex>0&&this._textWrapper.isWord(this._startHighlightIndex-1)?--this._startHighlightIndex:0}while(e||i);this._cursorOffset=this._textWrapper.length-this._startHighlightIndex,this.onTextHighlightObservable.notifyObservers(this),this._isTextHighlightOn=!0,this._clickedCoordinate=null,this._blinkIsEven=!0,this._cursorIndex=-1,this._markAsDirty()},e.prototype._selectAllText=function(){this._blinkIsEven=!0,this._isTextHighlightOn=!0,this._startHighlightIndex=0,this._endHighlightIndex=this._textWrapper.length,this._cursorOffset=this._textWrapper.length,this._cursorIndex=-1,this._markAsDirty()},e.prototype.processKeyboard=function(t){this.processKey(t.keyCode,t.key,t),this.onKeyboardEventProcessedObservable.notifyObservers(t)},e.prototype._onCopyText=function(t){this._isTextHighlightOn=!1;try{t.clipboardData&&t.clipboardData.setData("text/plain",this._highlightedText)}catch(t){}this._host.clipboardData=this._highlightedText},e.prototype._onCutText=function(t){if(this._highlightedText){this._textWrapper.removePart(this._startHighlightIndex,this._endHighlightIndex),this._textHasChanged(),this._isTextHighlightOn=!1,this._cursorOffset=this._textWrapper.length-this._startHighlightIndex;try{t.clipboardData&&t.clipboardData.setData("text/plain",this._highlightedText)}catch(t){}this._host.clipboardData=this._highlightedText,this._highlightedText=""}},e.prototype._onPasteText=function(t){var e="";e=t.clipboardData&&-1!==t.clipboardData.types.indexOf("text/plain")?t.clipboardData.getData("text/plain"):this._host.clipboardData;var i=this._textWrapper.length-this._cursorOffset;this._textWrapper.removePart(i,i,e),this._textHasChanged()},e.prototype._draw=function(t,e){var i=this;t.save(),this._applyStates(t),(this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY)&&(t.shadowColor=this.shadowColor,t.shadowBlur=this.shadowBlur,t.shadowOffsetX=this.shadowOffsetX,t.shadowOffsetY=this.shadowOffsetY),this._isFocused?this._focusedBackground&&(t.fillStyle=this._isEnabled?this._focusedBackground:this._disabledColor,t.fillRect(this._currentMeasure.left,this._currentMeasure.top,this._currentMeasure.width,this._currentMeasure.height)):this._background&&(t.fillStyle=this._isEnabled?this._background:this._disabledColor,t.fillRect(this._currentMeasure.left,this._currentMeasure.top,this._currentMeasure.width,this._currentMeasure.height)),(this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY)&&(t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0),this._fontOffset||(this._fontOffset=f._GetFontOffset(t.font));var r=this._currentMeasure.left+this._margin.getValueInPixel(this._host,this._tempParentMeasure.width);this.color&&(t.fillStyle=this.color);var o=this._beforeRenderText(this._textWrapper);this._isFocused||this._textWrapper.text||!this._placeholderText||((o=new P).text=this._placeholderText,this._placeholderColor&&(t.fillStyle=this._placeholderColor)),this._textWidth=t.measureText(o.text).width;var n=2*this._margin.getValueInPixel(this._host,this._tempParentMeasure.width);this._autoStretchWidth&&(this.width=Math.min(this._maxWidth.getValueInPixel(this._host,this._tempParentMeasure.width),this._textWidth+n)+"px");var s=this._fontOffset.ascent+(this._currentMeasure.height-this._fontOffset.height)/2,a=this._width.getValueInPixel(this._host,this._tempParentMeasure.width)-n;if(t.save(),t.beginPath(),t.rect(r,this._currentMeasure.top+(this._currentMeasure.height-this._fontOffset.height)/2,a+2,this._currentMeasure.height),t.clip(),this._isFocused&&this._textWidth>a){var h=r-this._textWidth+a;this._scrollLeft||(this._scrollLeft=h)}else this._scrollLeft=r;if(t.fillText(o.text,this._scrollLeft,this._currentMeasure.top+s),this._isFocused){if(this._clickedCoordinate){var l=this._scrollLeft+this._textWidth-this._clickedCoordinate,c=0;this._cursorOffset=0;var u=0;do{this._cursorOffset&&(u=Math.abs(l-c)),this._cursorOffset++,c=t.measureText(o.substr(o.length-this._cursorOffset,this._cursorOffset)).width}while(c<l&&o.length>=this._cursorOffset);Math.abs(l-c)>u&&this._cursorOffset--,this._blinkIsEven=!1,this._clickedCoordinate=null}if(!this._blinkIsEven){var _=o.substr(o.length-this._cursorOffset),d=t.measureText(_).width,p=this._scrollLeft+this._textWidth-d;p<r?(this._scrollLeft+=r-p,p=r,this._markAsDirty()):p>r+a&&(this._scrollLeft+=r+a-p,p=r+a,this._markAsDirty()),this._isTextHighlightOn||t.fillRect(p,this._currentMeasure.top+(this._currentMeasure.height-this._fontOffset.height)/2,2,this._fontOffset.height)}if(clearTimeout(this._blinkTimeout),this._blinkTimeout=setTimeout((function(){i._blinkIsEven=!i._blinkIsEven,i._markAsDirty()}),500),this._isTextHighlightOn){clearTimeout(this._blinkTimeout);var g=t.measureText(o.substring(this._startHighlightIndex)).width,b=this._scrollLeft+this._textWidth-g;this._highlightedText=o.substring(this._startHighlightIndex,this._endHighlightIndex);var m=t.measureText(o.substring(this._startHighlightIndex,this._endHighlightIndex)).width;b<r&&((m-=r-b)||(m=t.measureText(o.charAt(o.length-this._cursorOffset)).width),b=r),t.globalAlpha=this._highligherOpacity,t.fillStyle=this._textHighlightColor,t.fillRect(b,this._currentMeasure.top+(this._currentMeasure.height-this._fontOffset.height)/2,m,this._fontOffset.height),t.globalAlpha=1}}t.restore(),this._thickness&&(this._isFocused?this.focusedColor&&(t.strokeStyle=this.focusedColor):this.color&&(t.strokeStyle=this.color),t.lineWidth=this._thickness,t.strokeRect(this._currentMeasure.left+this._thickness/2,this._currentMeasure.top+this._thickness/2,this._currentMeasure.width-this._thickness,this._currentMeasure.height-this._thickness)),t.restore()},e.prototype._onPointerDown=function(e,i,r,o,n){return!!t.prototype._onPointerDown.call(this,e,i,r,o,n)&&(this._clickedCoordinate=i.x,this._isTextHighlightOn=!1,this._highlightedText="",this._cursorIndex=-1,this._isPointerDown=!0,this._host._capturingControl[r]=this,this._host.focusedControl===this?(clearTimeout(this._blinkTimeout),this._markAsDirty(),!0):!!this._isEnabled&&(this._host.focusedControl=this,!0))},e.prototype._onPointerMove=function(e,i,r,o){this._host.focusedControl===this&&this._isPointerDown&&(this._clickedCoordinate=i.x,this._markAsDirty(),this._updateValueFromCursorIndex(this._cursorOffset)),t.prototype._onPointerMove.call(this,e,i,r,o)},e.prototype._onPointerUp=function(e,i,r,o,n){this._isPointerDown=!1,delete this._host._capturingControl[r],t.prototype._onPointerUp.call(this,e,i,r,o,n)},e.prototype._beforeRenderText=function(t){return t},e.prototype.dispose=function(){t.prototype.dispose.call(this),this.onBlurObservable.clear(),this.onFocusObservable.clear(),this.onTextChangedObservable.clear(),this.onTextCopyObservable.clear(),this.onTextCutObservable.clear(),this.onTextPasteObservable.clear(),this.onTextHighlightObservable.clear(),this.onKeyboardEventProcessedObservable.clear()},n([Object(s.serialize)()],e.prototype,"promptMessage",void 0),n([Object(s.serialize)()],e.prototype,"disableMobilePrompt",void 0),n([Object(s.serialize)()],e.prototype,"maxWidth",null),n([Object(s.serialize)()],e.prototype,"highligherOpacity",null),n([Object(s.serialize)()],e.prototype,"onFocusSelectAll",null),n([Object(s.serialize)()],e.prototype,"textHighlightColor",null),n([Object(s.serialize)()],e.prototype,"margin",null),n([Object(s.serialize)()],e.prototype,"autoStretchWidth",null),n([Object(s.serialize)()],e.prototype,"thickness",null),n([Object(s.serialize)()],e.prototype,"focusedBackground",null),n([Object(s.serialize)()],e.prototype,"focusedColor",null),n([Object(s.serialize)()],e.prototype,"background",null),n([Object(s.serialize)()],e.prototype,"placeholderColor",null),n([Object(s.serialize)()],e.prototype,"placeholderText",null),n([Object(s.serialize)()],e.prototype,"deadKey",null),n([Object(s.serialize)()],e.prototype,"text",null),n([Object(s.serialize)()],e.prototype,"width",null),e}(f);s._TypeStore.RegisteredTypes["BABYLON.GUI.InputText"]=T;var w=function(t){function e(e){var i=t.call(this,e)||this;return i.name=e,i._rowDefinitions=new Array,i._columnDefinitions=new Array,i._cells={},i._childControls=new Array,i}return o(e,t),Object.defineProperty(e.prototype,"columnCount",{get:function(){return this._columnDefinitions.length},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"rowCount",{get:function(){return this._rowDefinitions.length},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"children",{get:function(){return this._childControls},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"cells",{get:function(){return this._cells},enumerable:!1,configurable:!0}),e.prototype.getRowDefinition=function(t){return t<0||t>=this._rowDefinitions.length?null:this._rowDefinitions[t]},e.prototype.getColumnDefinition=function(t){return t<0||t>=this._columnDefinitions.length?null:this._columnDefinitions[t]},e.prototype.addRowDefinition=function(t,e){return void 0===e&&(e=!1),this._rowDefinitions.push(new a(t,e?a.UNITMODE_PIXEL:a.UNITMODE_PERCENTAGE)),this._markAsDirty(),this},e.prototype.addColumnDefinition=function(t,e){return void 0===e&&(e=!1),this._columnDefinitions.push(new a(t,e?a.UNITMODE_PIXEL:a.UNITMODE_PERCENTAGE)),this._markAsDirty(),this},e.prototype.setRowDefinition=function(t,e,i){if(void 0===i&&(i=!1),t<0||t>=this._rowDefinitions.length)return this;var r=this._rowDefinitions[t];return r&&r.isPixel===i&&r.internalValue===e||(this._rowDefinitions[t]=new a(e,i?a.UNITMODE_PIXEL:a.UNITMODE_PERCENTAGE),this._markAsDirty()),this},e.prototype.setColumnDefinition=function(t,e,i){if(void 0===i&&(i=!1),t<0||t>=this._columnDefinitions.length)return this;var r=this._columnDefinitions[t];return r&&r.isPixel===i&&r.internalValue===e||(this._columnDefinitions[t]=new a(e,i?a.UNITMODE_PIXEL:a.UNITMODE_PERCENTAGE),this._markAsDirty()),this},e.prototype.getChildrenAt=function(t,e){var i=this._cells[t+":"+e];return i?i.children:null},e.prototype.getChildCellInfo=function(t){return t._tag},e.prototype._removeCell=function(e,i){if(e){t.prototype.removeControl.call(this,e);for(var r=0,o=e.children;r<o.length;r++){var n=o[r],s=this._childControls.indexOf(n);-1!==s&&this._childControls.splice(s,1)}delete this._cells[i]}},e.prototype._offsetCell=function(t,e){if(this._cells[e]){this._cells[t]=this._cells[e];for(var i=0,r=this._cells[t].children;i<r.length;i++){r[i]._tag=t}delete this._cells[e]}},e.prototype.removeColumnDefinition=function(t){if(t<0||t>=this._columnDefinitions.length)return this;for(var e=0;e<this._rowDefinitions.length;e++){var i=e+":"+t,r=this._cells[i];this._removeCell(r,i)}for(e=0;e<this._rowDefinitions.length;e++)for(var o=t+1;o<this._columnDefinitions.length;o++){var n=e+":"+(o-1);i=e+":"+o;this._offsetCell(n,i)}return this._columnDefinitions.splice(t,1),this._markAsDirty(),this},e.prototype.removeRowDefinition=function(t){if(t<0||t>=this._rowDefinitions.length)return this;for(var e=0;e<this._columnDefinitions.length;e++){var i=t+":"+e,r=this._cells[i];this._removeCell(r,i)}for(e=0;e<this._columnDefinitions.length;e++)for(var o=t+1;o<this._rowDefinitions.length;o++){var n=o-1+":"+e;i=o+":"+e;this._offsetCell(n,i)}return this._rowDefinitions.splice(t,1),this._markAsDirty(),this},e.prototype.addControl=function(e,i,r){if(void 0===i&&(i=0),void 0===r&&(r=0),0===this._rowDefinitions.length&&this.addRowDefinition(1,!1),0===this._columnDefinitions.length&&this.addColumnDefinition(1,!1),-1!==this._childControls.indexOf(e))return s.Tools.Warn("Control (Name:"+e.name+", UniqueId:"+e.uniqueId+") is already associated with this grid. You must remove it before reattaching it"),this;var o=Math.min(i,this._rowDefinitions.length-1)+":"+Math.min(r,this._columnDefinitions.length-1),n=this._cells[o];return n||(n=new g(o),this._cells[o]=n,n.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,n.verticalAlignment=f.VERTICAL_ALIGNMENT_TOP,t.prototype.addControl.call(this,n)),n.addControl(e),this._childControls.push(e),e._tag=o,e.parent=this,this._markAsDirty(),this},e.prototype.removeControl=function(t){var e=this._childControls.indexOf(t);-1!==e&&this._childControls.splice(e,1);var i=this._cells[t._tag];return i&&(i.removeControl(t),t._tag=null),this._markAsDirty(),this},e.prototype._getTypeName=function(){return"Grid"},e.prototype._getGridDefinitions=function(t){for(var e=[],i=[],r=[],o=[],n=this._currentMeasure.width,s=0,a=this._currentMeasure.height,h=0,l=0,c=0,u=this._rowDefinitions;c<u.length;c++){if((g=u[c]).isPixel)a-=f=g.getValue(this._host),i[l]=f;else h+=g.internalValue;l++}var _=0;l=0;for(var d=0,p=this._rowDefinitions;d<p.length;d++){var f,g=p[d];if(o.push(_),g.isPixel)_+=g.getValue(this._host);else _+=f=g.internalValue/h*a,i[l]=f;l++}l=0;for(var b=0,m=this._columnDefinitions;b<m.length;b++){if((g=m[b]).isPixel)n-=C=g.getValue(this._host),e[l]=C;else s+=g.internalValue;l++}var y=0;l=0;for(var v=0,O=this._columnDefinitions;v<O.length;v++){var C;g=O[v];if(r.push(y),g.isPixel)y+=g.getValue(this._host);else y+=C=g.internalValue/s*n,e[l]=C;l++}t(r,o,e,i)},e.prototype._additionalProcessing=function(e,i){var r=this;this._getGridDefinitions((function(t,e,i,o){for(var n in r._cells)if(r._cells.hasOwnProperty(n)){var s=n.split(":"),a=parseInt(s[0]),h=parseInt(s[1]),l=r._cells[n];l.left=t[h]+"px",l.top=e[a]+"px",l.width=i[h]+"px",l.height=o[a]+"px",l._left.ignoreAdaptiveScaling=!0,l._top.ignoreAdaptiveScaling=!0,l._width.ignoreAdaptiveScaling=!0,l._height.ignoreAdaptiveScaling=!0}})),t.prototype._additionalProcessing.call(this,e,i)},e.prototype._flagDescendantsAsMatrixDirty=function(){for(var t in this._cells){if(this._cells.hasOwnProperty(t))this._cells[t]._markMatrixAsDirty()}},e.prototype._renderHighlightSpecific=function(e){var i=this;t.prototype._renderHighlightSpecific.call(this,e),this._getGridDefinitions((function(t,r,o,n){for(var s=0;s<t.length;s++){var a=i._currentMeasure.left+t[s]+o[s];e.beginPath(),e.moveTo(a,i._currentMeasure.top),e.lineTo(a,i._currentMeasure.top+i._currentMeasure.height),e.stroke()}for(s=0;s<r.length;s++){var h=i._currentMeasure.top+r[s]+n[s];e.beginPath(),e.moveTo(i._currentMeasure.left,h),e.lineTo(i._currentMeasure.left+i._currentMeasure.width,h),e.stroke()}})),e.restore()},e.prototype.dispose=function(){t.prototype.dispose.call(this);for(var e=0,i=this._childControls;e<i.length;e++){i[e].dispose()}this._childControls=[]},e}(g);s._TypeStore.RegisteredTypes["BABYLON.GUI.Grid"]=w;var M=function(t){function e(e){var i=t.call(this,e)||this;return i.name=e,i._value=s.Color3.Red(),i._tmpColor=new s.Color3,i._pointerStartedOnSquare=!1,i._pointerStartedOnWheel=!1,i._squareLeft=0,i._squareTop=0,i._squareSize=0,i._h=360,i._s=1,i._v=1,i._lastPointerDownID=-1,i.onValueChangedObservable=new s.Observable,i._pointerIsDown=!1,i.value=new s.Color3(.88,.1,.1),i.size="200px",i.isPointerBlocker=!0,i}return o(e,t),Object.defineProperty(e.prototype,"value",{get:function(){return this._value},set:function(t){this._value.equals(t)||(this._value.copyFrom(t),this._value.toHSVToRef(this._tmpColor),this._h=this._tmpColor.r,this._s=Math.max(this._tmpColor.g,1e-5),this._v=Math.max(this._tmpColor.b,1e-5),this._markAsDirty(),this._value.r<=e._Epsilon&&(this._value.r=0),this._value.g<=e._Epsilon&&(this._value.g=0),this._value.b<=e._Epsilon&&(this._value.b=0),this._value.r>=1-e._Epsilon&&(this._value.r=1),this._value.g>=1-e._Epsilon&&(this._value.g=1),this._value.b>=1-e._Epsilon&&(this._value.b=1),this.onValueChangedObservable.notifyObservers(this._value))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"width",{get:function(){return this._width.toString(this._host)},set:function(t){this._width.toString(this._host)!==t&&this._width.fromString(t)&&(this._height.fromString(t),this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"height",{get:function(){return this._height.toString(this._host)},set:function(t){this._height.toString(this._host)!==t&&this._height.fromString(t)&&(this._width.fromString(t),this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"size",{get:function(){return this.width},set:function(t){this.width=t},enumerable:!1,configurable:!0}),e.prototype._getTypeName=function(){return"ColorPicker"},e.prototype._preMeasure=function(t,e){t.width<t.height?this._currentMeasure.height=t.width:this._currentMeasure.width=t.height},e.prototype._updateSquareProps=function(){var t=.5*Math.min(this._currentMeasure.width,this._currentMeasure.height),e=2*(t-.2*t)/Math.sqrt(2),i=t-.5*e;this._squareLeft=this._currentMeasure.left+i,this._squareTop=this._currentMeasure.top+i,this._squareSize=e},e.prototype._drawGradientSquare=function(t,e,i,r,o,n){var s=n.createLinearGradient(e,i,r+e,i);s.addColorStop(0,"#fff"),s.addColorStop(1,"hsl("+t+", 100%, 50%)"),n.fillStyle=s,n.fillRect(e,i,r,o);var a=n.createLinearGradient(e,i,e,o+i);a.addColorStop(0,"rgba(0,0,0,0)"),a.addColorStop(1,"#000"),n.fillStyle=a,n.fillRect(e,i,r,o)},e.prototype._drawCircle=function(t,e,i,r){r.beginPath(),r.arc(t,e,i+1,0,2*Math.PI,!1),r.lineWidth=3,r.strokeStyle="#333333",r.stroke(),r.beginPath(),r.arc(t,e,i,0,2*Math.PI,!1),r.lineWidth=3,r.strokeStyle="#ffffff",r.stroke()},e.prototype._createColorWheelCanvas=function(t,e){var i=document.createElement("canvas");i.width=2*t,i.height=2*t;for(var r=i.getContext("2d"),o=r.getImageData(0,0,2*t,2*t),n=o.data,a=this._tmpColor,h=t*t,l=t-e,c=l*l,u=-t;u<t;u++)for(var _=-t;_<t;_++){var d=u*u+_*_;if(!(d>h||d<c)){var p=Math.sqrt(d),f=Math.atan2(_,u);s.Color3.HSVtoRGBToRef(180*f/Math.PI+180,p/t,1,a);var g=4*(u+t+2*(_+t)*t);n[g]=255*a.r,n[g+1]=255*a.g,n[g+2]=255*a.b;var b=.2;b=t<50?.2:t>150?.04:-.16*(t-50)/100+.2;var m=(p-l)/(t-l);n[g+3]=m<b?m/b*255:m>1-b?255*(1-(m-(1-b))/b):255}}return r.putImageData(o,0,0),i},e.prototype._draw=function(t){t.save(),this._applyStates(t);var e=.5*Math.min(this._currentMeasure.width,this._currentMeasure.height),i=.2*e,r=this._currentMeasure.left,o=this._currentMeasure.top;this._colorWheelCanvas&&this._colorWheelCanvas.width==2*e||(this._colorWheelCanvas=this._createColorWheelCanvas(e,i)),this._updateSquareProps(),(this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY)&&(t.shadowColor=this.shadowColor,t.shadowBlur=this.shadowBlur,t.shadowOffsetX=this.shadowOffsetX,t.shadowOffsetY=this.shadowOffsetY,t.fillRect(this._squareLeft,this._squareTop,this._squareSize,this._squareSize)),t.drawImage(this._colorWheelCanvas,r,o),(this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY)&&(t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0),this._drawGradientSquare(this._h,this._squareLeft,this._squareTop,this._squareSize,this._squareSize,t);var n=this._squareLeft+this._squareSize*this._s,s=this._squareTop+this._squareSize*(1-this._v);this._drawCircle(n,s,.04*e,t);var a=e-.5*i;n=r+e+Math.cos((this._h-180)*Math.PI/180)*a,s=o+e+Math.sin((this._h-180)*Math.PI/180)*a,this._drawCircle(n,s,.35*i,t),t.restore()},e.prototype._updateValueFromPointer=function(t,i){if(this._pointerStartedOnWheel){var r=.5*Math.min(this._currentMeasure.width,this._currentMeasure.height),o=r+this._currentMeasure.left,n=r+this._currentMeasure.top;this._h=180*Math.atan2(i-n,t-o)/Math.PI+180}else this._pointerStartedOnSquare&&(this._updateSquareProps(),this._s=(t-this._squareLeft)/this._squareSize,this._v=1-(i-this._squareTop)/this._squareSize,this._s=Math.min(this._s,1),this._s=Math.max(this._s,e._Epsilon),this._v=Math.min(this._v,1),this._v=Math.max(this._v,e._Epsilon));s.Color3.HSVtoRGBToRef(this._h,this._s,this._v,this._tmpColor),this.value=this._tmpColor},e.prototype._isPointOnSquare=function(t,e){this._updateSquareProps();var i=this._squareLeft,r=this._squareTop,o=this._squareSize;return t>=i&&t<=i+o&&e>=r&&e<=r+o},e.prototype._isPointOnWheel=function(t,e){var i=.5*Math.min(this._currentMeasure.width,this._currentMeasure.height),r=i-.2*i,o=t-(i+this._currentMeasure.left),n=e-(i+this._currentMeasure.top),s=o*o+n*n;return s<=i*i&&s>=r*r},e.prototype._onPointerDown=function(e,i,r,o,n){if(!t.prototype._onPointerDown.call(this,e,i,r,o,n))return!1;this._pointerIsDown=!0,this._pointerStartedOnSquare=!1,this._pointerStartedOnWheel=!1,this._invertTransformMatrix.transformCoordinates(i.x,i.y,this._transformedPosition);var s=this._transformedPosition.x,a=this._transformedPosition.y;return this._isPointOnSquare(s,a)?this._pointerStartedOnSquare=!0:this._isPointOnWheel(s,a)&&(this._pointerStartedOnWheel=!0),this._updateValueFromPointer(s,a),this._host._capturingControl[r]=this,this._lastPointerDownID=r,!0},e.prototype._onPointerMove=function(e,i,r,o){if(r==this._lastPointerDownID){this._invertTransformMatrix.transformCoordinates(i.x,i.y,this._transformedPosition);var n=this._transformedPosition.x,s=this._transformedPosition.y;this._pointerIsDown&&this._updateValueFromPointer(n,s),t.prototype._onPointerMove.call(this,e,i,r,o)}},e.prototype._onPointerUp=function(e,i,r,o,n,s){this._pointerIsDown=!1,delete this._host._capturingControl[r],t.prototype._onPointerUp.call(this,e,i,r,o,n,s)},e.prototype._onCanvasBlur=function(){this._forcePointerUp(),t.prototype._onCanvasBlur.call(this)},e.ShowPickerDialogAsync=function(t,i){return new Promise((function(r,o){i.pickerWidth=i.pickerWidth||"640px",i.pickerHeight=i.pickerHeight||"400px",i.headerHeight=i.headerHeight||"35px",i.lastColor=i.lastColor||"#000000",i.swatchLimit=i.swatchLimit||20,i.numSwatchesPerLine=i.numSwatchesPerLine||10;var n,a,h,l,c,u,_,d,p,g,b,v,C,x,P,M,I,k,B,S=i.swatchLimit/i.numSwatchesPerLine,A=parseFloat(i.pickerWidth)/i.numSwatchesPerLine,E=Math.floor(.25*A),D=E*(i.numSwatchesPerLine+1),L=Math.floor((parseFloat(i.pickerWidth)-D)/i.numSwatchesPerLine),R=L*S+E*(S+1),F=(parseInt(i.pickerHeight)+R+Math.floor(.25*L)).toString()+"px",N=s.Color3.FromHexString("#dddddd"),z=N.r+N.g+N.b,V=["R","G","B"],j=!1;function H(t,e){B=e;var i=t.toHexString();if(I.background=i,g.name!=B&&(g.text=Math.floor(255*t.r).toString()),b.name!=B&&(b.text=Math.floor(255*t.g).toString()),v.name!=B&&(v.text=Math.floor(255*t.b).toString()),C.name!=B&&(C.text=t.r.toString()),x.name!=B&&(x.text=t.g.toString()),P.name!=B&&(P.text=t.b.toString()),M.name!=B){var r=i.split("#");M.text=r[1]}p.name!=B&&(p.value=t)}function W(t,e){var i=t.text;if(/[^0-9]/g.test(i))t.text=k;else if(""!=i&&(Math.floor(parseInt(i))<0?i="0":Math.floor(parseInt(i))>255?i="255":isNaN(parseInt(i))&&(i="0")),B==t.name&&(k=i),""!=i){i=parseInt(i).toString(),t.text=i;var r=s.Color3.FromHexString(I.background);B==t.name&&H("r"==e?new s.Color3(parseInt(i)/255,r.g,r.b):"g"==e?new s.Color3(r.r,parseInt(i)/255,r.b):new s.Color3(r.r,r.g,parseInt(i)/255),t.name)}}function G(t,e){var i=t.text;if(/[^0-9\.]/g.test(i))t.text=k;else{""!=i&&"."!=i&&0!=parseFloat(i)&&(parseFloat(i)<0?i="0.0":parseFloat(i)>1?i="1.0":isNaN(parseFloat(i))&&(i="0.0")),B==t.name&&(k=i),""!=i&&"."!=i&&0!=parseFloat(i)?(i=parseFloat(i).toString(),t.text=i):i="0.0";var r=s.Color3.FromHexString(I.background);B==t.name&&H("r"==e?new s.Color3(parseFloat(i),r.g,r.b):"g"==e?new s.Color3(r.r,parseFloat(i),r.b):new s.Color3(r.r,r.g,parseFloat(i)),t.name)}}function U(){if(i.savedColors&&i.savedColors[_]){if(j)var t="b";else t="";var e=O.CreateSimpleButton("Swatch_"+_,t);e.fontFamily="BabylonJSglyphs";var r=s.Color3.FromHexString(i.savedColors[_]),o=r.r+r.g+r.b;e.color=o>z?"#aaaaaa":"#ffffff",e.fontSize=Math.floor(.7*L),e.textBlock.verticalAlignment=f.VERTICAL_ALIGNMENT_CENTER,e.height=e.width=L.toString()+"px",e.background=i.savedColors[_],e.thickness=2;var n=_;return e.pointerDownAnimation=function(){e.thickness=4},e.pointerUpAnimation=function(){e.thickness=3},e.pointerEnterAnimation=function(){e.thickness=3},e.pointerOutAnimation=function(){e.thickness=2},e.onPointerClickObservable.add((function(){var t;j?(t=n,i.savedColors&&i.savedColors.splice(t,1),i.savedColors&&0==i.savedColors.length&&(Q(!1),j=!1),Y("",It)):i.savedColors&&H(s.Color3.FromHexString(i.savedColors[n]),e.name)})),e}return null}function X(t){if(void 0!==t&&(j=t),j){for(var e=0;e<d.children.length;e++){d.children[e].textBlock.text="b"}void 0!==h&&(h.textBlock.text="Done")}else{for(e=0;e<d.children.length;e++){d.children[e].textBlock.text=""}void 0!==h&&(h.textBlock.text="Edit")}}function Y(t,e){if(i.savedColors){""!=t&&i.savedColors.push(t),_=0,d.clearControls();var r=Math.ceil(i.savedColors.length/i.numSwatchesPerLine);if(0==r)var o=0;else o=r+1;if(d.rowCount!=r+o){for(var n=d.rowCount,s=0;s<n;s++)d.removeRowDefinition(0);for(s=0;s<r+o;s++)s%2?d.addRowDefinition(L,!0):d.addRowDefinition(E,!0)}d.height=(L*r+o*E).toString()+"px";for(var a=1,h=1;a<r+o;a+=2,h++){if(i.savedColors.length>h*i.numSwatchesPerLine)var l=i.numSwatchesPerLine;else l=i.savedColors.length-(h-1)*i.numSwatchesPerLine;for(var c=Math.min(Math.max(l,0),i.numSwatchesPerLine),u=0,p=1;u<c;u++)if(!(u>i.numSwatchesPerLine)){var f=U();null!=f&&(d.addControl(f,a,p),p+=2,_++)}}i.savedColors.length>=i.swatchLimit?K(e,!0):K(e,!1)}}function Q(t){t?((h=O.CreateSimpleButton("butEdit","Edit")).width=l,h.height=c,h.left=Math.floor(.1*parseInt(l)).toString()+"px",h.top=(-1*parseFloat(h.left)).toString()+"px",h.verticalAlignment=f.VERTICAL_ALIGNMENT_BOTTOM,h.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,h.thickness=2,h.color="#c0c0c0",h.fontSize=a,h.background="#535353",h.onPointerEnterObservable.add((function(){h.background="#414141"})),h.onPointerOutObservable.add((function(){h.background="#535353"})),h.pointerDownAnimation=function(){h.background="515151"},h.pointerUpAnimation=function(){h.background="#414141"},h.onPointerClickObservable.add((function(){j=!j,X()})),ct.addControl(h,1,0)):ct.removeControl(h)}function K(t,e){e?(t.color="#555555",t.background="#454545"):(t.color="#c0c0c0",t.background="#535353")}function q(e){i.savedColors&&i.savedColors.length>0?r({savedColors:i.savedColors,pickedColor:e}):r({pickedColor:e}),t.removeControl(Z)}var Z=new w;if(Z.name="Dialog Container",Z.width=i.pickerWidth,i.savedColors){Z.height=F;var J=parseInt(i.pickerHeight)/parseInt(F);Z.addRowDefinition(J,!1),Z.addRowDefinition(1-J,!1)}else Z.height=i.pickerHeight,Z.addRowDefinition(1,!1);if(t.addControl(Z),i.savedColors){(d=new w).name="Swatch Drawer",d.verticalAlignment=f.VERTICAL_ALIGNMENT_TOP,d.background="#535353",d.width=i.pickerWidth;var $=i.savedColors.length/i.numSwatchesPerLine;if(0==$)var tt=0;else tt=$+1;d.height=(L*$+tt*E).toString()+"px",d.top=Math.floor(.25*L).toString()+"px";for(var et=0;et<2*Math.ceil(i.savedColors.length/i.numSwatchesPerLine)+1;et++)et%2!=0?d.addRowDefinition(L,!0):d.addRowDefinition(E,!0);for(et=0;et<2*i.numSwatchesPerLine+1;et++)et%2!=0?d.addColumnDefinition(L,!0):d.addColumnDefinition(E,!0);Z.addControl(d,1,0)}var it=new w;it.name="Picker Panel",it.height=i.pickerHeight;var rt=parseInt(i.headerHeight)/parseInt(i.pickerHeight),ot=[rt,1-rt];it.addRowDefinition(ot[0],!1),it.addRowDefinition(ot[1],!1),Z.addControl(it,0,0);var nt=new m;nt.name="Dialogue Header Bar",nt.background="#cccccc",nt.thickness=0,it.addControl(nt,0,0);var st=O.CreateSimpleButton("closeButton","a");st.fontFamily="BabylonJSglyphs";var at=s.Color3.FromHexString(nt.background);n=new s.Color3(1-at.r,1-at.g,1-at.b),st.color=n.toHexString(),st.fontSize=Math.floor(.6*parseInt(i.headerHeight)),st.textBlock.textVerticalAlignment=f.VERTICAL_ALIGNMENT_CENTER,st.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_RIGHT,st.height=st.width=i.headerHeight,st.background=nt.background,st.thickness=0,st.pointerDownAnimation=function(){},st.pointerUpAnimation=function(){st.background=nt.background},st.pointerEnterAnimation=function(){st.color=nt.background,st.background="red"},st.pointerOutAnimation=function(){st.color=n.toHexString(),st.background=nt.background},st.onPointerClickObservable.add((function(){q(Ct.background)})),it.addControl(st,0,0);var ht=new w;ht.name="Dialogue Body",ht.background="#535353";var lt=[.4375,.5625];ht.addRowDefinition(1,!1),ht.addColumnDefinition(lt[0],!1),ht.addColumnDefinition(lt[1],!1),it.addControl(ht,1,0);var ct=new w;ct.name="Picker Grid",ct.addRowDefinition(.85,!1),ct.addRowDefinition(.15,!1),ht.addControl(ct,0,0),(p=new e).name="GUI Color Picker",i.pickerHeight<i.pickerWidth?p.width=.89:p.height=.89,p.value=s.Color3.FromHexString(i.lastColor),p.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_CENTER,p.verticalAlignment=f.VERTICAL_ALIGNMENT_CENTER,p.onPointerDownObservable.add((function(){B=p.name,k="",X(!1)})),p.onValueChangedObservable.add((function(t){B==p.name&&H(t,p.name)})),ct.addControl(p,0,0);var ut=new w;ut.name="Dialogue Right Half",ut.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT;var _t=[.514,.486];ut.addRowDefinition(_t[0],!1),ut.addRowDefinition(_t[1],!1),ht.addControl(ut,1,1);var dt=new w;dt.name="Swatches and Buttons";var pt=[.417,.583];dt.addRowDefinition(1,!1),dt.addColumnDefinition(pt[0],!1),dt.addColumnDefinition(pt[1],!1),ut.addControl(dt,0,0);var ft=new w;ft.name="New and Current Swatches";var gt=[.04,.16,.64,.16];ft.addRowDefinition(gt[0],!1),ft.addRowDefinition(gt[1],!1),ft.addRowDefinition(gt[2],!1),ft.addRowDefinition(gt[3],!1),dt.addControl(ft,0,0);var bt=new w;bt.name="Active Swatches",bt.width=.67,bt.addRowDefinition(.5,!1),bt.addRowDefinition(.5,!1),ft.addControl(bt,2,0);var mt=Math.floor(parseInt(i.pickerWidth)*lt[1]*pt[0]*.11),yt=Math.floor(parseInt(i.pickerHeight)*ot[1]*_t[0]*gt[1]*.5);if(i.pickerWidth>i.pickerHeight)var vt=yt;else vt=mt;var Ot=new y;Ot.text="new",Ot.name="New Color Label",Ot.color="#c0c0c0",Ot.fontSize=vt,ft.addControl(Ot,1,0),(I=new m).name="New Color Swatch",I.background=i.lastColor,I.thickness=0,bt.addControl(I,0,0);var Ct=O.CreateSimpleButton("currentSwatch","");Ct.background=i.lastColor,Ct.thickness=0,Ct.onPointerClickObservable.add((function(){H(s.Color3.FromHexString(Ct.background),Ct.name),X(!1)})),Ct.pointerDownAnimation=function(){},Ct.pointerUpAnimation=function(){},Ct.pointerEnterAnimation=function(){},Ct.pointerOutAnimation=function(){},bt.addControl(Ct,1,0);var xt=new m;xt.name="Swatch Outline",xt.width=.67,xt.thickness=2,xt.color="#404040",xt.isHitTestVisible=!1,ft.addControl(xt,2,0);var Pt=new y;Pt.name="Current Color Label",Pt.text="current",Pt.color="#c0c0c0",Pt.fontSize=vt,ft.addControl(Pt,3,0);var Tt=new w;Tt.name="Button Grid",Tt.height=.8;Tt.addRowDefinition(1/3,!1),Tt.addRowDefinition(1/3,!1),Tt.addRowDefinition(1/3,!1),dt.addControl(Tt,0,1),l=Math.floor(parseInt(i.pickerWidth)*lt[1]*pt[1]*.67).toString()+"px",c=Math.floor(parseInt(i.pickerHeight)*ot[1]*_t[0]*(parseFloat(Tt.height.toString())/100)*(1/3)*.7).toString()+"px",a=parseFloat(l)>parseFloat(c)?Math.floor(.45*parseFloat(c)):Math.floor(.11*parseFloat(l));var wt=O.CreateSimpleButton("butOK","OK");wt.width=l,wt.height=c,wt.verticalAlignment=f.VERTICAL_ALIGNMENT_CENTER,wt.thickness=2,wt.color="#c0c0c0",wt.fontSize=a,wt.background="#535353",wt.onPointerEnterObservable.add((function(){wt.background="#414141"})),wt.onPointerOutObservable.add((function(){wt.background="#535353"})),wt.pointerDownAnimation=function(){wt.background="515151"},wt.pointerUpAnimation=function(){wt.background="#414141"},wt.onPointerClickObservable.add((function(){X(!1),q(I.background)})),Tt.addControl(wt,0,0);var Mt=O.CreateSimpleButton("butCancel","Cancel");if(Mt.width=l,Mt.height=c,Mt.verticalAlignment=f.VERTICAL_ALIGNMENT_CENTER,Mt.thickness=2,Mt.color="#c0c0c0",Mt.fontSize=a,Mt.background="#535353",Mt.onPointerEnterObservable.add((function(){Mt.background="#414141"})),Mt.onPointerOutObservable.add((function(){Mt.background="#535353"})),Mt.pointerDownAnimation=function(){Mt.background="515151"},Mt.pointerUpAnimation=function(){Mt.background="#414141"},Mt.onPointerClickObservable.add((function(){X(!1),q(Ct.background)})),Tt.addControl(Mt,1,0),i.savedColors){var It=O.CreateSimpleButton("butSave","Save");It.width=l,It.height=c,It.verticalAlignment=f.VERTICAL_ALIGNMENT_CENTER,It.thickness=2,It.fontSize=a,i.savedColors.length<i.swatchLimit?(It.color="#c0c0c0",It.background="#535353"):K(It,!0),It.onPointerEnterObservable.add((function(){i.savedColors&&i.savedColors.length<i.swatchLimit&&(It.background="#414141")})),It.onPointerOutObservable.add((function(){i.savedColors&&i.savedColors.length<i.swatchLimit&&(It.background="#535353")})),It.pointerDownAnimation=function(){i.savedColors&&i.savedColors.length<i.swatchLimit&&(It.background="515151")},It.pointerUpAnimation=function(){i.savedColors&&i.savedColors.length<i.swatchLimit&&(It.background="#414141")},It.onPointerClickObservable.add((function(){i.savedColors&&(0==i.savedColors.length&&Q(!0),i.savedColors.length<i.swatchLimit&&Y(I.background,It),X(!1))})),i.savedColors.length>0&&Q(!0),Tt.addControl(It,2,0)}var kt=new w;kt.name="Dialog Lower Right",kt.addRowDefinition(.02,!1),kt.addRowDefinition(.63,!1),kt.addRowDefinition(.21,!1),kt.addRowDefinition(.14,!1),ut.addControl(kt,1,0),u=s.Color3.FromHexString(i.lastColor);var Bt=new w;Bt.name="RGB Values",Bt.width=.82,Bt.verticalAlignment=f.VERTICAL_ALIGNMENT_CENTER,Bt.addRowDefinition(1/3,!1),Bt.addRowDefinition(1/3,!1),Bt.addRowDefinition(1/3,!1),Bt.addColumnDefinition(.1,!1),Bt.addColumnDefinition(.2,!1),Bt.addColumnDefinition(.7,!1),kt.addControl(Bt,1,0);for(et=0;et<V.length;et++){(St=new y).text=V[et],St.color="#c0c0c0",St.fontSize=a,Bt.addControl(St,et,0)}(g=new T).width=.83,g.height=.72,g.name="rIntField",g.fontSize=a,g.text=(255*u.r).toString(),g.color="#f0f0f0",g.background="#454545",g.onFocusObservable.add((function(){B=g.name,k=g.text,X(!1)})),g.onBlurObservable.add((function(){""==g.text&&(g.text="0"),W(g,"r"),B==g.name&&(B="")})),g.onTextChangedObservable.add((function(){B==g.name&&W(g,"r")})),Bt.addControl(g,0,1),(b=new T).width=.83,b.height=.72,b.name="gIntField",b.fontSize=a,b.text=(255*u.g).toString(),b.color="#f0f0f0",b.background="#454545",b.onFocusObservable.add((function(){B=b.name,k=b.text,X(!1)})),b.onBlurObservable.add((function(){""==b.text&&(b.text="0"),W(b,"g"),B==b.name&&(B="")})),b.onTextChangedObservable.add((function(){B==b.name&&W(b,"g")})),Bt.addControl(b,1,1),(v=new T).width=.83,v.height=.72,v.name="bIntField",v.fontSize=a,v.text=(255*u.b).toString(),v.color="#f0f0f0",v.background="#454545",v.onFocusObservable.add((function(){B=v.name,k=v.text,X(!1)})),v.onBlurObservable.add((function(){""==v.text&&(v.text="0"),W(v,"b"),B==v.name&&(B="")})),v.onTextChangedObservable.add((function(){B==v.name&&W(v,"b")})),Bt.addControl(v,2,1),(C=new T).width=.95,C.height=.72,C.name="rDecField",C.fontSize=a,C.text=u.r.toString(),C.color="#f0f0f0",C.background="#454545",C.onFocusObservable.add((function(){B=C.name,k=C.text,X(!1)})),C.onBlurObservable.add((function(){0!=parseFloat(C.text)&&""!=C.text||(C.text="0",G(C,"r")),B==C.name&&(B="")})),C.onTextChangedObservable.add((function(){B==C.name&&G(C,"r")})),Bt.addControl(C,0,2),(x=new T).width=.95,x.height=.72,x.name="gDecField",x.fontSize=a,x.text=u.g.toString(),x.color="#f0f0f0",x.background="#454545",x.onFocusObservable.add((function(){B=x.name,k=x.text,X(!1)})),x.onBlurObservable.add((function(){0!=parseFloat(x.text)&&""!=x.text||(x.text="0",G(x,"g")),B==x.name&&(B="")})),x.onTextChangedObservable.add((function(){B==x.name&&G(x,"g")})),Bt.addControl(x,1,2),(P=new T).width=.95,P.height=.72,P.name="bDecField",P.fontSize=a,P.text=u.b.toString(),P.color="#f0f0f0",P.background="#454545",P.onFocusObservable.add((function(){B=P.name,k=P.text,X(!1)})),P.onBlurObservable.add((function(){0!=parseFloat(P.text)&&""!=P.text||(P.text="0",G(P,"b")),B==P.name&&(B="")})),P.onTextChangedObservable.add((function(){B==P.name&&G(P,"b")})),Bt.addControl(P,2,2);var St,At=new w;At.name="Hex Value",At.width=.82,At.addRowDefinition(1,!1),At.addColumnDefinition(.1,!1),At.addColumnDefinition(.9,!1),kt.addControl(At,2,0),(St=new y).text="#",St.color="#c0c0c0",St.fontSize=a,At.addControl(St,0,0),(M=new T).width=.96,M.height=.72,M.name="hexField",M.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_CENTER,M.fontSize=a;var Et=i.lastColor.split("#");M.text=Et[1],M.color="#f0f0f0",M.background="#454545",M.onFocusObservable.add((function(){B=M.name,k=M.text,X(!1)})),M.onBlurObservable.add((function(){if(3==M.text.length){var t=M.text.split("");M.text=t[0]+t[0]+t[1]+t[1]+t[2]+t[2]}""==M.text&&(M.text="000000",H(s.Color3.FromHexString(M.text),"b")),B==M.name&&(B="")})),M.onTextChangedObservable.add((function(){var t=M.text,e=/[^0-9A-F]/i.test(t);if((M.text.length>6||e)&&B==M.name)M.text=k;else{if(M.text.length<6)for(var i=6-M.text.length,r=0;r<i;r++)t="0"+t;if(3==M.text.length){var o=M.text.split("");t=o[0]+o[0]+o[1]+o[1]+o[2]+o[2]}t="#"+t,B==M.name&&(k=M.text,H(s.Color3.FromHexString(t),M.name))}})),At.addControl(M,0,1),i.savedColors&&i.savedColors.length>0&&Y("",It)}))},e._Epsilon=1e-6,n([Object(s.serialize)()],e.prototype,"value",null),n([Object(s.serialize)()],e.prototype,"width",null),n([Object(s.serialize)()],e.prototype,"height",null),n([Object(s.serialize)()],e.prototype,"size",null),e}(f);s._TypeStore.RegisteredTypes["BABYLON.GUI.ColorPicker"]=M;var I=function(t){function e(e){var i=t.call(this,e)||this;return i.name=e,i._thickness=1,i}return o(e,t),Object.defineProperty(e.prototype,"thickness",{get:function(){return this._thickness},set:function(t){this._thickness!==t&&(this._thickness=t,this._markAsDirty())},enumerable:!1,configurable:!0}),e.prototype._getTypeName=function(){return"Ellipse"},e.prototype._localDraw=function(t){t.save(),(this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY)&&(t.shadowColor=this.shadowColor,t.shadowBlur=this.shadowBlur,t.shadowOffsetX=this.shadowOffsetX,t.shadowOffsetY=this.shadowOffsetY),f.drawEllipse(this._currentMeasure.left+this._currentMeasure.width/2,this._currentMeasure.top+this._currentMeasure.height/2,this._currentMeasure.width/2-this._thickness/2,this._currentMeasure.height/2-this._thickness/2,t),this._background&&(t.fillStyle=this._background,t.fill()),(this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY)&&(t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0),this._thickness&&(this.color&&(t.strokeStyle=this.color),t.lineWidth=this._thickness,t.stroke()),t.restore()},e.prototype._additionalProcessing=function(e,i){t.prototype._additionalProcessing.call(this,e,i),this._measureForChildren.width-=2*this._thickness,this._measureForChildren.height-=2*this._thickness,this._measureForChildren.left+=this._thickness,this._measureForChildren.top+=this._thickness},e.prototype._clipForChildren=function(t){f.drawEllipse(this._currentMeasure.left+this._currentMeasure.width/2,this._currentMeasure.top+this._currentMeasure.height/2,this._currentMeasure.width/2,this._currentMeasure.height/2,t),t.clip()},n([Object(s.serialize)()],e.prototype,"thickness",null),e}(g);s._TypeStore.RegisteredTypes["BABYLON.GUI.Ellipse"]=I;var k=function(t){function e(e){var i=t.call(this,e)||this;return i.name=e,i.focusedColor=null,i._isFocused=!1,i._unfocusedColor=null,i.onFocusObservable=new s.Observable,i.onBlurObservable=new s.Observable,i.onKeyboardEventProcessedObservable=new s.Observable,i._unfocusedColor=i.color,i}return o(e,t),e.prototype.onBlur=function(){this._isFocused&&(this._isFocused=!1,this.focusedColor&&null!=this._unfocusedColor&&(this.color=this._unfocusedColor),this.onBlurObservable.notifyObservers(this))},e.prototype.onFocus=function(){this._isFocused=!0,this.focusedColor&&(this._unfocusedColor=this.color,this.color=this.focusedColor),this.onFocusObservable.notifyObservers(this)},e.prototype.keepsFocusWith=function(){return null},e.prototype.focus=function(){this._host.moveFocusToControl(this)},e.prototype.blur=function(){this._host.focusedControl=null},e.prototype.processKeyboard=function(t){this.onKeyboardEventProcessedObservable.notifyObservers(t,-1,this)},e.prototype._onPointerDown=function(e,i,r,o,n){return this.focus(),t.prototype._onPointerDown.call(this,e,i,r,o,n)},e.prototype.displose=function(){t.prototype.dispose.call(this),this.onBlurObservable.clear(),this.onFocusObservable.clear(),this.onKeyboardEventProcessedObservable.clear()},e}(O);s._TypeStore.RegisteredTypes["BABYLON.GUI.FocusableButton"]=k;var B=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype._beforeRenderText=function(t){for(var e=new P,i="",r=0;r<t.length;r++)i+="•";return e.text=i,e},e}(T);s._TypeStore.RegisteredTypes["BABYLON.GUI.InputPassword"]=B;var S=function(t){function e(e){var i=t.call(this,e)||this;return i.name=e,i._lineWidth=1,i._x1=new a(0),i._y1=new a(0),i._x2=new a(0),i._y2=new a(0),i._dash=new Array,i._automaticSize=!0,i.isHitTestVisible=!1,i._horizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,i._verticalAlignment=f.VERTICAL_ALIGNMENT_TOP,i}return o(e,t),Object.defineProperty(e.prototype,"dash",{get:function(){return this._dash},set:function(t){this._dash!==t&&(this._dash=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"connectedControl",{get:function(){return this._connectedControl},set:function(t){var e=this;this._connectedControl!==t&&(this._connectedControlDirtyObserver&&this._connectedControl&&(this._connectedControl.onDirtyObservable.remove(this._connectedControlDirtyObserver),this._connectedControlDirtyObserver=null),t&&(this._connectedControlDirtyObserver=t.onDirtyObservable.add((function(){return e._markAsDirty()}))),this._connectedControl=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"x1",{get:function(){return this._x1.toString(this._host)},set:function(t){this._x1.toString(this._host)!==t&&this._x1.fromString(t)&&this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"y1",{get:function(){return this._y1.toString(this._host)},set:function(t){this._y1.toString(this._host)!==t&&this._y1.fromString(t)&&this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"x2",{get:function(){return this._x2.toString(this._host)},set:function(t){this._x2.toString(this._host)!==t&&this._x2.fromString(t)&&this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"y2",{get:function(){return this._y2.toString(this._host)},set:function(t){this._y2.toString(this._host)!==t&&this._y2.fromString(t)&&this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"lineWidth",{get:function(){return this._lineWidth},set:function(t){this._lineWidth!==t&&(this._lineWidth=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"horizontalAlignment",{set:function(t){},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"verticalAlignment",{set:function(t){},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"_effectiveX2",{get:function(){return(this._connectedControl?this._connectedControl.centerX:0)+this._x2.getValue(this._host)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"_effectiveY2",{get:function(){return(this._connectedControl?this._connectedControl.centerY:0)+this._y2.getValue(this._host)},enumerable:!1,configurable:!0}),e.prototype._getTypeName=function(){return"Line"},e.prototype._draw=function(t){t.save(),(this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY)&&(t.shadowColor=this.shadowColor,t.shadowBlur=this.shadowBlur,t.shadowOffsetX=this.shadowOffsetX,t.shadowOffsetY=this.shadowOffsetY),this._applyStates(t),t.strokeStyle=this.color,t.lineWidth=this._lineWidth,t.setLineDash(this._dash),t.beginPath(),t.moveTo(this._cachedParentMeasure.left+this._x1.getValue(this._host),this._cachedParentMeasure.top+this._y1.getValue(this._host)),t.lineTo(this._cachedParentMeasure.left+this._effectiveX2,this._cachedParentMeasure.top+this._effectiveY2),t.stroke(),t.restore()},e.prototype._measure=function(){this._currentMeasure.width=Math.abs(this._x1.getValue(this._host)-this._effectiveX2)+this._lineWidth,this._currentMeasure.height=Math.abs(this._y1.getValue(this._host)-this._effectiveY2)+this._lineWidth},e.prototype._computeAlignment=function(t,e){this._currentMeasure.left=t.left+Math.min(this._x1.getValue(this._host),this._effectiveX2)-this._lineWidth/2,this._currentMeasure.top=t.top+Math.min(this._y1.getValue(this._host),this._effectiveY2)-this._lineWidth/2},e.prototype.moveToVector3=function(t,e,i){if(void 0===i&&(i=!1),this._host&&this.parent===this._host._rootContainer){var r=this._host._getGlobalViewport(e),o=s.Vector3.Project(t,s.Matrix.Identity(),e.getTransformMatrix(),r);this._moveToProjectedPosition(o,i),o.z<0||o.z>1?this.notRenderable=!0:this.notRenderable=!1}else s.Tools.Error("Cannot move a control to a vector3 if the control is not at root level")},e.prototype._moveToProjectedPosition=function(t,e){void 0===e&&(e=!1);var i=t.x+this._linkOffsetX.getValue(this._host)+"px",r=t.y+this._linkOffsetY.getValue(this._host)+"px";e?(this.x2=i,this.y2=r,this._x2.ignoreAdaptiveScaling=!0,this._y2.ignoreAdaptiveScaling=!0):(this.x1=i,this.y1=r,this._x1.ignoreAdaptiveScaling=!0,this._y1.ignoreAdaptiveScaling=!0)},n([Object(s.serialize)()],e.prototype,"dash",null),n([Object(s.serialize)()],e.prototype,"x1",null),n([Object(s.serialize)()],e.prototype,"y1",null),n([Object(s.serialize)()],e.prototype,"x2",null),n([Object(s.serialize)()],e.prototype,"y2",null),n([Object(s.serialize)()],e.prototype,"lineWidth",null),e}(f);s._TypeStore.RegisteredTypes["BABYLON.GUI.Line"]=S;var A=function(){function t(t){this._multiLine=t,this._x=new a(0),this._y=new a(0),this._point=new s.Vector3(0,0,0)}return Object.defineProperty(t.prototype,"x",{get:function(){return this._x.toString(this._multiLine._host)},set:function(t){this._x.toString(this._multiLine._host)!==t&&this._x.fromString(t)&&this._multiLine._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"y",{get:function(){return this._y.toString(this._multiLine._host)},set:function(t){this._y.toString(this._multiLine._host)!==t&&this._y.fromString(t)&&this._multiLine._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"control",{get:function(){return this._control},set:function(t){this._control!==t&&(this._control&&this._controlObserver&&(this._control.onDirtyObservable.remove(this._controlObserver),this._controlObserver=null),this._control=t,this._control&&(this._controlObserver=this._control.onDirtyObservable.add(this._multiLine.onPointUpdate)),this._multiLine._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"mesh",{get:function(){return this._mesh},set:function(t){this._mesh!==t&&(this._mesh&&this._meshObserver&&this._mesh.getScene().onAfterCameraRenderObservable.remove(this._meshObserver),this._mesh=t,this._mesh&&(this._meshObserver=this._mesh.getScene().onAfterCameraRenderObservable.add(this._multiLine.onPointUpdate)),this._multiLine._markAsDirty())},enumerable:!1,configurable:!0}),t.prototype.resetLinks=function(){this.control=null,this.mesh=null},t.prototype.translate=function(){return this._point=this._translatePoint(),this._point},t.prototype._translatePoint=function(){if(null!=this._mesh)return this._multiLine._host.getProjectedPositionWithZ(this._mesh.getBoundingInfo().boundingSphere.center,this._mesh.getWorldMatrix());if(null!=this._control)return new s.Vector3(this._control.centerX,this._control.centerY,1-s.Epsilon);var t=this._multiLine._host,e=this._x.getValueInPixel(t,Number(t._canvas.width)),i=this._y.getValueInPixel(t,Number(t._canvas.height));return new s.Vector3(e,i,1-s.Epsilon)},t.prototype.dispose=function(){this.resetLinks()},t}(),E=function(t){function e(e){var i=t.call(this,e)||this;return i.name=e,i._lineWidth=1,i.onPointUpdate=function(){i._markAsDirty()},i._automaticSize=!0,i.isHitTestVisible=!1,i._horizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,i._verticalAlignment=f.VERTICAL_ALIGNMENT_TOP,i._dash=[],i._points=[],i}return o(e,t),Object.defineProperty(e.prototype,"dash",{get:function(){return this._dash},set:function(t){this._dash!==t&&(this._dash=t,this._markAsDirty())},enumerable:!1,configurable:!0}),e.prototype.getAt=function(t){return this._points[t]||(this._points[t]=new A(this)),this._points[t]},e.prototype.add=function(){for(var t=this,e=[],i=0;i<arguments.length;i++)e[i]=arguments[i];return e.map((function(e){return t.push(e)}))},e.prototype.push=function(t){var e=this.getAt(this._points.length);return null==t||(t instanceof s.AbstractMesh?e.mesh=t:t instanceof f?e.control=t:null!=t.x&&null!=t.y&&(e.x=t.x,e.y=t.y)),e},e.prototype.remove=function(t){var e;if(t instanceof A){if(-1===(e=this._points.indexOf(t)))return}else e=t;var i=this._points[e];i&&(i.dispose(),this._points.splice(e,1))},e.prototype.reset=function(){for(;this._points.length>0;)this.remove(this._points.length-1)},e.prototype.resetLinks=function(){this._points.forEach((function(t){null!=t&&t.resetLinks()}))},Object.defineProperty(e.prototype,"lineWidth",{get:function(){return this._lineWidth},set:function(t){this._lineWidth!==t&&(this._lineWidth=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"horizontalAlignment",{set:function(t){},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"verticalAlignment",{set:function(t){},enumerable:!1,configurable:!0}),e.prototype._getTypeName=function(){return"MultiLine"},e.prototype._draw=function(t,e){t.save(),(this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY)&&(t.shadowColor=this.shadowColor,t.shadowBlur=this.shadowBlur,t.shadowOffsetX=this.shadowOffsetX,t.shadowOffsetY=this.shadowOffsetY),this._applyStates(t),t.strokeStyle=this.color,t.lineWidth=this._lineWidth,t.setLineDash(this._dash),t.beginPath();var i,r=!0;this._points.forEach((function(e){e&&(r?(t.moveTo(e._point.x,e._point.y),r=!1):e._point.z<1&&i.z<1?t.lineTo(e._point.x,e._point.y):t.moveTo(e._point.x,e._point.y),i=e._point)})),t.stroke(),t.restore()},e.prototype._additionalProcessing=function(t,e){var i=this;this._minX=null,this._minY=null,this._maxX=null,this._maxY=null,this._points.forEach((function(t,e){t&&(t.translate(),(null==i._minX||t._point.x<i._minX)&&(i._minX=t._point.x),(null==i._minY||t._point.y<i._minY)&&(i._minY=t._point.y),(null==i._maxX||t._point.x>i._maxX)&&(i._maxX=t._point.x),(null==i._maxY||t._point.y>i._maxY)&&(i._maxY=t._point.y))})),null==this._minX&&(this._minX=0),null==this._minY&&(this._minY=0),null==this._maxX&&(this._maxX=0),null==this._maxY&&(this._maxY=0)},e.prototype._measure=function(){null!=this._minX&&null!=this._maxX&&null!=this._minY&&null!=this._maxY&&(this._currentMeasure.width=Math.abs(this._maxX-this._minX)+this._lineWidth,this._currentMeasure.height=Math.abs(this._maxY-this._minY)+this._lineWidth)},e.prototype._computeAlignment=function(t,e){null!=this._minX&&null!=this._minY&&(this._currentMeasure.left=this._minX-this._lineWidth/2,this._currentMeasure.top=this._minY-this._lineWidth/2)},e.prototype.dispose=function(){this.reset(),t.prototype.dispose.call(this)},n([Object(s.serialize)()],e.prototype,"dash",null),e}(f);s._TypeStore.RegisteredTypes["BABYLON.GUI.MultiLine"]=E;var D=function(t){function e(e){var i=t.call(this,e)||this;return i.name=e,i._isChecked=!1,i._background="black",i._checkSizeRatio=.8,i._thickness=1,i.group="",i.onIsCheckedChangedObservable=new s.Observable,i.isPointerBlocker=!0,i}return o(e,t),Object.defineProperty(e.prototype,"thickness",{get:function(){return this._thickness},set:function(t){this._thickness!==t&&(this._thickness=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"checkSizeRatio",{get:function(){return this._checkSizeRatio},set:function(t){t=Math.max(Math.min(1,t),0),this._checkSizeRatio!==t&&(this._checkSizeRatio=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"background",{get:function(){return this._background},set:function(t){this._background!==t&&(this._background=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isChecked",{get:function(){return this._isChecked},set:function(t){var e=this;this._isChecked!==t&&(this._isChecked=t,this._markAsDirty(),this.onIsCheckedChangedObservable.notifyObservers(t),this._isChecked&&this._host&&this._host.executeOnAllControls((function(t){if(t!==e&&void 0!==t.group){var i=t;i.group===e.group&&(i.isChecked=!1)}})))},enumerable:!1,configurable:!0}),e.prototype._getTypeName=function(){return"RadioButton"},e.prototype._draw=function(t){t.save(),this._applyStates(t);var e=this._currentMeasure.width-this._thickness,i=this._currentMeasure.height-this._thickness;if((this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY)&&(t.shadowColor=this.shadowColor,t.shadowBlur=this.shadowBlur,t.shadowOffsetX=this.shadowOffsetX,t.shadowOffsetY=this.shadowOffsetY),f.drawEllipse(this._currentMeasure.left+this._currentMeasure.width/2,this._currentMeasure.top+this._currentMeasure.height/2,this._currentMeasure.width/2-this._thickness/2,this._currentMeasure.height/2-this._thickness/2,t),t.fillStyle=this._isEnabled?this._background:this._disabledColor,t.fill(),(this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY)&&(t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0),t.strokeStyle=this.color,t.lineWidth=this._thickness,t.stroke(),this._isChecked){t.fillStyle=this._isEnabled?this.color:this._disabledColor;var r=e*this._checkSizeRatio,o=i*this._checkSizeRatio;f.drawEllipse(this._currentMeasure.left+this._currentMeasure.width/2,this._currentMeasure.top+this._currentMeasure.height/2,r/2-this._thickness/2,o/2-this._thickness/2,t),t.fill()}t.restore()},e.prototype._onPointerDown=function(e,i,r,o,n){return!!t.prototype._onPointerDown.call(this,e,i,r,o,n)&&(this.isChecked||(this.isChecked=!0),!0)},e.AddRadioButtonWithHeader=function(t,i,r,o){var n=new C;n.isVertical=!1,n.height="30px";var s=new e;s.width="20px",s.height="20px",s.isChecked=r,s.color="green",s.group=i,s.onIsCheckedChangedObservable.add((function(t){return o(s,t)})),n.addControl(s);var a=new y;return a.text=t,a.width="180px",a.paddingLeft="5px",a.textHorizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,a.color="white",n.addControl(a),n},n([Object(s.serialize)()],e.prototype,"thickness",null),n([Object(s.serialize)()],e.prototype,"group",void 0),n([Object(s.serialize)()],e.prototype,"checkSizeRatio",null),n([Object(s.serialize)()],e.prototype,"background",null),n([Object(s.serialize)()],e.prototype,"isChecked",null),e}(f);s._TypeStore.RegisteredTypes["BABYLON.GUI.RadioButton"]=D;var L=function(t){function e(e){var i=t.call(this,e)||this;return i.name=e,i._thumbWidth=new a(20,a.UNITMODE_PIXEL,!1),i._minimum=0,i._maximum=100,i._value=50,i._isVertical=!1,i._barOffset=new a(5,a.UNITMODE_PIXEL,!1),i._isThumbClamped=!1,i._displayThumb=!0,i._step=0,i._lastPointerDownID=-1,i._effectiveBarOffset=0,i.onValueChangedObservable=new s.Observable,i._pointerIsDown=!1,i.isPointerBlocker=!0,i}return o(e,t),Object.defineProperty(e.prototype,"displayThumb",{get:function(){return this._displayThumb},set:function(t){this._displayThumb!==t&&(this._displayThumb=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"step",{get:function(){return this._step},set:function(t){this._step!==t&&(this._step=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"barOffset",{get:function(){return this._barOffset.toString(this._host)},set:function(t){this._barOffset.toString(this._host)!==t&&this._barOffset.fromString(t)&&this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"barOffsetInPixels",{get:function(){return this._barOffset.getValueInPixel(this._host,this._cachedParentMeasure.width)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"thumbWidth",{get:function(){return this._thumbWidth.toString(this._host)},set:function(t){this._thumbWidth.toString(this._host)!==t&&this._thumbWidth.fromString(t)&&this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"thumbWidthInPixels",{get:function(){return this._thumbWidth.getValueInPixel(this._host,this._cachedParentMeasure.width)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"minimum",{get:function(){return this._minimum},set:function(t){this._minimum!==t&&(this._minimum=t,this._markAsDirty(),this.value=Math.max(Math.min(this.value,this._maximum),this._minimum))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"maximum",{get:function(){return this._maximum},set:function(t){this._maximum!==t&&(this._maximum=t,this._markAsDirty(),this.value=Math.max(Math.min(this.value,this._maximum),this._minimum))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"value",{get:function(){return this._value},set:function(t){t=Math.max(Math.min(t,this._maximum),this._minimum),this._value!==t&&(this._value=t,this._markAsDirty(),this.onValueChangedObservable.notifyObservers(this._value))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isVertical",{get:function(){return this._isVertical},set:function(t){this._isVertical!==t&&(this._isVertical=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isThumbClamped",{get:function(){return this._isThumbClamped},set:function(t){this._isThumbClamped!==t&&(this._isThumbClamped=t,this._markAsDirty())},enumerable:!1,configurable:!0}),e.prototype._getTypeName=function(){return"BaseSlider"},e.prototype._getThumbPosition=function(){return this.isVertical?(this.maximum-this.value)/(this.maximum-this.minimum)*this._backgroundBoxLength:(this.value-this.minimum)/(this.maximum-this.minimum)*this._backgroundBoxLength},e.prototype._getThumbThickness=function(t){var e=0;switch(t){case"circle":e=this._thumbWidth.isPixel?Math.max(this._thumbWidth.getValue(this._host),this._backgroundBoxThickness):this._backgroundBoxThickness*this._thumbWidth.getValue(this._host);break;case"rectangle":e=this._thumbWidth.isPixel?Math.min(this._thumbWidth.getValue(this._host),this._backgroundBoxThickness):this._backgroundBoxThickness*this._thumbWidth.getValue(this._host)}return e},e.prototype._prepareRenderingData=function(t){this._effectiveBarOffset=0,this._renderLeft=this._currentMeasure.left,this._renderTop=this._currentMeasure.top,this._renderWidth=this._currentMeasure.width,this._renderHeight=this._currentMeasure.height,this._backgroundBoxLength=Math.max(this._currentMeasure.width,this._currentMeasure.height),this._backgroundBoxThickness=Math.min(this._currentMeasure.width,this._currentMeasure.height),this._effectiveThumbThickness=this._getThumbThickness(t),this.displayThumb&&(this._backgroundBoxLength-=this._effectiveThumbThickness),this.isVertical&&this._currentMeasure.height<this._currentMeasure.width?console.error("Height should be greater than width"):(this._barOffset.isPixel?this._effectiveBarOffset=Math.min(this._barOffset.getValue(this._host),this._backgroundBoxThickness):this._effectiveBarOffset=this._backgroundBoxThickness*this._barOffset.getValue(this._host),this._backgroundBoxThickness-=2*this._effectiveBarOffset,this.isVertical?(this._renderLeft+=this._effectiveBarOffset,!this.isThumbClamped&&this.displayThumb&&(this._renderTop+=this._effectiveThumbThickness/2),this._renderHeight=this._backgroundBoxLength,this._renderWidth=this._backgroundBoxThickness):(this._renderTop+=this._effectiveBarOffset,!this.isThumbClamped&&this.displayThumb&&(this._renderLeft+=this._effectiveThumbThickness/2),this._renderHeight=this._backgroundBoxThickness,this._renderWidth=this._backgroundBoxLength))},e.prototype._updateValueFromPointer=function(t,e){var i;0!=this.rotation&&(this._invertTransformMatrix.transformCoordinates(t,e,this._transformedPosition),t=this._transformedPosition.x,e=this._transformedPosition.y),i=this._isVertical?this._minimum+(1-(e-this._currentMeasure.top)/this._currentMeasure.height)*(this._maximum-this._minimum):this._minimum+(t-this._currentMeasure.left)/this._currentMeasure.width*(this._maximum-this._minimum);var r=1/this._step|0;this.value=this._step?(i*r|0)/r:i},e.prototype._onPointerDown=function(e,i,r,o,n){return!!t.prototype._onPointerDown.call(this,e,i,r,o,n)&&(this._pointerIsDown=!0,this._updateValueFromPointer(i.x,i.y),this._host._capturingControl[r]=this,this._lastPointerDownID=r,!0)},e.prototype._onPointerMove=function(e,i,r,o){r==this._lastPointerDownID&&(this._pointerIsDown&&this._updateValueFromPointer(i.x,i.y),t.prototype._onPointerMove.call(this,e,i,r,o))},e.prototype._onPointerUp=function(e,i,r,o,n){this._pointerIsDown=!1,delete this._host._capturingControl[r],t.prototype._onPointerUp.call(this,e,i,r,o,n)},e.prototype._onCanvasBlur=function(){this._forcePointerUp(),t.prototype._onCanvasBlur.call(this)},n([Object(s.serialize)()],e.prototype,"displayThumb",null),n([Object(s.serialize)()],e.prototype,"step",null),n([Object(s.serialize)()],e.prototype,"barOffset",null),n([Object(s.serialize)()],e.prototype,"thumbWidth",null),n([Object(s.serialize)()],e.prototype,"minimum",null),n([Object(s.serialize)()],e.prototype,"maximum",null),n([Object(s.serialize)()],e.prototype,"value",null),n([Object(s.serialize)()],e.prototype,"isVertical",null),n([Object(s.serialize)()],e.prototype,"isThumbClamped",null),e}(f),R=function(t){function e(e){var i=t.call(this,e)||this;return i.name=e,i._background="black",i._borderColor="white",i._thumbColor="",i._isThumbCircle=!1,i._displayValueBar=!0,i}return o(e,t),Object.defineProperty(e.prototype,"displayValueBar",{get:function(){return this._displayValueBar},set:function(t){this._displayValueBar!==t&&(this._displayValueBar=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"borderColor",{get:function(){return this._borderColor},set:function(t){this._borderColor!==t&&(this._borderColor=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"background",{get:function(){return this._background},set:function(t){this._background!==t&&(this._background=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"thumbColor",{get:function(){return this._thumbColor},set:function(t){this._thumbColor!==t&&(this._thumbColor=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isThumbCircle",{get:function(){return this._isThumbCircle},set:function(t){this._isThumbCircle!==t&&(this._isThumbCircle=t,this._markAsDirty())},enumerable:!1,configurable:!0}),e.prototype._getTypeName=function(){return"Slider"},e.prototype._draw=function(t,e){t.save(),this._applyStates(t),this._prepareRenderingData(this.isThumbCircle?"circle":"rectangle");var i=this._renderLeft,r=this._renderTop,o=this._renderWidth,n=this._renderHeight,s=0;this.isThumbClamped&&this.isThumbCircle?(this.isVertical?r+=this._effectiveThumbThickness/2:i+=this._effectiveThumbThickness/2,s=this._backgroundBoxThickness/2):s=(this._effectiveThumbThickness-this._effectiveBarOffset)/2,(this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY)&&(t.shadowColor=this.shadowColor,t.shadowBlur=this.shadowBlur,t.shadowOffsetX=this.shadowOffsetX,t.shadowOffsetY=this.shadowOffsetY);var a=this._getThumbPosition();t.fillStyle=this._background,this.isVertical?this.isThumbClamped?this.isThumbCircle?(t.beginPath(),t.arc(i+this._backgroundBoxThickness/2,r,s,Math.PI,2*Math.PI),t.fill(),t.fillRect(i,r,o,n)):t.fillRect(i,r,o,n+this._effectiveThumbThickness):t.fillRect(i,r,o,n):this.isThumbClamped?this.isThumbCircle?(t.beginPath(),t.arc(i+this._backgroundBoxLength,r+this._backgroundBoxThickness/2,s,0,2*Math.PI),t.fill(),t.fillRect(i,r,o,n)):t.fillRect(i,r,o+this._effectiveThumbThickness,n):t.fillRect(i,r,o,n),(this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY)&&(t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0),t.fillStyle=this.color,this._displayValueBar&&(this.isVertical?this.isThumbClamped?this.isThumbCircle?(t.beginPath(),t.arc(i+this._backgroundBoxThickness/2,r+this._backgroundBoxLength,s,0,2*Math.PI),t.fill(),t.fillRect(i,r+a,o,n-a)):t.fillRect(i,r+a,o,n-a+this._effectiveThumbThickness):t.fillRect(i,r+a,o,n-a):this.isThumbClamped&&this.isThumbCircle?(t.beginPath(),t.arc(i,r+this._backgroundBoxThickness/2,s,0,2*Math.PI),t.fill(),t.fillRect(i,r,a,n)):t.fillRect(i,r,a,n)),t.fillStyle=this._thumbColor||this.color,this.displayThumb&&((this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY)&&(t.shadowColor=this.shadowColor,t.shadowBlur=this.shadowBlur,t.shadowOffsetX=this.shadowOffsetX,t.shadowOffsetY=this.shadowOffsetY),this._isThumbCircle?(t.beginPath(),this.isVertical?t.arc(i+this._backgroundBoxThickness/2,r+a,s,0,2*Math.PI):t.arc(i+a,r+this._backgroundBoxThickness/2,s,0,2*Math.PI),t.fill(),(this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY)&&(t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0),t.strokeStyle=this._borderColor,t.stroke()):(this.isVertical?t.fillRect(i-this._effectiveBarOffset,this._currentMeasure.top+a,this._currentMeasure.width,this._effectiveThumbThickness):t.fillRect(this._currentMeasure.left+a,this._currentMeasure.top,this._effectiveThumbThickness,this._currentMeasure.height),(this.shadowBlur||this.shadowOffsetX||this.shadowOffsetY)&&(t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0),t.strokeStyle=this._borderColor,this.isVertical?t.strokeRect(i-this._effectiveBarOffset,this._currentMeasure.top+a,this._currentMeasure.width,this._effectiveThumbThickness):t.strokeRect(this._currentMeasure.left+a,this._currentMeasure.top,this._effectiveThumbThickness,this._currentMeasure.height))),t.restore()},n([Object(s.serialize)()],e.prototype,"displayValueBar",null),n([Object(s.serialize)()],e.prototype,"borderColor",null),n([Object(s.serialize)()],e.prototype,"background",null),n([Object(s.serialize)()],e.prototype,"thumbColor",null),n([Object(s.serialize)()],e.prototype,"isThumbCircle",null),e}(L);s._TypeStore.RegisteredTypes["BABYLON.GUI.Slider"]=R;var F=function(){function t(t){this.name=t,this._groupPanel=new C,this._selectors=new Array,this._groupPanel.verticalAlignment=f.VERTICAL_ALIGNMENT_TOP,this._groupPanel.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,this._groupHeader=this._addGroupHeader(t)}return Object.defineProperty(t.prototype,"groupPanel",{get:function(){return this._groupPanel},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"selectors",{get:function(){return this._selectors},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"header",{get:function(){return this._groupHeader.text},set:function(t){"label"!==this._groupHeader.text&&(this._groupHeader.text=t)},enumerable:!1,configurable:!0}),t.prototype._addGroupHeader=function(t){var e=new y("groupHead",t);return e.width=.9,e.height="30px",e.textWrapping=!0,e.color="black",e.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,e.textHorizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,e.left="2px",this._groupPanel.addControl(e),e},t.prototype._getSelector=function(t){if(!(t<0||t>=this._selectors.length))return this._selectors[t]},t.prototype.removeSelector=function(t){t<0||t>=this._selectors.length||(this._groupPanel.removeControl(this._selectors[t]),this._selectors.splice(t,1))},t}(),N=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.addCheckbox=function(t,e,i){void 0===e&&(e=function(t){}),void 0===i&&(i=!1);i=i||!1;var r=new x;r.width="20px",r.height="20px",r.color="#364249",r.background="#CCCCCC",r.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,r.onIsCheckedChangedObservable.add((function(t){e(t)}));var o=f.AddHeader(r,t,"200px",{isHorizontal:!0,controlFirst:!0});o.height="30px",o.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,o.left="4px",this.groupPanel.addControl(o),this.selectors.push(o),r.isChecked=i,this.groupPanel.parent&&this.groupPanel.parent.parent&&(r.color=this.groupPanel.parent.parent.buttonColor,r.background=this.groupPanel.parent.parent.buttonBackground)},e.prototype._setSelectorLabel=function(t,e){this.selectors[t].children[1].text=e},e.prototype._setSelectorLabelColor=function(t,e){this.selectors[t].children[1].color=e},e.prototype._setSelectorButtonColor=function(t,e){this.selectors[t].children[0].color=e},e.prototype._setSelectorButtonBackground=function(t,e){this.selectors[t].children[0].background=e},e}(F),z=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e._selectNb=0,e}return o(e,t),e.prototype.addRadio=function(t,e,i){void 0===e&&(e=function(t){}),void 0===i&&(i=!1);var r=this._selectNb++,o=new D;o.name=t,o.width="20px",o.height="20px",o.color="#364249",o.background="#CCCCCC",o.group=this.name,o.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,o.onIsCheckedChangedObservable.add((function(t){t&&e(r)}));var n=f.AddHeader(o,t,"200px",{isHorizontal:!0,controlFirst:!0});n.height="30px",n.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,n.left="4px",this.groupPanel.addControl(n),this.selectors.push(n),o.isChecked=i,this.groupPanel.parent&&this.groupPanel.parent.parent&&(o.color=this.groupPanel.parent.parent.buttonColor,o.background=this.groupPanel.parent.parent.buttonBackground)},e.prototype._setSelectorLabel=function(t,e){this.selectors[t].children[1].text=e},e.prototype._setSelectorLabelColor=function(t,e){this.selectors[t].children[1].color=e},e.prototype._setSelectorButtonColor=function(t,e){this.selectors[t].children[0].color=e},e.prototype._setSelectorButtonBackground=function(t,e){this.selectors[t].children[0].background=e},e}(F),V=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.addSlider=function(t,e,i,r,o,n,s){void 0===e&&(e=function(t){}),void 0===i&&(i="Units"),void 0===r&&(r=0),void 0===o&&(o=0),void 0===n&&(n=0),void 0===s&&(s=function(t){return 0|t});var a=new R;a.name=i,a.value=n,a.minimum=r,a.maximum=o,a.width=.9,a.height="20px",a.color="#364249",a.background="#CCCCCC",a.borderColor="black",a.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,a.left="4px",a.paddingBottom="4px",a.onValueChangedObservable.add((function(t){a.parent.children[0].text=a.parent.children[0].name+": "+s(t)+" "+a.name,e(t)}));var h=f.AddHeader(a,t+": "+s(n)+" "+i,"30px",{isHorizontal:!1,controlFirst:!1});h.height="60px",h.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,h.left="4px",h.children[0].name=t,this.groupPanel.addControl(h),this.selectors.push(h),this.groupPanel.parent&&this.groupPanel.parent.parent&&(a.color=this.groupPanel.parent.parent.buttonColor,a.background=this.groupPanel.parent.parent.buttonBackground)},e.prototype._setSelectorLabel=function(t,e){this.selectors[t].children[0].name=e,this.selectors[t].children[0].text=e+": "+this.selectors[t].children[1].value+" "+this.selectors[t].children[1].name},e.prototype._setSelectorLabelColor=function(t,e){this.selectors[t].children[0].color=e},e.prototype._setSelectorButtonColor=function(t,e){this.selectors[t].children[1].color=e},e.prototype._setSelectorButtonBackground=function(t,e){this.selectors[t].children[1].background=e},e}(F),j=function(t){function e(e,i){void 0===i&&(i=[]);var r=t.call(this,e)||this;if(r.name=e,r.groups=i,r._buttonColor="#364249",r._buttonBackground="#CCCCCC",r._headerColor="black",r._barColor="white",r._barHeight="2px",r._spacerHeight="20px",r._bars=new Array,r._groups=i,r.thickness=2,r._panel=new C,r._panel.verticalAlignment=f.VERTICAL_ALIGNMENT_TOP,r._panel.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,r._panel.top=5,r._panel.left=5,r._panel.width=.95,i.length>0){for(var o=0;o<i.length-1;o++)r._panel.addControl(i[o].groupPanel),r._addSpacer();r._panel.addControl(i[i.length-1].groupPanel)}return r.addControl(r._panel),r}return o(e,t),e.prototype._getTypeName=function(){return"SelectionPanel"},Object.defineProperty(e.prototype,"panel",{get:function(){return this._panel},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"headerColor",{get:function(){return this._headerColor},set:function(t){this._headerColor!==t&&(this._headerColor=t,this._setHeaderColor())},enumerable:!1,configurable:!0}),e.prototype._setHeaderColor=function(){for(var t=0;t<this._groups.length;t++)this._groups[t].groupPanel.children[0].color=this._headerColor},Object.defineProperty(e.prototype,"buttonColor",{get:function(){return this._buttonColor},set:function(t){this._buttonColor!==t&&(this._buttonColor=t,this._setbuttonColor())},enumerable:!1,configurable:!0}),e.prototype._setbuttonColor=function(){for(var t=0;t<this._groups.length;t++)for(var e=0;e<this._groups[t].selectors.length;e++)this._groups[t]._setSelectorButtonColor(e,this._buttonColor)},Object.defineProperty(e.prototype,"labelColor",{get:function(){return this._labelColor},set:function(t){this._labelColor!==t&&(this._labelColor=t,this._setLabelColor())},enumerable:!1,configurable:!0}),e.prototype._setLabelColor=function(){for(var t=0;t<this._groups.length;t++)for(var e=0;e<this._groups[t].selectors.length;e++)this._groups[t]._setSelectorLabelColor(e,this._labelColor)},Object.defineProperty(e.prototype,"buttonBackground",{get:function(){return this._buttonBackground},set:function(t){this._buttonBackground!==t&&(this._buttonBackground=t,this._setButtonBackground())},enumerable:!1,configurable:!0}),e.prototype._setButtonBackground=function(){for(var t=0;t<this._groups.length;t++)for(var e=0;e<this._groups[t].selectors.length;e++)this._groups[t]._setSelectorButtonBackground(e,this._buttonBackground)},Object.defineProperty(e.prototype,"barColor",{get:function(){return this._barColor},set:function(t){this._barColor!==t&&(this._barColor=t,this._setBarColor())},enumerable:!1,configurable:!0}),e.prototype._setBarColor=function(){for(var t=0;t<this._bars.length;t++)this._bars[t].children[0].background=this._barColor},Object.defineProperty(e.prototype,"barHeight",{get:function(){return this._barHeight},set:function(t){this._barHeight!==t&&(this._barHeight=t,this._setBarHeight())},enumerable:!1,configurable:!0}),e.prototype._setBarHeight=function(){for(var t=0;t<this._bars.length;t++)this._bars[t].children[0].height=this._barHeight},Object.defineProperty(e.prototype,"spacerHeight",{get:function(){return this._spacerHeight},set:function(t){this._spacerHeight!==t&&(this._spacerHeight=t,this._setSpacerHeight())},enumerable:!1,configurable:!0}),e.prototype._setSpacerHeight=function(){for(var t=0;t<this._bars.length;t++)this._bars[t].height=this._spacerHeight},e.prototype._addSpacer=function(){var t=new g;t.width=1,t.height=this._spacerHeight,t.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT;var e=new m;e.width=1,e.height=this._barHeight,e.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,e.verticalAlignment=f.VERTICAL_ALIGNMENT_CENTER,e.background=this._barColor,e.color="transparent",t.addControl(e),this._panel.addControl(t),this._bars.push(t)},e.prototype.addGroup=function(t){this._groups.length>0&&this._addSpacer(),this._panel.addControl(t.groupPanel),this._groups.push(t),t.groupPanel.children[0].color=this._headerColor;for(var e=0;e<t.selectors.length;e++)t._setSelectorButtonColor(e,this._buttonColor),t._setSelectorButtonBackground(e,this._buttonBackground)},e.prototype.removeGroup=function(t){if(!(t<0||t>=this._groups.length)){var e=this._groups[t];this._panel.removeControl(e.groupPanel),this._groups.splice(t,1),t<this._bars.length&&(this._panel.removeControl(this._bars[t]),this._bars.splice(t,1))}},e.prototype.setHeaderName=function(t,e){e<0||e>=this._groups.length||(this._groups[e].groupPanel.children[0].text=t)},e.prototype.relabel=function(t,e,i){if(!(e<0||e>=this._groups.length)){var r=this._groups[e];i<0||i>=r.selectors.length||r._setSelectorLabel(i,t)}},e.prototype.removeFromGroupSelector=function(t,e){if(!(t<0||t>=this._groups.length)){var i=this._groups[t];e<0||e>=i.selectors.length||i.removeSelector(e)}},e.prototype.addToGroupCheckbox=function(t,e,i,r){(void 0===i&&(i=function(){}),void 0===r&&(r=!1),t<0||t>=this._groups.length)||this._groups[t].addCheckbox(e,i,r)},e.prototype.addToGroupRadio=function(t,e,i,r){(void 0===i&&(i=function(){}),void 0===r&&(r=!1),t<0||t>=this._groups.length)||this._groups[t].addRadio(e,i,r)},e.prototype.addToGroupSlider=function(t,e,i,r,o,n,s,a){(void 0===i&&(i=function(){}),void 0===r&&(r="Units"),void 0===o&&(o=0),void 0===n&&(n=0),void 0===s&&(s=0),void 0===a&&(a=function(t){return 0|t}),t<0||t>=this._groups.length)||this._groups[t].addSlider(e,i,r,o,n,s,a)},e}(m),H=function(t){function e(e){var i=t.call(this,e)||this;return i._freezeControls=!1,i._bucketWidth=0,i._bucketHeight=0,i._buckets={},i}return o(e,t),Object.defineProperty(e.prototype,"freezeControls",{get:function(){return this._freezeControls},set:function(t){if(this._freezeControls!==t){t||this._restoreMeasures(),this._freezeControls=!1;var e=this.host.getSize(),i=e.width,r=e.height,o=this.host.getContext(),n=new _(0,0,i,r);this.host._numLayoutCalls=0,this.host._rootContainer._layout(n,o),t&&(this._updateMeasures(),this._useBuckets()&&this._makeBuckets()),this._freezeControls=t,this.host.markAsDirty()}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"bucketWidth",{get:function(){return this._bucketWidth},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"bucketHeight",{get:function(){return this._bucketHeight},enumerable:!1,configurable:!0}),e.prototype.setBucketSizes=function(t,e){this._bucketWidth=t,this._bucketHeight=e,this._useBuckets()?this._freezeControls&&this._makeBuckets():this._buckets={}},e.prototype._useBuckets=function(){return this._bucketWidth>0&&this._bucketHeight>0},e.prototype._makeBuckets=function(){this._buckets={},this._bucketLen=Math.ceil(this.widthInPixels/this._bucketWidth),this._dispatchInBuckets(this._children),this._oldLeft=null,this._oldTop=null},e.prototype._dispatchInBuckets=function(t){for(var e=0;e<t.length;++e){for(var i=t[e],r=Math.max(0,Math.floor((i._customData._origLeft-this._customData.origLeft)/this._bucketWidth)),o=Math.floor((i._customData._origLeft-this._customData.origLeft+i._currentMeasure.width-1)/this._bucketWidth),n=Math.max(0,Math.floor((i._customData._origTop-this._customData.origTop)/this._bucketHeight)),s=Math.floor((i._customData._origTop-this._customData.origTop+i._currentMeasure.height-1)/this._bucketHeight);n<=s;){for(var a=r;a<=o;++a){var h=n*this._bucketLen+a,l=this._buckets[h];l||(l=[],this._buckets[h]=l),l.push(i)}n++}i instanceof g&&i._children.length>0&&this._dispatchInBuckets(i._children)}},e.prototype._updateMeasures=function(){var t=0|this.leftInPixels,e=0|this.topInPixels;this._measureForChildren.left-=t,this._measureForChildren.top-=e,this._currentMeasure.left-=t,this._currentMeasure.top-=e,this._customData.origLeftForChildren=this._measureForChildren.left,this._customData.origTopForChildren=this._measureForChildren.top,this._customData.origLeft=this._currentMeasure.left,this._customData.origTop=this._currentMeasure.top,this._updateChildrenMeasures(this._children,t,e)},e.prototype._updateChildrenMeasures=function(t,e,i){for(var r=0;r<t.length;++r){var o=t[r];o._currentMeasure.left-=e,o._currentMeasure.top-=i,o._customData._origLeft=o._currentMeasure.left,o._customData._origTop=o._currentMeasure.top,o instanceof g&&o._children.length>0&&this._updateChildrenMeasures(o._children,e,i)}},e.prototype._restoreMeasures=function(){var t=0|this.leftInPixels,e=0|this.topInPixels;this._measureForChildren.left=this._customData.origLeftForChildren+t,this._measureForChildren.top=this._customData.origTopForChildren+e,this._currentMeasure.left=this._customData.origLeft+t,this._currentMeasure.top=this._customData.origTop+e},e.prototype._getTypeName=function(){return"ScrollViewerWindow"},e.prototype._additionalProcessing=function(e,i){t.prototype._additionalProcessing.call(this,e,i),this._parentMeasure=e,this._measureForChildren.left=this._currentMeasure.left,this._measureForChildren.top=this._currentMeasure.top,this._measureForChildren.width=e.width,this._measureForChildren.height=e.height},e.prototype._layout=function(e,i){return this._freezeControls?(this.invalidateRect(),!1):t.prototype._layout.call(this,e,i)},e.prototype._scrollChildren=function(t,e,i){for(var r=0;r<t.length;++r){var o=t[r];o._currentMeasure.left=o._customData._origLeft+e,o._currentMeasure.top=o._customData._origTop+i,o._isClipped=!1,o instanceof g&&o._children.length>0&&this._scrollChildren(o._children,e,i)}},e.prototype._scrollChildrenWithBuckets=function(t,e,i,r){for(var o=Math.max(0,Math.floor(-t/this._bucketWidth)),n=Math.floor((-t+this._parentMeasure.width-1)/this._bucketWidth),s=Math.max(0,Math.floor(-e/this._bucketHeight)),a=Math.floor((-e+this._parentMeasure.height-1)/this._bucketHeight);s<=a;){for(var h=o;h<=n;++h){var l=s*this._bucketLen+h,c=this._buckets[l];if(c)for(var u=0;u<c.length;++u){var _=c[u];_._currentMeasure.left=_._customData._origLeft+i,_._currentMeasure.top=_._customData._origTop+r,_._isClipped=!1}}s++}},e.prototype._draw=function(e,i){if(this._freezeControls){this._localDraw(e),this.clipChildren&&this._clipForChildren(e);var r=0|this.leftInPixels,o=0|this.topInPixels;this._useBuckets()&&null!==this._oldLeft&&null!==this._oldTop?(this._scrollChildrenWithBuckets(this._oldLeft,this._oldTop,r,o),this._scrollChildrenWithBuckets(r,o,r,o)):this._scrollChildren(this._children,r,o),this._oldLeft=r,this._oldTop=o;for(var n=0,s=this._children;n<s.length;n++){var a=s[n];a._intersectsRect(this._parentMeasure)&&a._render(e,this._parentMeasure)}}else t.prototype._draw.call(this,e,i)},e.prototype._postMeasure=function(){if(this._freezeControls)t.prototype._postMeasure.call(this);else{for(var e=this.parentClientWidth,i=this.parentClientHeight,r=0,o=this.children;r<o.length;r++){var n=o[r];n.isVisible&&!n.notRenderable&&(n.horizontalAlignment===f.HORIZONTAL_ALIGNMENT_CENTER&&n._offsetLeft(this._currentMeasure.left-n._currentMeasure.left),n.verticalAlignment===f.VERTICAL_ALIGNMENT_CENTER&&n._offsetTop(this._currentMeasure.top-n._currentMeasure.top),e=Math.max(e,n._currentMeasure.left-this._currentMeasure.left+n._currentMeasure.width+n.paddingRightInPixels),i=Math.max(i,n._currentMeasure.top-this._currentMeasure.top+n._currentMeasure.height+n.paddingBottomInPixels))}this._currentMeasure.width!==e&&(this._width.updateInPlace(e,a.UNITMODE_PIXEL),this._currentMeasure.width=e,this._rebuildLayout=!0,this._isDirty=!0),this._currentMeasure.height!==i&&(this._height.updateInPlace(i,a.UNITMODE_PIXEL),this._currentMeasure.height=i,this._rebuildLayout=!0,this._isDirty=!0),t.prototype._postMeasure.call(this)}},e}(g),W=function(t){function e(e){var i=t.call(this,e)||this;return i.name=e,i._background="black",i._borderColor="white",i._tempMeasure=new _(0,0,0,0),i}return o(e,t),Object.defineProperty(e.prototype,"borderColor",{get:function(){return this._borderColor},set:function(t){this._borderColor!==t&&(this._borderColor=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"background",{get:function(){return this._background},set:function(t){this._background!==t&&(this._background=t,this._markAsDirty())},enumerable:!1,configurable:!0}),e.prototype._getTypeName=function(){return"Scrollbar"},e.prototype._getThumbThickness=function(){return this._thumbWidth.isPixel?this._thumbWidth.getValue(this._host):this._backgroundBoxThickness*this._thumbWidth.getValue(this._host)},e.prototype._draw=function(t){t.save(),this._applyStates(t),this._prepareRenderingData("rectangle");var e=this._renderLeft,i=this._getThumbPosition();t.fillStyle=this._background,t.fillRect(this._currentMeasure.left,this._currentMeasure.top,this._currentMeasure.width,this._currentMeasure.height),t.fillStyle=this.color,this.isVertical?(this._tempMeasure.left=e-this._effectiveBarOffset,this._tempMeasure.top=this._currentMeasure.top+i,this._tempMeasure.width=this._currentMeasure.width,this._tempMeasure.height=this._effectiveThumbThickness):(this._tempMeasure.left=this._currentMeasure.left+i,this._tempMeasure.top=this._currentMeasure.top,this._tempMeasure.width=this._effectiveThumbThickness,this._tempMeasure.height=this._currentMeasure.height),t.fillRect(this._tempMeasure.left,this._tempMeasure.top,this._tempMeasure.width,this._tempMeasure.height),t.restore()},e.prototype._updateValueFromPointer=function(t,e){0!=this.rotation&&(this._invertTransformMatrix.transformCoordinates(t,e,this._transformedPosition),t=this._transformedPosition.x,e=this._transformedPosition.y),this._first&&(this._first=!1,this._originX=t,this._originY=e,(t<this._tempMeasure.left||t>this._tempMeasure.left+this._tempMeasure.width||e<this._tempMeasure.top||e>this._tempMeasure.top+this._tempMeasure.height)&&(this.isVertical?this.value=this.minimum+(1-(e-this._currentMeasure.top)/this._currentMeasure.height)*(this.maximum-this.minimum):this.value=this.minimum+(t-this._currentMeasure.left)/this._currentMeasure.width*(this.maximum-this.minimum)));var i=0;i=this.isVertical?-(e-this._originY)/(this._currentMeasure.height-this._effectiveThumbThickness):(t-this._originX)/(this._currentMeasure.width-this._effectiveThumbThickness),this.value+=i*(this.maximum-this.minimum),this._originX=t,this._originY=e},e.prototype._onPointerDown=function(e,i,r,o,n){return this._first=!0,t.prototype._onPointerDown.call(this,e,i,r,o,n)},n([Object(s.serialize)()],e.prototype,"borderColor",null),n([Object(s.serialize)()],e.prototype,"background",null),e}(L),G=function(t){function e(e){var i=t.call(this,e)||this;return i.name=e,i._thumbLength=.5,i._thumbHeight=1,i._barImageHeight=1,i._tempMeasure=new _(0,0,0,0),i.num90RotationInVerticalMode=1,i}return o(e,t),Object.defineProperty(e.prototype,"backgroundImage",{get:function(){return this._backgroundBaseImage},set:function(t){var e=this;this._backgroundBaseImage!==t&&(this._backgroundBaseImage=t,this.isVertical&&0!==this.num90RotationInVerticalMode?t.isLoaded?(this._backgroundImage=t._rotate90(this.num90RotationInVerticalMode,!0),this._markAsDirty()):t.onImageLoadedObservable.addOnce((function(){var i=t._rotate90(e.num90RotationInVerticalMode,!0);e._backgroundImage=i,i.isLoaded||i.onImageLoadedObservable.addOnce((function(){e._markAsDirty()})),e._markAsDirty()})):(this._backgroundImage=t,t&&!t.isLoaded&&t.onImageLoadedObservable.addOnce((function(){e._markAsDirty()})),this._markAsDirty()))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"thumbImage",{get:function(){return this._thumbBaseImage},set:function(t){var e=this;this._thumbBaseImage!==t&&(this._thumbBaseImage=t,this.isVertical&&0!==this.num90RotationInVerticalMode?t.isLoaded?(this._thumbImage=t._rotate90(-this.num90RotationInVerticalMode,!0),this._markAsDirty()):t.onImageLoadedObservable.addOnce((function(){var i=t._rotate90(-e.num90RotationInVerticalMode,!0);e._thumbImage=i,i.isLoaded||i.onImageLoadedObservable.addOnce((function(){e._markAsDirty()})),e._markAsDirty()})):(this._thumbImage=t,t&&!t.isLoaded&&t.onImageLoadedObservable.addOnce((function(){e._markAsDirty()})),this._markAsDirty()))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"thumbLength",{get:function(){return this._thumbLength},set:function(t){this._thumbLength!==t&&(this._thumbLength=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"thumbHeight",{get:function(){return this._thumbHeight},set:function(t){this._thumbLength!==t&&(this._thumbHeight=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"barImageHeight",{get:function(){return this._barImageHeight},set:function(t){this._barImageHeight!==t&&(this._barImageHeight=t,this._markAsDirty())},enumerable:!1,configurable:!0}),e.prototype._getTypeName=function(){return"ImageScrollBar"},e.prototype._getThumbThickness=function(){return this._thumbWidth.isPixel?this._thumbWidth.getValue(this._host):this._backgroundBoxThickness*this._thumbWidth.getValue(this._host)},e.prototype._draw=function(t){t.save(),this._applyStates(t),this._prepareRenderingData("rectangle");var e=this._getThumbPosition(),i=this._renderLeft,r=this._renderTop,o=this._renderWidth,n=this._renderHeight;this._backgroundImage&&(this._tempMeasure.copyFromFloats(i,r,o,n),this.isVertical?(this._tempMeasure.copyFromFloats(i+o*(1-this._barImageHeight)*.5,this._currentMeasure.top,o*this._barImageHeight,n),this._tempMeasure.height+=this._effectiveThumbThickness,this._backgroundImage._currentMeasure.copyFrom(this._tempMeasure)):(this._tempMeasure.copyFromFloats(this._currentMeasure.left,r+n*(1-this._barImageHeight)*.5,o,n*this._barImageHeight),this._tempMeasure.width+=this._effectiveThumbThickness,this._backgroundImage._currentMeasure.copyFrom(this._tempMeasure)),this._backgroundImage._draw(t)),this.isVertical?this._tempMeasure.copyFromFloats(i-this._effectiveBarOffset+this._currentMeasure.width*(1-this._thumbHeight)*.5,this._currentMeasure.top+e,this._currentMeasure.width*this._thumbHeight,this._effectiveThumbThickness):this._tempMeasure.copyFromFloats(this._currentMeasure.left+e,this._currentMeasure.top+this._currentMeasure.height*(1-this._thumbHeight)*.5,this._effectiveThumbThickness,this._currentMeasure.height*this._thumbHeight),this._thumbImage&&(this._thumbImage._currentMeasure.copyFrom(this._tempMeasure),this._thumbImage._draw(t)),t.restore()},e.prototype._updateValueFromPointer=function(t,e){0!=this.rotation&&(this._invertTransformMatrix.transformCoordinates(t,e,this._transformedPosition),t=this._transformedPosition.x,e=this._transformedPosition.y),this._first&&(this._first=!1,this._originX=t,this._originY=e,(t<this._tempMeasure.left||t>this._tempMeasure.left+this._tempMeasure.width||e<this._tempMeasure.top||e>this._tempMeasure.top+this._tempMeasure.height)&&(this.isVertical?this.value=this.minimum+(1-(e-this._currentMeasure.top)/this._currentMeasure.height)*(this.maximum-this.minimum):this.value=this.minimum+(t-this._currentMeasure.left)/this._currentMeasure.width*(this.maximum-this.minimum)));var i=0;i=this.isVertical?-(e-this._originY)/(this._currentMeasure.height-this._effectiveThumbThickness):(t-this._originX)/(this._currentMeasure.width-this._effectiveThumbThickness),this.value+=i*(this.maximum-this.minimum),this._originX=t,this._originY=e},e.prototype._onPointerDown=function(e,i,r,o,n){return this._first=!0,t.prototype._onPointerDown.call(this,e,i,r,o,n)},n([Object(s.serialize)()],e.prototype,"num90RotationInVerticalMode",void 0),e}(L),U=function(t){function e(e,i){var r=t.call(this,e)||this;return r._barSize=20,r._pointerIsOver=!1,r._wheelPrecision=.05,r._thumbLength=.5,r._thumbHeight=1,r._barImageHeight=1,r._horizontalBarImageHeight=1,r._verticalBarImageHeight=1,r._oldWindowContentsWidth=0,r._oldWindowContentsHeight=0,r._forceHorizontalBar=!1,r._forceVerticalBar=!1,r._useImageBar=i||!1,r.onDirtyObservable.add((function(){r._horizontalBarSpace.color=r.color,r._verticalBarSpace.color=r.color,r._dragSpace.color=r.color})),r.onPointerEnterObservable.add((function(){r._pointerIsOver=!0})),r.onPointerOutObservable.add((function(){r._pointerIsOver=!1})),r._grid=new w,r._useImageBar?(r._horizontalBar=new G,r._verticalBar=new G):(r._horizontalBar=new W,r._verticalBar=new W),r._window=new H("scrollViewer_window"),r._window.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,r._window.verticalAlignment=f.VERTICAL_ALIGNMENT_TOP,r._grid.addColumnDefinition(1),r._grid.addColumnDefinition(0,!0),r._grid.addRowDefinition(1),r._grid.addRowDefinition(0,!0),t.prototype.addControl.call(r,r._grid),r._grid.addControl(r._window,0,0),r._verticalBarSpace=new m,r._verticalBarSpace.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,r._verticalBarSpace.verticalAlignment=f.VERTICAL_ALIGNMENT_TOP,r._verticalBarSpace.thickness=1,r._grid.addControl(r._verticalBarSpace,0,1),r._addBar(r._verticalBar,r._verticalBarSpace,!0,Math.PI),r._horizontalBarSpace=new m,r._horizontalBarSpace.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,r._horizontalBarSpace.verticalAlignment=f.VERTICAL_ALIGNMENT_TOP,r._horizontalBarSpace.thickness=1,r._grid.addControl(r._horizontalBarSpace,1,0),r._addBar(r._horizontalBar,r._horizontalBarSpace,!1,0),r._dragSpace=new m,r._dragSpace.thickness=1,r._grid.addControl(r._dragSpace,1,1),r._useImageBar||(r.barColor="grey",r.barBackground="transparent"),r}return o(e,t),Object.defineProperty(e.prototype,"horizontalBar",{get:function(){return this._horizontalBar},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"verticalBar",{get:function(){return this._verticalBar},enumerable:!1,configurable:!0}),e.prototype.addControl=function(t){return t?(this._window.addControl(t),this):this},e.prototype.removeControl=function(t){return this._window.removeControl(t),this},Object.defineProperty(e.prototype,"children",{get:function(){return this._window.children},enumerable:!1,configurable:!0}),e.prototype._flagDescendantsAsMatrixDirty=function(){for(var t=0,e=this._children;t<e.length;t++){e[t]._markMatrixAsDirty()}},Object.defineProperty(e.prototype,"freezeControls",{get:function(){return this._window.freezeControls},set:function(t){this._window.freezeControls=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"bucketWidth",{get:function(){return this._window.bucketWidth},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"bucketHeight",{get:function(){return this._window.bucketHeight},enumerable:!1,configurable:!0}),e.prototype.setBucketSizes=function(t,e){this._window.setBucketSizes(t,e)},Object.defineProperty(e.prototype,"forceHorizontalBar",{get:function(){return this._forceHorizontalBar},set:function(t){this._grid.setRowDefinition(1,t?this._barSize:0,!0),this._horizontalBar.isVisible=t,this._forceHorizontalBar=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"forceVerticalBar",{get:function(){return this._forceVerticalBar},set:function(t){this._grid.setColumnDefinition(1,t?this._barSize:0,!0),this._verticalBar.isVisible=t,this._forceVerticalBar=t},enumerable:!1,configurable:!0}),e.prototype.resetWindow=function(){this._window.width="100%",this._window.height="100%"},e.prototype._getTypeName=function(){return"ScrollViewer"},e.prototype._buildClientSizes=function(){var t=this.host.idealRatio;this._window.parentClientWidth=this._currentMeasure.width-(this._verticalBar.isVisible||this.forceVerticalBar?this._barSize*t:0)-2*this.thickness,this._window.parentClientHeight=this._currentMeasure.height-(this._horizontalBar.isVisible||this.forceHorizontalBar?this._barSize*t:0)-2*this.thickness,this._clientWidth=this._window.parentClientWidth,this._clientHeight=this._window.parentClientHeight},e.prototype._additionalProcessing=function(e,i){t.prototype._additionalProcessing.call(this,e,i),this._buildClientSizes()},e.prototype._postMeasure=function(){t.prototype._postMeasure.call(this),this._updateScroller(),this._setWindowPosition(!1)},Object.defineProperty(e.prototype,"wheelPrecision",{get:function(){return this._wheelPrecision},set:function(t){this._wheelPrecision!==t&&(t<0&&(t=0),t>1&&(t=1),this._wheelPrecision=t)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"scrollBackground",{get:function(){return this._horizontalBarSpace.background},set:function(t){this._horizontalBarSpace.background!==t&&(this._horizontalBarSpace.background=t,this._verticalBarSpace.background=t)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"barColor",{get:function(){return this._barColor},set:function(t){this._barColor!==t&&(this._barColor=t,this._horizontalBar.color=t,this._verticalBar.color=t)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"thumbImage",{get:function(){return this._barImage},set:function(t){if(this._barImage!==t){this._barImage=t;var e=this._horizontalBar,i=this._verticalBar;e.thumbImage=t,i.thumbImage=t}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"horizontalThumbImage",{get:function(){return this._horizontalBarImage},set:function(t){this._horizontalBarImage!==t&&(this._horizontalBarImage=t,this._horizontalBar.thumbImage=t)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"verticalThumbImage",{get:function(){return this._verticalBarImage},set:function(t){this._verticalBarImage!==t&&(this._verticalBarImage=t,this._verticalBar.thumbImage=t)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"barSize",{get:function(){return this._barSize},set:function(t){this._barSize!==t&&(this._barSize=t,this._markAsDirty(),this._horizontalBar.isVisible&&this._grid.setRowDefinition(1,this._barSize,!0),this._verticalBar.isVisible&&this._grid.setColumnDefinition(1,this._barSize,!0))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"thumbLength",{get:function(){return this._thumbLength},set:function(t){if(this._thumbLength!==t){t<=0&&(t=.1),t>1&&(t=1),this._thumbLength=t;var e=this._horizontalBar,i=this._verticalBar;e.thumbLength=t,i.thumbLength=t,this._markAsDirty()}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"thumbHeight",{get:function(){return this._thumbHeight},set:function(t){if(this._thumbHeight!==t){t<=0&&(t=.1),t>1&&(t=1),this._thumbHeight=t;var e=this._horizontalBar,i=this._verticalBar;e.thumbHeight=t,i.thumbHeight=t,this._markAsDirty()}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"barImageHeight",{get:function(){return this._barImageHeight},set:function(t){if(this._barImageHeight!==t){t<=0&&(t=.1),t>1&&(t=1),this._barImageHeight=t;var e=this._horizontalBar,i=this._verticalBar;e.barImageHeight=t,i.barImageHeight=t,this._markAsDirty()}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"horizontalBarImageHeight",{get:function(){return this._horizontalBarImageHeight},set:function(t){this._horizontalBarImageHeight!==t&&(t<=0&&(t=.1),t>1&&(t=1),this._horizontalBarImageHeight=t,this._horizontalBar.barImageHeight=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"verticalBarImageHeight",{get:function(){return this._verticalBarImageHeight},set:function(t){this._verticalBarImageHeight!==t&&(t<=0&&(t=.1),t>1&&(t=1),this._verticalBarImageHeight=t,this._verticalBar.barImageHeight=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"barBackground",{get:function(){return this._barBackground},set:function(t){if(this._barBackground!==t){this._barBackground=t;var e=this._horizontalBar,i=this._verticalBar;e.background=t,i.background=t,this._dragSpace.background=t}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"barImage",{get:function(){return this._barBackgroundImage},set:function(t){this._barBackgroundImage,this._barBackgroundImage=t;var e=this._horizontalBar,i=this._verticalBar;e.backgroundImage=t,i.backgroundImage=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"horizontalBarImage",{get:function(){return this._horizontalBarBackgroundImage},set:function(t){this._horizontalBarBackgroundImage,this._horizontalBarBackgroundImage=t,this._horizontalBar.backgroundImage=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"verticalBarImage",{get:function(){return this._verticalBarBackgroundImage},set:function(t){this._verticalBarBackgroundImage,this._verticalBarBackgroundImage=t,this._verticalBar.backgroundImage=t},enumerable:!1,configurable:!0}),e.prototype._setWindowPosition=function(t){void 0===t&&(t=!0);var e=this.host.idealRatio,i=this._window._currentMeasure.width,r=this._window._currentMeasure.height;if(t||this._oldWindowContentsWidth!==i||this._oldWindowContentsHeight!==r){this._oldWindowContentsWidth=i,this._oldWindowContentsHeight=r;var o=this._clientWidth-i,n=this._clientHeight-r,s=this._horizontalBar.value/e*o+"px",a=this._verticalBar.value/e*n+"px";s!==this._window.left&&(this._window.left=s,this.freezeControls||(this._rebuildLayout=!0)),a!==this._window.top&&(this._window.top=a,this.freezeControls||(this._rebuildLayout=!0))}},e.prototype._updateScroller=function(){var t=this._window._currentMeasure.width,e=this._window._currentMeasure.height;this._horizontalBar.isVisible&&t<=this._clientWidth&&!this.forceHorizontalBar?(this._grid.setRowDefinition(1,0,!0),this._horizontalBar.isVisible=!1,this._horizontalBar.value=0,this._rebuildLayout=!0):!this._horizontalBar.isVisible&&(t>this._clientWidth||this.forceHorizontalBar)&&(this._grid.setRowDefinition(1,this._barSize,!0),this._horizontalBar.isVisible=!0,this._rebuildLayout=!0),this._verticalBar.isVisible&&e<=this._clientHeight&&!this.forceVerticalBar?(this._grid.setColumnDefinition(1,0,!0),this._verticalBar.isVisible=!1,this._verticalBar.value=0,this._rebuildLayout=!0):!this._verticalBar.isVisible&&(e>this._clientHeight||this.forceVerticalBar)&&(this._grid.setColumnDefinition(1,this._barSize,!0),this._verticalBar.isVisible=!0,this._rebuildLayout=!0),this._buildClientSizes();var i=this.host.idealRatio;this._horizontalBar.thumbWidth=.9*this._thumbLength*(this._clientWidth/i)+"px",this._verticalBar.thumbWidth=.9*this._thumbLength*(this._clientHeight/i)+"px"},e.prototype._link=function(e){t.prototype._link.call(this,e),this._attachWheel()},e.prototype._addBar=function(t,e,i,r){var o=this;t.paddingLeft=0,t.width="100%",t.height="100%",t.barOffset=0,t.value=0,t.maximum=1,t.horizontalAlignment=f.HORIZONTAL_ALIGNMENT_CENTER,t.verticalAlignment=f.VERTICAL_ALIGNMENT_CENTER,t.isVertical=i,t.rotation=r,t.isVisible=!1,e.addControl(t),t.onValueChangedObservable.add((function(t){o._setWindowPosition()}))},e.prototype._attachWheel=function(){var t=this;this._host&&!this._onWheelObserver&&(this._onWheelObserver=this.onWheelObservable.add((function(e){t._pointerIsOver&&(1==t._verticalBar.isVisible&&(e.y<0&&t._verticalBar.value>0?t._verticalBar.value-=t._wheelPrecision:e.y>0&&t._verticalBar.value<t._verticalBar.maximum&&(t._verticalBar.value+=t._wheelPrecision)),1==t._horizontalBar.isVisible&&(e.x<0&&t._horizontalBar.value<t._horizontalBar.maximum?t._horizontalBar.value+=t._wheelPrecision:e.x>0&&t._horizontalBar.value>0&&(t._horizontalBar.value-=t._wheelPrecision)))})))},e.prototype._renderHighlightSpecific=function(e){this.isHighlighted&&(t.prototype._renderHighlightSpecific.call(this,e),this._grid._renderHighlightSpecific(e),e.restore())},e.prototype.dispose=function(){this.onWheelObservable.remove(this._onWheelObserver),this._onWheelObserver=null,t.prototype.dispose.call(this)},e}(m);s._TypeStore.RegisteredTypes["BABYLON.GUI.ScrollViewer"]=U;var X=function(t){function e(e,i){var r=t.call(this,e)||this;r.name=e,r.onIsActiveChangedObservable=new s.Observable,r.delegatePickingToChildren=!1,r._isActive=!1,r.group=null!=i?i:"",r.thickness=0,r.isPointerBlocker=!0;var o=null;return r.toActiveAnimation=function(){r.thickness=1},r.toInactiveAnimation=function(){r.thickness=0},r.pointerEnterActiveAnimation=function(){o=r.alpha,r.alpha-=.1},r.pointerOutActiveAnimation=function(){null!==o&&(r.alpha=o)},r.pointerDownActiveAnimation=function(){r.scaleX-=.05,r.scaleY-=.05},r.pointerUpActiveAnimation=function(){r.scaleX+=.05,r.scaleY+=.05},r.pointerEnterInactiveAnimation=function(){o=r.alpha,r.alpha-=.1},r.pointerOutInactiveAnimation=function(){null!==o&&(r.alpha=o)},r.pointerDownInactiveAnimation=function(){r.scaleX-=.05,r.scaleY-=.05},r.pointerUpInactiveAnimation=function(){r.scaleX+=.05,r.scaleY+=.05},r}return o(e,t),Object.defineProperty(e.prototype,"image",{get:function(){return this._image},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"textBlock",{get:function(){return this._textBlock},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"group",{get:function(){return this._group},set:function(t){this._group!==t&&(this._group=t)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isActive",{get:function(){return this._isActive},set:function(t){var e,i,r=this;this._isActive!==t&&(this._isActive=t,this._isActive?null===(e=this.toActiveAnimation)||void 0===e||e.call(this):null===(i=this.toInactiveAnimation)||void 0===i||i.call(this),this._markAsDirty(),this.onIsActiveChangedObservable.notifyObservers(t),this._isActive&&this._host&&this._group&&this._host.executeOnAllControls((function(t){if("ToggleButton"===t.typeName){if(t===r)return;var e=t;e.group===r.group&&(e.isActive=!1)}})))},enumerable:!1,configurable:!0}),e.prototype._getTypeName=function(){return"ToggleButton"},e.prototype._processPicking=function(e,i,r,o,n,s,a,h){if(!this._isEnabled||!this.isHitTestVisible||!this.isVisible||this.notRenderable)return!1;if(!t.prototype.contains.call(this,e,i))return!1;if(this.delegatePickingToChildren){for(var l=!1,c=this._children.length-1;c>=0;c--){var u=this._children[c];if(u.isEnabled&&u.isHitTestVisible&&u.isVisible&&!u.notRenderable&&u.contains(e,i)){l=!0;break}}if(!l)return!1}return this._processObservables(o,e,i,r,n,s,a,h),!0},e.prototype._onPointerEnter=function(e,i){return!!t.prototype._onPointerEnter.call(this,e,i)&&(this._isActive?this.pointerEnterActiveAnimation&&this.pointerEnterActiveAnimation():this.pointerEnterInactiveAnimation&&this.pointerEnterInactiveAnimation(),!0)},e.prototype._onPointerOut=function(e,i,r){void 0===r&&(r=!1),this._isActive?this.pointerOutActiveAnimation&&this.pointerOutActiveAnimation():this.pointerOutInactiveAnimation&&this.pointerOutInactiveAnimation(),t.prototype._onPointerOut.call(this,e,i,r)},e.prototype._onPointerDown=function(e,i,r,o,n){return!!t.prototype._onPointerDown.call(this,e,i,r,o,n)&&(this._isActive?this.pointerDownActiveAnimation&&this.pointerDownActiveAnimation():this.pointerDownInactiveAnimation&&this.pointerDownInactiveAnimation(),!0)},e.prototype._onPointerUp=function(e,i,r,o,n,s){this._isActive?this.pointerUpActiveAnimation&&this.pointerUpActiveAnimation():this.pointerUpInactiveAnimation&&this.pointerUpInactiveAnimation(),t.prototype._onPointerUp.call(this,e,i,r,o,n,s)},e}(m);s._TypeStore.RegisteredTypes["BABYLON.GUI.ToggleButton"]=X;var Y=function(){},Q=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.onKeyPressObservable=new s.Observable,e.defaultButtonWidth="40px",e.defaultButtonHeight="40px",e.defaultButtonPaddingLeft="2px",e.defaultButtonPaddingRight="2px",e.defaultButtonPaddingTop="2px",e.defaultButtonPaddingBottom="2px",e.defaultButtonColor="#DDD",e.defaultButtonBackground="#070707",e.shiftButtonColor="#7799FF",e.selectedShiftThickness=1,e.shiftState=0,e._currentlyConnectedInputText=null,e._connectedInputTexts=[],e._onKeyPressObserver=null,e}return o(e,t),e.prototype._getTypeName=function(){return"VirtualKeyboard"},e.prototype._createKey=function(t,e){var i=this,r=O.CreateSimpleButton(t,t);return r.width=e&&e.width?e.width:this.defaultButtonWidth,r.height=e&&e.height?e.height:this.defaultButtonHeight,r.color=e&&e.color?e.color:this.defaultButtonColor,r.background=e&&e.background?e.background:this.defaultButtonBackground,r.paddingLeft=e&&e.paddingLeft?e.paddingLeft:this.defaultButtonPaddingLeft,r.paddingRight=e&&e.paddingRight?e.paddingRight:this.defaultButtonPaddingRight,r.paddingTop=e&&e.paddingTop?e.paddingTop:this.defaultButtonPaddingTop,r.paddingBottom=e&&e.paddingBottom?e.paddingBottom:this.defaultButtonPaddingBottom,r.thickness=0,r.isFocusInvisible=!0,r.shadowColor=this.shadowColor,r.shadowBlur=this.shadowBlur,r.shadowOffsetX=this.shadowOffsetX,r.shadowOffsetY=this.shadowOffsetY,r.onPointerUpObservable.add((function(){i.onKeyPressObservable.notifyObservers(t)})),r},e.prototype.addKeysRow=function(t,e){var i=new C;i.isVertical=!1,i.isFocusInvisible=!0;for(var r=null,o=0;o<t.length;o++){var n=null;e&&e.length===t.length&&(n=e[o]);var s=this._createKey(t[o],n);(!r||s.heightInPixels>r.heightInPixels)&&(r=s),i.addControl(s)}i.height=r?r.height:this.defaultButtonHeight,this.addControl(i)},e.prototype.applyShiftState=function(t){if(this.children)for(var e=0;e<this.children.length;e++){var i=this.children[e];if(i&&i.children)for(var r=i,o=0;o<r.children.length;o++){var n=r.children[o];if(n&&n.children[0]){var s=n.children[0];"⇧"===s.text&&(n.color=t?this.shiftButtonColor:this.defaultButtonColor,n.thickness=t>1?this.selectedShiftThickness:0),s.text=t>0?s.text.toUpperCase():s.text.toLowerCase()}}}},Object.defineProperty(e.prototype,"connectedInputText",{get:function(){return this._currentlyConnectedInputText},enumerable:!1,configurable:!0}),e.prototype.connect=function(t){var e=this;if(!this._connectedInputTexts.some((function(e){return e.input===t}))){null===this._onKeyPressObserver&&(this._onKeyPressObserver=this.onKeyPressObservable.add((function(t){if(e._currentlyConnectedInputText){switch(e._currentlyConnectedInputText._host.focusedControl=e._currentlyConnectedInputText,t){case"⇧":return e.shiftState++,e.shiftState>2&&(e.shiftState=0),void e.applyShiftState(e.shiftState);case"←":return void e._currentlyConnectedInputText.processKey(8);case"↵":return void e._currentlyConnectedInputText.processKey(13)}e._currentlyConnectedInputText.processKey(-1,e.shiftState?t.toUpperCase():t),1===e.shiftState&&(e.shiftState=0,e.applyShiftState(e.shiftState))}}))),this.isVisible=!1,this._currentlyConnectedInputText=t,t._connectedVirtualKeyboard=this;var i=t.onFocusObservable.add((function(){e._currentlyConnectedInputText=t,t._connectedVirtualKeyboard=e,e.isVisible=!0})),r=t.onBlurObservable.add((function(){t._connectedVirtualKeyboard=null,e._currentlyConnectedInputText=null,e.isVisible=!1}));this._connectedInputTexts.push({input:t,onBlurObserver:r,onFocusObserver:i})}},e.prototype.disconnect=function(t){var e=this;if(t){var i=this._connectedInputTexts.filter((function(e){return e.input===t}));1===i.length&&(this._removeConnectedInputObservables(i[0]),this._connectedInputTexts=this._connectedInputTexts.filter((function(e){return e.input!==t})),this._currentlyConnectedInputText===t&&(this._currentlyConnectedInputText=null))}else this._connectedInputTexts.forEach((function(t){e._removeConnectedInputObservables(t)})),this._connectedInputTexts=[];0===this._connectedInputTexts.length&&(this._currentlyConnectedInputText=null,this.onKeyPressObservable.remove(this._onKeyPressObserver),this._onKeyPressObserver=null)},e.prototype._removeConnectedInputObservables=function(t){t.input._connectedVirtualKeyboard=null,t.input.onFocusObservable.remove(t.onFocusObserver),t.input.onBlurObservable.remove(t.onBlurObserver)},e.prototype.dispose=function(){t.prototype.dispose.call(this),this.disconnect()},e.CreateDefaultLayout=function(t){var i=new e(t);return i.addKeysRow(["1","2","3","4","5","6","7","8","9","0","←"]),i.addKeysRow(["q","w","e","r","t","y","u","i","o","p"]),i.addKeysRow(["a","s","d","f","g","h","j","k","l",";","'","↵"]),i.addKeysRow(["⇧","z","x","c","v","b","n","m",",",".","/"]),i.addKeysRow([" "],[{width:"200px"}]),i},e}(C);s._TypeStore.RegisteredTypes["BABYLON.GUI.VirtualKeyboard"]=Q;var K=function(t){function e(e){var i=t.call(this,e)||this;return i.name=e,i._cellWidth=20,i._cellHeight=20,i._minorLineTickness=1,i._minorLineColor="DarkGray",i._majorLineTickness=2,i._majorLineColor="White",i._majorLineFrequency=5,i._background="Black",i._displayMajorLines=!0,i._displayMinorLines=!0,i}return o(e,t),Object.defineProperty(e.prototype,"displayMinorLines",{get:function(){return this._displayMinorLines},set:function(t){this._displayMinorLines!==t&&(this._displayMinorLines=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"displayMajorLines",{get:function(){return this._displayMajorLines},set:function(t){this._displayMajorLines!==t&&(this._displayMajorLines=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"background",{get:function(){return this._background},set:function(t){this._background!==t&&(this._background=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"cellWidth",{get:function(){return this._cellWidth},set:function(t){this._cellWidth=t,this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"cellHeight",{get:function(){return this._cellHeight},set:function(t){this._cellHeight=t,this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"minorLineTickness",{get:function(){return this._minorLineTickness},set:function(t){this._minorLineTickness=t,this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"minorLineColor",{get:function(){return this._minorLineColor},set:function(t){this._minorLineColor=t,this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"majorLineTickness",{get:function(){return this._majorLineTickness},set:function(t){this._majorLineTickness=t,this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"majorLineColor",{get:function(){return this._majorLineColor},set:function(t){this._majorLineColor=t,this._markAsDirty()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"majorLineFrequency",{get:function(){return this._majorLineFrequency},set:function(t){this._majorLineFrequency=t,this._markAsDirty()},enumerable:!1,configurable:!0}),e.prototype._draw=function(t,e){if(t.save(),this._applyStates(t),this._isEnabled){this._background&&(t.fillStyle=this._background,t.fillRect(this._currentMeasure.left,this._currentMeasure.top,this._currentMeasure.width,this._currentMeasure.height));var i=this._currentMeasure.width/this._cellWidth,r=this._currentMeasure.height/this._cellHeight,o=this._currentMeasure.left+this._currentMeasure.width/2,n=this._currentMeasure.top+this._currentMeasure.height/2;if(this._displayMinorLines){t.strokeStyle=this._minorLineColor,t.lineWidth=this._minorLineTickness;for(var s=-i/2;s<i/2;s++){var a=o+s*this.cellWidth;t.beginPath(),t.moveTo(a,this._currentMeasure.top),t.lineTo(a,this._currentMeasure.top+this._currentMeasure.height),t.stroke()}for(var h=-r/2;h<r/2;h++){var l=n+h*this.cellHeight;t.beginPath(),t.moveTo(this._currentMeasure.left,l),t.lineTo(this._currentMeasure.left+this._currentMeasure.width,l),t.stroke()}}if(this._displayMajorLines){t.strokeStyle=this._majorLineColor,t.lineWidth=this._majorLineTickness;for(s=-i/2+this._majorLineFrequency;s<i/2;s+=this._majorLineFrequency){a=o+s*this.cellWidth;t.beginPath(),t.moveTo(a,this._currentMeasure.top),t.lineTo(a,this._currentMeasure.top+this._currentMeasure.height),t.stroke()}for(h=-r/2+this._majorLineFrequency;h<r/2;h+=this._majorLineFrequency){l=n+h*this.cellHeight;t.moveTo(this._currentMeasure.left,l),t.lineTo(this._currentMeasure.left+this._currentMeasure.width,l),t.closePath(),t.stroke()}}}t.restore()},e.prototype._getTypeName=function(){return"DisplayGrid"},n([Object(s.serialize)()],e.prototype,"displayMinorLines",null),n([Object(s.serialize)()],e.prototype,"displayMajorLines",null),n([Object(s.serialize)()],e.prototype,"background",null),n([Object(s.serialize)()],e.prototype,"cellWidth",null),n([Object(s.serialize)()],e.prototype,"cellHeight",null),n([Object(s.serialize)()],e.prototype,"minorLineTickness",null),n([Object(s.serialize)()],e.prototype,"minorLineColor",null),n([Object(s.serialize)()],e.prototype,"majorLineTickness",null),n([Object(s.serialize)()],e.prototype,"majorLineColor",null),n([Object(s.serialize)()],e.prototype,"majorLineFrequency",null),e}(f);s._TypeStore.RegisteredTypes["BABYLON.GUI.DisplayGrid"]=K;var q=function(t){function e(e){var i=t.call(this,e)||this;return i.name=e,i._tempMeasure=new _(0,0,0,0),i}return o(e,t),Object.defineProperty(e.prototype,"displayThumb",{get:function(){return this._displayThumb&&null!=this.thumbImage},set:function(t){this._displayThumb!==t&&(this._displayThumb=t,this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"backgroundImage",{get:function(){return this._backgroundImage},set:function(t){var e=this;this._backgroundImage!==t&&(this._backgroundImage=t,t&&!t.isLoaded&&t.onImageLoadedObservable.addOnce((function(){return e._markAsDirty()})),this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"valueBarImage",{get:function(){return this._valueBarImage},set:function(t){var e=this;this._valueBarImage!==t&&(this._valueBarImage=t,t&&!t.isLoaded&&t.onImageLoadedObservable.addOnce((function(){return e._markAsDirty()})),this._markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"thumbImage",{get:function(){return this._thumbImage},set:function(t){var e=this;this._thumbImage!==t&&(this._thumbImage=t,t&&!t.isLoaded&&t.onImageLoadedObservable.addOnce((function(){return e._markAsDirty()})),this._markAsDirty())},enumerable:!1,configurable:!0}),e.prototype._getTypeName=function(){return"ImageBasedSlider"},e.prototype._draw=function(t,e){t.save(),this._applyStates(t),this._prepareRenderingData("rectangle");var i=this._getThumbPosition(),r=this._renderLeft,o=this._renderTop,n=this._renderWidth,s=this._renderHeight;this._backgroundImage&&(this._tempMeasure.copyFromFloats(r,o,n,s),this.isThumbClamped&&this.displayThumb&&(this.isVertical?this._tempMeasure.height+=this._effectiveThumbThickness:this._tempMeasure.width+=this._effectiveThumbThickness),this._backgroundImage._currentMeasure.copyFrom(this._tempMeasure),this._backgroundImage._draw(t)),this._valueBarImage&&(this.isVertical?this.isThumbClamped&&this.displayThumb?this._tempMeasure.copyFromFloats(r,o+i,n,s-i+this._effectiveThumbThickness):this._tempMeasure.copyFromFloats(r,o+i,n,s-i):this.isThumbClamped&&this.displayThumb?this._tempMeasure.copyFromFloats(r,o,i+this._effectiveThumbThickness/2,s):this._tempMeasure.copyFromFloats(r,o,i,s),this._valueBarImage._currentMeasure.copyFrom(this._tempMeasure),this._valueBarImage._draw(t)),this.displayThumb&&(this.isVertical?this._tempMeasure.copyFromFloats(r-this._effectiveBarOffset,this._currentMeasure.top+i,this._currentMeasure.width,this._effectiveThumbThickness):this._tempMeasure.copyFromFloats(this._currentMeasure.left+i,this._currentMeasure.top,this._effectiveThumbThickness,this._currentMeasure.height),this._thumbImage._currentMeasure.copyFrom(this._tempMeasure),this._thumbImage._draw(t)),t.restore()},n([Object(s.serialize)()],e.prototype,"displayThumb",null),e}(L);s._TypeStore.RegisteredTypes["BABYLON.GUI.ImageBasedSlider"]=q;var Z="Statics";f.AddHeader=function(t,e,i,r){var o=new C("panel"),n=!r||r.isHorizontal,s=!r||r.controlFirst;o.isVertical=!n;var a=new y("header");return a.text=e,a.textHorizontalAlignment=f.HORIZONTAL_ALIGNMENT_LEFT,n?a.width=i:a.height=i,s?(o.addControl(t),o.addControl(a),a.paddingLeft="5px"):(o.addControl(a),o.addControl(t),a.paddingRight="5px"),a.shadowBlur=t.shadowBlur,a.shadowColor=t.shadowColor,a.shadowOffsetX=t.shadowOffsetX,a.shadowOffsetY=t.shadowOffsetY,o};var J=function(){function t(t){this._fontFamily="Arial",this._fontStyle="",this._fontWeight="",this._fontSize=new a(18,a.UNITMODE_PIXEL,!1),this.onChangedObservable=new s.Observable,this._host=t}return Object.defineProperty(t.prototype,"fontSize",{get:function(){return this._fontSize.toString(this._host)},set:function(t){this._fontSize.toString(this._host)!==t&&this._fontSize.fromString(t)&&this.onChangedObservable.notifyObservers(this)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontFamily",{get:function(){return this._fontFamily},set:function(t){this._fontFamily!==t&&(this._fontFamily=t,this.onChangedObservable.notifyObservers(this))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontStyle",{get:function(){return this._fontStyle},set:function(t){this._fontStyle!==t&&(this._fontStyle=t,this.onChangedObservable.notifyObservers(this))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"fontWeight",{get:function(){return this._fontWeight},set:function(t){this._fontWeight!==t&&(this._fontWeight=t,this.onChangedObservable.notifyObservers(this))},enumerable:!1,configurable:!0}),t.prototype.dispose=function(){this.onChangedObservable.clear()},t}(),$=function(t){function e(e,i,r,o,n,a,h){void 0===i&&(i=0),void 0===r&&(r=0),void 0===n&&(n=!1),void 0===a&&(a=s.Texture.NEAREST_SAMPLINGMODE);var l=t.call(this,e,{width:i,height:r},o,n,a,s.Constants.TEXTUREFORMAT_RGBA,h)||this;return l._isDirty=!1,l._rootContainer=new g("root"),l._lastControlOver={},l._lastControlDown={},l._capturingControl={},l._linkedControls=new Array,l._isFullscreen=!1,l._fullscreenViewport=new s.Viewport(0,0,1,1),l._idealWidth=0,l._idealHeight=0,l._useSmallestIdeal=!1,l._renderAtIdealSize=!1,l._blockNextFocusCheck=!1,l._renderScale=1,l._cursorChanged=!1,l._defaultMousePointerId=0,l._numLayoutCalls=0,l._numRenderCalls=0,l._clipboardData="",l.onClipboardObservable=new s.Observable,l.onControlPickedObservable=new s.Observable,l.onBeginLayoutObservable=new s.Observable,l.onEndLayoutObservable=new s.Observable,l.onBeginRenderObservable=new s.Observable,l.onEndRenderObservable=new s.Observable,l.premulAlpha=!1,l.applyYInversionOnUpdate=!0,l._useInvalidateRectOptimization=!0,l._invalidatedRectangle=null,l._clearMeasure=new _(0,0,0,0),l.onClipboardCopy=function(t){var e=t,i=new s.ClipboardInfo(s.ClipboardEventTypes.COPY,e);l.onClipboardObservable.notifyObservers(i),e.preventDefault()},l.onClipboardCut=function(t){var e=t,i=new s.ClipboardInfo(s.ClipboardEventTypes.CUT,e);l.onClipboardObservable.notifyObservers(i),e.preventDefault()},l.onClipboardPaste=function(t){var e=t,i=new s.ClipboardInfo(s.ClipboardEventTypes.PASTE,e);l.onClipboardObservable.notifyObservers(i),e.preventDefault()},(o=l.getScene())&&l._texture?(l._rootElement=o.getEngine().getInputElement(),l._renderObserver=o.onBeforeCameraRenderObservable.add((function(t){return l._checkUpdate(t)})),l._preKeyboardObserver=o.onPreKeyboardObservable.add((function(t){l._focusedControl&&(t.type===s.KeyboardEventTypes.KEYDOWN&&l._focusedControl.processKeyboard(t.event),t.skipOnPointerObservable=!0)})),l._rootContainer._link(l),l.hasAlpha=!0,i&&r||(l._resizeObserver=o.getEngine().onResizeObservable.add((function(){return l._onResize()})),l._onResize()),l._texture.isReady=!0,l):l}return o(e,t),Object.defineProperty(e.prototype,"numLayoutCalls",{get:function(){return this._numLayoutCalls},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"numRenderCalls",{get:function(){return this._numRenderCalls},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"renderScale",{get:function(){return this._renderScale},set:function(t){t!==this._renderScale&&(this._renderScale=t,this._onResize())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"background",{get:function(){return this._background},set:function(t){this._background!==t&&(this._background=t,this.markAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"idealWidth",{get:function(){return this._idealWidth},set:function(t){this._idealWidth!==t&&(this._idealWidth=t,this.markAsDirty(),this._rootContainer._markAllAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"idealHeight",{get:function(){return this._idealHeight},set:function(t){this._idealHeight!==t&&(this._idealHeight=t,this.markAsDirty(),this._rootContainer._markAllAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"useSmallestIdeal",{get:function(){return this._useSmallestIdeal},set:function(t){this._useSmallestIdeal!==t&&(this._useSmallestIdeal=t,this.markAsDirty(),this._rootContainer._markAllAsDirty())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"renderAtIdealSize",{get:function(){return this._renderAtIdealSize},set:function(t){this._renderAtIdealSize!==t&&(this._renderAtIdealSize=t,this._onResize())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"idealRatio",{get:function(){var t=0,e=0;return this._idealWidth&&(t=this.getSize().width/this._idealWidth),this._idealHeight&&(e=this.getSize().height/this._idealHeight),this._useSmallestIdeal&&this._idealWidth&&this._idealHeight?window.innerWidth<window.innerHeight?t:e:this._idealWidth?t:this._idealHeight?e:1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"layer",{get:function(){return this._layerToDispose},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"rootContainer",{get:function(){return this._rootContainer},enumerable:!1,configurable:!0}),e.prototype.getChildren=function(){return[this._rootContainer]},e.prototype.getDescendants=function(t,e){return this._rootContainer.getDescendants(t,e)},Object.defineProperty(e.prototype,"focusedControl",{get:function(){return this._focusedControl},set:function(t){this._focusedControl!=t&&(this._focusedControl&&this._focusedControl.onBlur(),t&&t.onFocus(),this._focusedControl=t)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isForeground",{get:function(){return!this.layer||!this.layer.isBackground},set:function(t){this.layer&&this.layer.isBackground!==!t&&(this.layer.isBackground=!t)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"clipboardData",{get:function(){return this._clipboardData},set:function(t){this._clipboardData=t},enumerable:!1,configurable:!0}),e.prototype.getClassName=function(){return"AdvancedDynamicTexture"},e.prototype.executeOnAllControls=function(t,e){e||(e=this._rootContainer),t(e);for(var i=0,r=e.children;i<r.length;i++){var o=r[i];o.children?this.executeOnAllControls(t,o):t(o)}},Object.defineProperty(e.prototype,"useInvalidateRectOptimization",{get:function(){return this._useInvalidateRectOptimization},set:function(t){this._useInvalidateRectOptimization=t},enumerable:!1,configurable:!0}),e.prototype.invalidateRect=function(t,e,i,r){if(this._useInvalidateRectOptimization)if(this._invalidatedRectangle){var o=Math.ceil(Math.max(this._invalidatedRectangle.left+this._invalidatedRectangle.width-1,i)),n=Math.ceil(Math.max(this._invalidatedRectangle.top+this._invalidatedRectangle.height-1,r));this._invalidatedRectangle.left=Math.floor(Math.min(this._invalidatedRectangle.left,t)),this._invalidatedRectangle.top=Math.floor(Math.min(this._invalidatedRectangle.top,e)),this._invalidatedRectangle.width=o-this._invalidatedRectangle.left+1,this._invalidatedRectangle.height=n-this._invalidatedRectangle.top+1}else this._invalidatedRectangle=new _(t,e,i-t+1,r-e+1)},e.prototype.markAsDirty=function(){this._isDirty=!0},e.prototype.createStyle=function(){return new J(this)},e.prototype.addControl=function(t){return this._rootContainer.addControl(t),this},e.prototype.removeControl=function(t){return this._rootContainer.removeControl(t),this},e.prototype.dispose=function(){var e=this.getScene();e&&(this._rootElement=null,e.onBeforeCameraRenderObservable.remove(this._renderObserver),this._resizeObserver&&e.getEngine().onResizeObservable.remove(this._resizeObserver),this._pointerMoveObserver&&e.onPrePointerObservable.remove(this._pointerMoveObserver),this._pointerObserver&&e.onPointerObservable.remove(this._pointerObserver),this._preKeyboardObserver&&e.onPreKeyboardObservable.remove(this._preKeyboardObserver),this._canvasPointerOutObserver&&e.getEngine().onCanvasPointerOutObservable.remove(this._canvasPointerOutObserver),this._canvasBlurObserver&&e.getEngine().onCanvasBlurObservable.remove(this._canvasBlurObserver),this._layerToDispose&&(this._layerToDispose.texture=null,this._layerToDispose.dispose(),this._layerToDispose=null),this._rootContainer.dispose(),this.onClipboardObservable.clear(),this.onControlPickedObservable.clear(),this.onBeginRenderObservable.clear(),this.onEndRenderObservable.clear(),this.onBeginLayoutObservable.clear(),this.onEndLayoutObservable.clear(),t.prototype.dispose.call(this))},e.prototype._onResize=function(){var t=this.getScene();if(t){var e=t.getEngine(),i=this.getSize(),r=e.getRenderWidth()*this._renderScale,o=e.getRenderHeight()*this._renderScale;this._renderAtIdealSize&&(this._idealWidth?(o=o*this._idealWidth/r,r=this._idealWidth):this._idealHeight&&(r=r*this._idealHeight/o,o=this._idealHeight)),i.width===r&&i.height===o||(this.scaleTo(r,o),this.markAsDirty(),(this._idealWidth||this._idealHeight)&&this._rootContainer._markAllAsDirty()),this.invalidateRect(0,0,i.width-1,i.height-1)}},e.prototype._getGlobalViewport=function(t){var e=t.getEngine();return this._fullscreenViewport.toGlobal(e.getRenderWidth(),e.getRenderHeight())},e.prototype.getProjectedPosition=function(t,e){var i=this.getScene();if(!i)return s.Vector2.Zero();var r=this._getGlobalViewport(i),o=s.Vector3.Project(t,e,i.getTransformMatrix(),r);return o.scaleInPlace(this.renderScale),new s.Vector2(o.x,o.y)},e.prototype.getProjectedPositionWithZ=function(t,e){var i=this.getScene();if(!i)return s.Vector3.Zero();var r=this._getGlobalViewport(i),o=s.Vector3.Project(t,e,i.getTransformMatrix(),r);return o.scaleInPlace(this.renderScale),new s.Vector3(o.x,o.y,o.z)},e.prototype._checkUpdate=function(t){if(!this._layerToDispose||0!=(t.layerMask&this._layerToDispose.layerMask)){if(this._isFullscreen&&this._linkedControls.length){var e=this.getScene();if(!e)return;for(var i=this._getGlobalViewport(e),r=function(t){if(!t.isVisible)return"continue";var r=t._linkedMesh;if(!r||r.isDisposed())return s.Tools.SetImmediate((function(){t.linkWithMesh(null)})),"continue";var n=r.getBoundingInfo?r.getBoundingInfo().boundingSphere.center:s.Vector3.ZeroReadOnly,a=s.Vector3.Project(n,r.getWorldMatrix(),e.getTransformMatrix(),i);if(a.z<0||a.z>1)return t.notRenderable=!0,"continue";t.notRenderable=!1,a.scaleInPlace(o.renderScale),t._moveToProjectedPosition(a)},o=this,n=0,a=this._linkedControls;n<a.length;n++){r(a[n])}}(this._isDirty||this._rootContainer.isDirty)&&(this._isDirty=!1,this._render(),this.update(this.applyYInversionOnUpdate,this.premulAlpha))}},e.prototype._render=function(){var t=this.getSize(),e=t.width,i=t.height,r=this.getContext();r.font="18px Arial",r.strokeStyle="white",this.onBeginLayoutObservable.notifyObservers(this);var o=new _(0,0,e,i);this._numLayoutCalls=0,this._rootContainer._layout(o,r),this.onEndLayoutObservable.notifyObservers(this),this._isDirty=!1,this._invalidatedRectangle?this._clearMeasure.copyFrom(this._invalidatedRectangle):this._clearMeasure.copyFromFloats(0,0,e,i),r.clearRect(this._clearMeasure.left,this._clearMeasure.top,this._clearMeasure.width,this._clearMeasure.height),this._background&&(r.save(),r.fillStyle=this._background,r.fillRect(this._clearMeasure.left,this._clearMeasure.top,this._clearMeasure.width,this._clearMeasure.height),r.restore()),this.onBeginRenderObservable.notifyObservers(this),this._numRenderCalls=0,this._rootContainer._render(r,this._invalidatedRectangle),this.onEndRenderObservable.notifyObservers(this),this._invalidatedRectangle=null},e.prototype._changeCursor=function(t){this._rootElement&&(this._rootElement.style.cursor=t,this._cursorChanged=!0)},e.prototype._registerLastControlDown=function(t,e){this._lastControlDown[e]=t,this.onControlPickedObservable.notifyObservers(t)},e.prototype._doPicking=function(t,e,i,r,o,n,a,h){var l=this.getScene();if(l){var c=l.getEngine(),u=this.getSize();if(this._isFullscreen){var _=(l.cameraToUseForPointers||l.activeCamera).viewport;t*=u.width/(c.getRenderWidth()*_.width),e*=u.height/(c.getRenderHeight()*_.height)}this._capturingControl[o]?this._capturingControl[o]._processObservables(r,t,e,i,o,n):(this._cursorChanged=!1,this._rootContainer._processPicking(t,e,i,r,o,n,a,h)||(l.doNotHandleCursors||this._changeCursor(""),r===s.PointerEventTypes.POINTERMOVE&&this._lastControlOver[o]&&(this._lastControlOver[o]._onPointerOut(this._lastControlOver[o],i),delete this._lastControlOver[o])),this._cursorChanged||l.doNotHandleCursors||this._changeCursor(""),this._manageFocus())}},e.prototype._cleanControlAfterRemovalFromList=function(t,e){for(var i in t){if(t.hasOwnProperty(i))t[i]===e&&delete t[i]}},e.prototype._cleanControlAfterRemoval=function(t){this._cleanControlAfterRemovalFromList(this._lastControlDown,t),this._cleanControlAfterRemovalFromList(this._lastControlOver,t)},e.prototype.attach=function(){var t=this,e=this.getScene();if(e){var i=new s.Viewport(0,0,0,0);this._pointerMoveObserver=e.onPrePointerObservable.add((function(r,o){if(!e.isPointerCaptured(r.event.pointerId)&&(r.type===s.PointerEventTypes.POINTERMOVE||r.type===s.PointerEventTypes.POINTERUP||r.type===s.PointerEventTypes.POINTERDOWN||r.type===s.PointerEventTypes.POINTERWHEEL)&&e){r.type===s.PointerEventTypes.POINTERMOVE&&r.event.pointerId&&(t._defaultMousePointerId=r.event.pointerId);var n=e.cameraToUseForPointers||e.activeCamera,a=e.getEngine();n?n.viewport.toGlobalToRef(a.getRenderWidth(),a.getRenderHeight(),i):(i.x=0,i.y=0,i.width=a.getRenderWidth(),i.height=a.getRenderHeight());var h=e.pointerX/a.getHardwareScalingLevel()-i.x,l=e.pointerY/a.getHardwareScalingLevel()-(a.getRenderHeight()-i.y-i.height);t._shouldBlockPointer=!1;var c=r.event.pointerId||t._defaultMousePointerId;t._doPicking(h,l,r,r.type,c,r.event.button,r.event.deltaX,r.event.deltaY),t._shouldBlockPointer&&(r.skipOnPointerObservable=t._shouldBlockPointer)}})),this._attachToOnPointerOut(e),this._attachToOnBlur(e)}},e.prototype.registerClipboardEvents=function(){self.addEventListener("copy",this.onClipboardCopy,!1),self.addEventListener("cut",this.onClipboardCut,!1),self.addEventListener("paste",this.onClipboardPaste,!1)},e.prototype.unRegisterClipboardEvents=function(){self.removeEventListener("copy",this.onClipboardCopy),self.removeEventListener("cut",this.onClipboardCut),self.removeEventListener("paste",this.onClipboardPaste)},e.prototype.attachToMesh=function(t,e){var i=this;void 0===e&&(e=!0);var r=this.getScene();r&&(this._pointerObserver=r.onPointerObservable.add((function(e,r){if(e.type===s.PointerEventTypes.POINTERMOVE||e.type===s.PointerEventTypes.POINTERUP||e.type===s.PointerEventTypes.POINTERDOWN){var o=e.event.pointerId||i._defaultMousePointerId;if(e.pickInfo&&e.pickInfo.hit&&e.pickInfo.pickedMesh===t){var n=e.pickInfo.getTextureCoordinates();if(n){var a=i.getSize();i._doPicking(n.x*a.width,(i.applyYInversionOnUpdate?1-n.y:n.y)*a.height,e,e.type,o,e.event.button)}}else if(e.type===s.PointerEventTypes.POINTERUP){if(i._lastControlDown[o]&&i._lastControlDown[o]._forcePointerUp(o),delete i._lastControlDown[o],i.focusedControl){var h=i.focusedControl.keepsFocusWith(),l=!0;if(h)for(var c=0,u=h;c<u.length;c++){var _=u[c];if(i!==_._host){var d=_._host;if(d._lastControlOver[o]&&d._lastControlOver[o].isAscendant(_)){l=!1;break}}}l&&(i.focusedControl=null)}}else e.type===s.PointerEventTypes.POINTERMOVE&&(i._lastControlOver[o]&&i._lastControlOver[o]._onPointerOut(i._lastControlOver[o],e,!0),delete i._lastControlOver[o])}})),t.enablePointerMoveEvents=e,this._attachToOnPointerOut(r),this._attachToOnBlur(r))},e.prototype.moveFocusToControl=function(t){this.focusedControl=t,this._lastPickedControl=t,this._blockNextFocusCheck=!0},e.prototype._manageFocus=function(){if(this._blockNextFocusCheck)return this._blockNextFocusCheck=!1,void(this._lastPickedControl=this._focusedControl);if(this._focusedControl&&this._focusedControl!==this._lastPickedControl){if(this._lastPickedControl.isFocusInvisible)return;this.focusedControl=null}},e.prototype._attachToOnPointerOut=function(t){var e=this;this._canvasPointerOutObserver=t.getEngine().onCanvasPointerOutObservable.add((function(t){e._lastControlOver[t.pointerId]&&e._lastControlOver[t.pointerId]._onPointerOut(e._lastControlOver[t.pointerId],null),delete e._lastControlOver[t.pointerId],e._lastControlDown[t.pointerId]&&e._lastControlDown[t.pointerId]!==e._capturingControl[t.pointerId]&&(e._lastControlDown[t.pointerId]._forcePointerUp(),delete e._lastControlDown[t.pointerId])}))},e.prototype._attachToOnBlur=function(t){var e=this;this._canvasBlurObserver=t.getEngine().onCanvasBlurObservable.add((function(t){Object.entries(e._lastControlDown).forEach((function(t){t[0];t[1]._onCanvasBlur()})),e._lastControlDown={}}))},e.prototype.serializeContent=function(){var t={root:{}};return this._rootContainer.serialize(t.root),t},e.prototype.parseContent=function(t){this._rootContainer=f.Parse(t.root,this)},e.prototype.parseFromSnippetAsync=function(t){var i=this;return"_BLANK"===t?Promise.resolve():new Promise((function(r,o){var n=new s.WebRequest;n.addEventListener("readystatechange",(function(){if(4==n.readyState)if(200==n.status){var e=JSON.parse(JSON.parse(n.responseText).jsonPayload),s=JSON.parse(e.gui);i.parseContent(s),i.snippetId=t,r()}else o("Unable to load the snippet "+t)})),n.open("GET",e.SnippetUrl+"/"+t.replace(/#/g,"/")),n.send()}))},e.CreateForMesh=function(t,i,r,o,n,a){void 0===i&&(i=1024),void 0===r&&(r=1024),void 0===o&&(o=!0),void 0===n&&(n=!1);var h=new e(t.name+" AdvancedDynamicTexture",i,r,t.getScene(),!0,s.Texture.TRILINEAR_SAMPLINGMODE,a),l=new s.StandardMaterial("AdvancedDynamicTextureMaterial",t.getScene());return l.backFaceCulling=!1,l.diffuseColor=s.Color3.Black(),l.specularColor=s.Color3.Black(),n?(l.diffuseTexture=h,l.emissiveTexture=h,h.hasAlpha=!0):(l.emissiveTexture=h,l.opacityTexture=h),t.material=l,h.attachToMesh(t,o),h},e.CreateForMeshTexture=function(t,i,r,o,n){void 0===i&&(i=1024),void 0===r&&(r=1024),void 0===o&&(o=!0);var a=new e(t.name+" AdvancedDynamicTexture",i,r,t.getScene(),!0,s.Texture.TRILINEAR_SAMPLINGMODE,n);return a.attachToMesh(t,o),a},e.CreateFullscreenUI=function(t,i,r,o){void 0===i&&(i=!0),void 0===r&&(r=null),void 0===o&&(o=s.Texture.BILINEAR_SAMPLINGMODE);var n=new e(t,0,0,r,!1,o),a=new s.Layer(t+"_layer",null,r,!i);return a.texture=n,n._layerToDispose=a,n._isFullscreen=!0,n.attach(),n},e.SnippetUrl="https://snippet.babylonjs.com",e}(s.DynamicTexture),tt=function(){function t(t){this.texture=t,this._captureRenderTime=!1,this._renderTime=new s.PerfCounter,this._captureLayoutTime=!1,this._layoutTime=new s.PerfCounter,this._onBeginRenderObserver=null,this._onEndRenderObserver=null,this._onBeginLayoutObserver=null,this._onEndLayoutObserver=null}return Object.defineProperty(t.prototype,"renderTimeCounter",{get:function(){return this._renderTime},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"layoutTimeCounter",{get:function(){return this._layoutTime},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"captureRenderTime",{get:function(){return this._captureRenderTime},set:function(t){var e=this;t!==this._captureRenderTime&&(this._captureRenderTime=t,t?(this._onBeginRenderObserver=this.texture.onBeginRenderObservable.add((function(){e._renderTime.beginMonitoring()})),this._onEndRenderObserver=this.texture.onEndRenderObservable.add((function(){e._renderTime.endMonitoring(!0)}))):(this.texture.onBeginRenderObservable.remove(this._onBeginRenderObserver),this._onBeginRenderObserver=null,this.texture.onEndRenderObservable.remove(this._onEndRenderObserver),this._onEndRenderObserver=null))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"captureLayoutTime",{get:function(){return this._captureLayoutTime},set:function(t){var e=this;t!==this._captureLayoutTime&&(this._captureLayoutTime=t,t?(this._onBeginLayoutObserver=this.texture.onBeginLayoutObservable.add((function(){e._layoutTime.beginMonitoring()})),this._onEndLayoutObserver=this.texture.onEndLayoutObservable.add((function(){e._layoutTime.endMonitoring(!0)}))):(this.texture.onBeginLayoutObservable.remove(this._onBeginLayoutObserver),this._onBeginLayoutObserver=null,this.texture.onEndLayoutObservable.remove(this._onEndLayoutObserver),this._onEndLayoutObserver=null))},enumerable:!1,configurable:!0}),t.prototype.dispose=function(){this.texture.onBeginRenderObservable.remove(this._onBeginRenderObserver),this._onBeginRenderObserver=null,this.texture.onEndRenderObservable.remove(this._onEndRenderObserver),this._onEndRenderObserver=null,this.texture.onBeginLayoutObservable.remove(this._onBeginLayoutObserver),this._onBeginLayoutObserver=null,this.texture.onEndLayoutObservable.remove(this._onEndLayoutObserver),this._onEndLayoutObserver=null,this.texture=null},t}(),et=function(){function t(t){void 0===t&&(t=null),this._nodes={},this._nodeTypes={element:1,attribute:2,text:3},this._isLoaded=!1,this._objectAttributes={textHorizontalAlignment:1,textVerticalAlignment:2,horizontalAlignment:3,verticalAlignment:4,stretch:5},t&&(this._parentClass=t)}return t.prototype._getChainElement=function(t){var e=window;this._parentClass&&(e=this._parentClass);var i=t;i=i.split(".");for(var r=0;r<i.length;r++)e=e[i[r]];return e},t.prototype._getClassAttribute=function(t){var e=t.split(".");return s._TypeStore.GetClass("BABYLON.GUI."+e[0])[e[1]]},t.prototype._createGuiElement=function(t,e,i){void 0===i&&(i=!0);try{var r=new(s._TypeStore.GetClass("BABYLON.GUI."+t.nodeName));e&&i&&e.addControl(r);for(var o=0;o<t.attributes.length;o++)if(!t.attributes[o].name.toLowerCase().includes("datasource"))if(t.attributes[o].name.toLowerCase().includes("observable")){var n=this._getChainElement(t.attributes[o].value);r[t.attributes[o].name].add(n)}else if("linkWithMesh"==t.attributes[o].name)this._parentClass?r.linkWithMesh(this._parentClass[t.attributes[o].value]):r.linkWithMesh(window[t.attributes[o].value]);else if(t.attributes[o].value.startsWith("{{")&&t.attributes[o].value.endsWith("}}")){n=this._getChainElement(t.attributes[o].value.substring(2,t.attributes[o].value.length-2));r[t.attributes[o].name]=n}else this._objectAttributes[t.attributes[o].name]?r[t.attributes[o].name]=this._getClassAttribute(t.attributes[o].value):"true"==t.attributes[o].value||"false"==t.attributes[o].value?r[t.attributes[o].name]="true"==t.attributes[o].value:r[t.attributes[o].name]=isNaN(Number(t.attributes[o].value))?t.attributes[o].value:Number(t.attributes[o].value);if(!t.attributes.getNamedItem("id"))return this._nodes[t.nodeName+Object.keys(this._nodes).length+"_gen"]=r,r;var a=t.attributes.getNamedItem("id").value;if(a.startsWith("{{")&&a.endsWith("}}")&&(a=this._getChainElement(a.substring(2,a.length-2))),this._nodes[a])throw"XmlLoader Exception : Duplicate ID, every element should have an unique ID attribute";return this._nodes[a]=r,r}catch(e){throw"XmlLoader Exception : Error parsing Control "+t.nodeName+","+e+"."}},t.prototype._parseGrid=function(t,e,i){for(var r,o,n,s,a,h=t.children,l=!1,c=-1,u=-1,_=0,d=0;d<h.length;d++)if(h[d].nodeType==this._nodeTypes.element){if("Row"!=h[d].nodeName)throw"XmlLoader Exception : Expecting Row node, received "+h[d].nodeName;if(c+=1,n=h[d].children,!h[d].attributes.getNamedItem("height"))throw"XmlLoader Exception : Height must be defined for grid rows";o=Number(h[d].attributes.getNamedItem("height").nodeValue),l=!!h[d].attributes.getNamedItem("isPixel")&&JSON.parse(h[d].attributes.getNamedItem("isPixel").nodeValue),e.addRowDefinition(o,l);for(var p=0;p<n.length;p++)if(n[p].nodeType==this._nodeTypes.element){if("Column"!=n[p].nodeName)throw"XmlLoader Exception : Expecting Column node, received "+n[p].nodeName;if(u+=1,c>0&&u>_)throw"XmlLoader Exception : In the Grid element, the number of columns is defined in the first row, do not add more columns in the subsequent rows.";if(0==c){if(!n[p].attributes.getNamedItem("width"))throw"XmlLoader Exception : Width must be defined for all the grid columns in the first row";r=Number(n[p].attributes.getNamedItem("width").nodeValue),l=!!n[p].attributes.getNamedItem("isPixel")&&JSON.parse(n[p].attributes.getNamedItem("isPixel").nodeValue),e.addColumnDefinition(r,l)}s=n[p].children;for(var f=0;f<s.length;f++)s[f].nodeType==this._nodeTypes.element&&(a=this._createGuiElement(s[f],e,!1),e.addControl(a,c,u),s[f].firstChild&&this._parseXml(s[f].firstChild,a))}0==c&&(_=u),u=-1}t.nextSibling&&this._parseXml(t.nextSibling,i)},t.prototype._parseElement=function(t,e,i){t.firstChild&&this._parseXml(t.firstChild,e),t.nextSibling&&this._parseXml(t.nextSibling,i)},t.prototype._prepareSourceElement=function(t,e,i,r,o){this._parentClass?this._parentClass[i]=r[o]:window[i]=r[o],t.firstChild&&this._parseXml(t.firstChild,e,!0)},t.prototype._parseElementsFromSource=function(t,e,i){var r=t.attributes.getNamedItem("dataSource").value;if(!r.includes(" in "))throw"XmlLoader Exception : Malformed XML, Data Source must include an in";var o=!0,n=r.split(" in ");if(n.length<2)throw"XmlLoader Exception : Malformed XML, Data Source must an iterator and a source";var s=n[1];if(s.startsWith("{")&&s.endsWith("}")&&(o=!1),(!o||s.startsWith("[")&&s.endsWith("]"))&&(s=s.substring(1,s.length-1)),s=this._parentClass?this._parentClass[s]:window[s],o)for(var a=0;a<s.length;a++)this._prepareSourceElement(t,e,n[0],s,a);else for(var a in s)this._prepareSourceElement(t,e,n[0],s,a);t.nextSibling&&this._parseXml(t.nextSibling,i)},t.prototype._parseXml=function(t,e,i){if(void 0===i&&(i=!1),t.nodeType==this._nodeTypes.element){i&&t.setAttribute("id",e.id+(e._children.length+1));var r=this._createGuiElement(t,e);"Grid"==t.nodeName?this._parseGrid(t,r,e):t.attributes.getNamedItem("dataSource")?this._parseElementsFromSource(t,r,e):this._parseElement(t,r,e)}else t.nextSibling&&this._parseXml(t.nextSibling,e,i)},t.prototype.isLoaded=function(){return this._isLoaded},t.prototype.getNodeById=function(t){return this._nodes[t]},t.prototype.getNodes=function(){return this._nodes},t.prototype.loadLayout=function(t,e,i){var r=new XMLHttpRequest;r.onreadystatechange=function(){if(4==r.readyState&&200==r.status){if(!r.responseXML)throw"XmlLoader Exception : XML file is malformed or corrupted.";var t=r.responseXML.documentElement;this._parseXml(t.firstChild,e),this._isLoaded=!0,i&&i()}}.bind(this),r.open("GET",t,!0),r.send()},t}(),it=function(t){function e(e,i){void 0===i&&(i=0);var r=t.call(this,e.x,e.y,e.z)||this;return r.buttonIndex=i,r}return o(e,t),e}(s.Vector3),rt=function(){function t(t){this.name=t,this._downCount=0,this._enterCount=-1,this._downPointerIds={},this._isVisible=!0,this.onPointerMoveObservable=new s.Observable,this.onPointerOutObservable=new s.Observable,this.onPointerDownObservable=new s.Observable,this.onPointerUpObservable=new s.Observable,this.onPointerClickObservable=new s.Observable,this.onPointerEnterObservable=new s.Observable,this._behaviors=new Array}return Object.defineProperty(t.prototype,"position",{get:function(){return this._node?this._node.position:s.Vector3.Zero()},set:function(t){this._node&&(this._node.position=t)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"scaling",{get:function(){return this._node?this._node.scaling:new s.Vector3(1,1,1)},set:function(t){this._node&&(this._node.scaling=t)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"behaviors",{get:function(){return this._behaviors},enumerable:!1,configurable:!0}),t.prototype.addBehavior=function(t){var e=this;if(-1!==this._behaviors.indexOf(t))return this;t.init();var i=this._host.scene;return i.isLoading?i.onDataLoadedObservable.addOnce((function(){t.attach(e)})):t.attach(this),this._behaviors.push(t),this},t.prototype.removeBehavior=function(t){var e=this._behaviors.indexOf(t);return-1===e||(this._behaviors[e].detach(),this._behaviors.splice(e,1)),this},t.prototype.getBehaviorByName=function(t){for(var e=0,i=this._behaviors;e<i.length;e++){var r=i[e];if(r.name===t)return r}return null},Object.defineProperty(t.prototype,"isVisible",{get:function(){return this._isVisible},set:function(t){if(this._isVisible!==t){this._isVisible=t;var e=this.mesh;e&&e.setEnabled(t)}},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"typeName",{get:function(){return this._getTypeName()},enumerable:!1,configurable:!0}),t.prototype.getClassName=function(){return this._getTypeName()},t.prototype._getTypeName=function(){return"Control3D"},Object.defineProperty(t.prototype,"node",{get:function(){return this._node},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"mesh",{get:function(){return this._node instanceof s.AbstractMesh?this._node:null},enumerable:!1,configurable:!0}),t.prototype.linkToTransformNode=function(t){return this._node&&(this._node.parent=t),this},t.prototype._prepareNode=function(t){if(!this._node){if(this._node=this._createNode(t),!this.node)return;this._injectGUI3DMetadata(this.node).control=this;var e=this.mesh;e&&(e.isPickable=!0,this._affectMaterial(e))}},t.prototype._injectGUI3DMetadata=function(t){var e,i;return t.metadata=null!==(e=t.metadata)&&void 0!==e?e:{},t.metadata.GUI3D=null!==(i=t.metadata.GUI3D)&&void 0!==i?i:{},t.metadata.GUI3D},t.prototype._createNode=function(t){return null},t.prototype._affectMaterial=function(t){t.material=null},t.prototype._onPointerMove=function(t,e){this.onPointerMoveObservable.notifyObservers(e,-1,t,this)},t.prototype._onPointerEnter=function(t){return-1===this._enterCount&&(this._enterCount=0),this._enterCount++,!(this._enterCount>1)&&(this.onPointerEnterObservable.notifyObservers(this,-1,t,this),this.pointerEnterAnimation&&this.pointerEnterAnimation(),!0)},t.prototype._onPointerOut=function(t){this._enterCount--,this._enterCount>0||(this._enterCount=0,this.onPointerOutObservable.notifyObservers(this,-1,t,this),this.pointerOutAnimation&&this.pointerOutAnimation())},t.prototype._onPointerDown=function(t,e,i,r){return this._downCount++,this._downPointerIds[i]=this._downPointerIds[i]+1||1,1===this._downCount&&(this.onPointerDownObservable.notifyObservers(new it(e,r),-1,t,this),this.pointerDownAnimation&&this.pointerDownAnimation(),!0)},t.prototype._onPointerUp=function(t,e,i,r,o){this._downCount--,this._downPointerIds[i]--,this._downPointerIds[i]<=0&&delete this._downPointerIds[i],this._downCount<0?this._downCount=0:0==this._downCount&&(o&&(this._enterCount>0||-1===this._enterCount)&&this.onPointerClickObservable.notifyObservers(new it(e,r),-1,t,this),this.onPointerUpObservable.notifyObservers(new it(e,r),-1,t,this),this.pointerUpAnimation&&this.pointerUpAnimation())},t.prototype.forcePointerUp=function(t){if(void 0===t&&(t=null),null!==t)this._onPointerUp(this,s.Vector3.Zero(),t,0,!0);else{for(var e in this._downPointerIds)this._onPointerUp(this,s.Vector3.Zero(),+e,0,!0);this._downCount>0&&(this._downCount=1,this._onPointerUp(this,s.Vector3.Zero(),0,0,!0))}},t.prototype._processObservables=function(t,e,i,r){if(t===s.PointerEventTypes.POINTERMOVE){this._onPointerMove(this,e);var o=this._host._lastControlOver[i];return o&&o!==this&&o._onPointerOut(this),o!==this&&this._onPointerEnter(this),this._host._lastControlOver[i]=this,!0}return t===s.PointerEventTypes.POINTERDOWN?(this._onPointerDown(this,e,i,r),this._host._lastControlDown[i]=this,this._host._lastPickedControl=this,!0):(t===s.PointerEventTypes.POINTERUP||t===s.PointerEventTypes.POINTERDOUBLETAP)&&(this._host._lastControlDown[i]&&this._host._lastControlDown[i]._onPointerUp(this,e,i,r,!0),delete this._host._lastControlDown[i],!0)},t.prototype._disposeNode=function(){this._node&&(this._node.dispose(),this._node=null)},t.prototype.dispose=function(){this.onPointerDownObservable.clear(),this.onPointerEnterObservable.clear(),this.onPointerMoveObservable.clear(),this.onPointerOutObservable.clear(),this.onPointerUpObservable.clear(),this.onPointerClickObservable.clear(),this._disposeNode();for(var t=0,e=this._behaviors;t<e.length;t++){e[t].detach()}},t}(),ot=function(t){function e(e){return t.call(this,e)||this}return o(e,t),e.prototype._getTypeName=function(){return"AbstractButton3D"},e.prototype._createNode=function(t){return new s.TransformNode("button"+this.name)},e}(rt),nt=function(t){function e(e){var i=t.call(this,e)||this;return i._contentResolution=512,i._contentScaleRatio=2,i.pointerEnterAnimation=function(){i.mesh&&(i._currentMaterial.emissiveColor=s.Color3.Red())},i.pointerOutAnimation=function(){i._currentMaterial.emissiveColor=s.Color3.Black()},i.pointerDownAnimation=function(){i.mesh&&i.mesh.scaling.scaleInPlace(.95)},i.pointerUpAnimation=function(){i.mesh&&i.mesh.scaling.scaleInPlace(1/.95)},i}return o(e,t),Object.defineProperty(e.prototype,"contentResolution",{get:function(){return this._contentResolution},set:function(t){this._contentResolution!==t&&(this._contentResolution=t,this._resetContent())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"contentScaleRatio",{get:function(){return this._contentScaleRatio},set:function(t){this._contentScaleRatio!==t&&(this._contentScaleRatio=t,this._resetContent())},enumerable:!1,configurable:!0}),e.prototype._disposeFacadeTexture=function(){this._facadeTexture&&(this._facadeTexture.dispose(),this._facadeTexture=null)},e.prototype._resetContent=function(){this._disposeFacadeTexture(),this.content=this._content},Object.defineProperty(e.prototype,"content",{get:function(){return this._content},set:function(t){this._content=t,this._host&&this._host.utilityLayer&&(this._facadeTexture?this._facadeTexture.rootContainer.clearControls():(this._facadeTexture=new $("Facade",this._contentResolution,this._contentResolution,this._host.utilityLayer.utilityLayerScene,!0,s.Texture.TRILINEAR_SAMPLINGMODE),this._facadeTexture.rootContainer.scaleX=this._contentScaleRatio,this._facadeTexture.rootContainer.scaleY=this._contentScaleRatio,this._facadeTexture.premulAlpha=!0),this._facadeTexture.addControl(t),this._applyFacade(this._facadeTexture))},enumerable:!1,configurable:!0}),e.prototype._applyFacade=function(t){this._currentMaterial.emissiveTexture=t},e.prototype._getTypeName=function(){return"Button3D"},e.prototype._createNode=function(t){for(var e=new Array(6),i=0;i<6;i++)e[i]=new s.Vector4(0,0,0,0);return e[1]=new s.Vector4(0,0,1,1),s.BoxBuilder.CreateBox(this.name+"_rootMesh",{width:1,height:1,depth:.08,faceUV:e},t)},e.prototype._affectMaterial=function(t){var e=new s.StandardMaterial(this.name+"Material",t.getScene());e.specularColor=s.Color3.Black(),t.material=e,this._currentMaterial=e,this._resetContent()},e.prototype.dispose=function(){t.prototype.dispose.call(this),this._disposeFacadeTexture(),this._currentMaterial&&this._currentMaterial.dispose()},e}(ot),st=function(t){function e(e){var i=t.call(this,e)||this;return i._blockLayout=!1,i._children=new Array,i}return o(e,t),Object.defineProperty(e.prototype,"children",{get:function(){return this._children},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"blockLayout",{get:function(){return this._blockLayout},set:function(t){this._blockLayout!==t&&(this._blockLayout=t,this._blockLayout||this._arrangeChildren())},enumerable:!1,configurable:!0}),e.prototype.updateLayout=function(){return this._arrangeChildren(),this},e.prototype.containsControl=function(t){return-1!==this._children.indexOf(t)},e.prototype.addControl=function(t){return-1!==this._children.indexOf(t)||(t.parent=this,t._host=this._host,this._children.push(t),this._host.utilityLayer&&(t._prepareNode(this._host.utilityLayer.utilityLayerScene),t.node&&(t.node.parent=this.node),this.blockLayout||this._arrangeChildren())),this},e.prototype._arrangeChildren=function(){},e.prototype._createNode=function(t){return new s.TransformNode("ContainerNode",t)},e.prototype.removeControl=function(t){var e=this._children.indexOf(t);return-1!==e&&(this._children.splice(e,1),t.parent=null,t._disposeNode()),this},e.prototype._getTypeName=function(){return"Container3D"},e.prototype.dispose=function(){for(var e=0,i=this._children;e<i.length;e++){i[e].dispose()}this._children=[],t.prototype.dispose.call(this)},e.UNSET_ORIENTATION=0,e.FACEORIGIN_ORIENTATION=1,e.FACEORIGINREVERSED_ORIENTATION=2,e.FACEFORWARD_ORIENTATION=3,e.FACEFORWARDREVERSED_ORIENTATION=4,e}(rt),at=function(t){function e(){var e=t.call(this)||this;return e._columns=10,e._rows=0,e._rowThenColum=!0,e._orientation=st.FACEORIGIN_ORIENTATION,e.margin=0,e}return o(e,t),Object.defineProperty(e.prototype,"orientation",{get:function(){return this._orientation},set:function(t){var e=this;this._orientation!==t&&(this._orientation=t,s.Tools.SetImmediate((function(){e._arrangeChildren()})))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"columns",{get:function(){return this._columns},set:function(t){var e=this;this._columns!==t&&(this._columns=t,this._rowThenColum=!0,s.Tools.SetImmediate((function(){e._arrangeChildren()})))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"rows",{get:function(){return this._rows},set:function(t){var e=this;this._rows!==t&&(this._rows=t,this._rowThenColum=!1,s.Tools.SetImmediate((function(){e._arrangeChildren()})))},enumerable:!1,configurable:!0}),e.prototype._arrangeChildren=function(){this._cellWidth=0,this._cellHeight=0;for(var t=0,e=0,i=0,r=s.Matrix.Invert(this.node.computeWorldMatrix(!0)),o=0,n=this._children;o<n.length;o++){if((m=n[o]).mesh){i++,m.mesh.computeWorldMatrix(!0);var a=m.mesh.getHierarchyBoundingVectors(),h=s.TmpVectors.Vector3[0],l=s.TmpVectors.Vector3[1];a.max.subtractToRef(a.min,l),l.scaleInPlace(.5),s.Vector3.TransformNormalToRef(l,r,h),this._cellWidth=Math.max(this._cellWidth,2*h.x),this._cellHeight=Math.max(this._cellHeight,2*h.y)}}this._cellWidth+=2*this.margin,this._cellHeight+=2*this.margin,this._rowThenColum?(e=this._columns,t=Math.ceil(i/this._columns)):(t=this._rows,e=Math.ceil(i/this._rows));var c=.5*e*this._cellWidth,u=.5*t*this._cellHeight,_=[],d=0;if(this._rowThenColum)for(var p=0;p<t;p++)for(var f=0;f<e&&(_.push(new s.Vector3(f*this._cellWidth-c+this._cellWidth/2,p*this._cellHeight-u+this._cellHeight/2,0)),!(++d>i));f++);else for(f=0;f<e;f++)for(p=0;p<t&&(_.push(new s.Vector3(f*this._cellWidth-c+this._cellWidth/2,p*this._cellHeight-u+this._cellHeight/2,0)),!(++d>i));p++);d=0;for(var g=0,b=this._children;g<b.length;g++){var m;(m=b[g]).mesh&&(this._mapGridNode(m,_[d]),d++)}this._finalProcessing()},e.prototype._finalProcessing=function(){},e}(st),ht=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e._radius=5,e}return o(e,t),Object.defineProperty(e.prototype,"radius",{get:function(){return this._radius},set:function(t){var e=this;this._radius!==t&&(this._radius=t,s.Tools.SetImmediate((function(){e._arrangeChildren()})))},enumerable:!1,configurable:!0}),e.prototype._mapGridNode=function(t,e){var i=t.mesh;if(i){var r=this._cylindricalMapping(e);switch(t.position=r,this.orientation){case st.FACEORIGIN_ORIENTATION:i.lookAt(new s.Vector3(2*r.x,r.y,2*r.z));break;case st.FACEORIGINREVERSED_ORIENTATION:i.lookAt(new s.Vector3(-r.x,r.y,-r.z));break;case st.FACEFORWARD_ORIENTATION:break;case st.FACEFORWARDREVERSED_ORIENTATION:i.rotate(s.Axis.Y,Math.PI,s.Space.LOCAL)}}},e.prototype._cylindricalMapping=function(t){var e=new s.Vector3(0,t.y,this._radius),i=t.x/this._radius;return s.Matrix.RotationYawPitchRollToRef(i,0,0,s.TmpVectors.Matrix[0]),s.Vector3.TransformNormal(e,s.TmpVectors.Matrix[0])},e}(at),lt="precision highp float;\n\nattribute vec3 position;\nattribute vec3 normal;\nattribute vec2 uv;\n\nuniform mat4 world;\nuniform mat4 viewProjection;\nvarying vec2 vUV;\n#ifdef BORDER\nvarying vec2 scaleInfo;\nuniform float borderWidth;\nuniform vec3 scaleFactor;\n#endif\n#ifdef HOVERLIGHT\nvarying vec3 worldPosition;\n#endif\nvoid main(void) {\nvUV=uv;\n#ifdef BORDER\nvec3 scale=scaleFactor;\nfloat minScale=min(min(scale.x,scale.y),scale.z);\nfloat maxScale=max(max(scale.x,scale.y),scale.z);\nfloat minOverMiddleScale=minScale/(scale.x+scale.y+scale.z-minScale-maxScale);\nfloat areaYZ=scale.y*scale.z;\nfloat areaXZ=scale.x*scale.z;\nfloat areaXY=scale.x*scale.y;\nfloat scaledBorderWidth=borderWidth;\nif (abs(normal.x) == 1.0)\n{\nscale.x=scale.y;\nscale.y=scale.z;\nif (areaYZ>areaXZ && areaYZ>areaXY)\n{\nscaledBorderWidth*=minOverMiddleScale;\n}\n}\nelse if (abs(normal.y) == 1.0)\n{\nscale.x=scale.z;\nif (areaXZ>areaXY && areaXZ>areaYZ)\n{\nscaledBorderWidth*=minOverMiddleScale;\n}\n}\nelse\n{\nif (areaXY>areaYZ && areaXY>areaXZ)\n{\nscaledBorderWidth*=minOverMiddleScale;\n}\n}\nfloat scaleRatio=min(scale.x,scale.y)/max(scale.x,scale.y);\nif (scale.x>scale.y)\n{\nscaleInfo.x=1.0-(scaledBorderWidth*scaleRatio);\nscaleInfo.y=1.0-scaledBorderWidth;\n}\nelse\n{\nscaleInfo.x=1.0-scaledBorderWidth;\nscaleInfo.y=1.0-(scaledBorderWidth*scaleRatio);\n}\n#endif\nvec4 worldPos=world*vec4(position,1.0);\n#ifdef HOVERLIGHT\nworldPosition=worldPos.xyz;\n#endif\ngl_Position=viewProjection*worldPos;\n}\n";s.Effect.ShadersStore.fluentVertexShader=lt;var ct="precision highp float;\nvarying vec2 vUV;\nuniform vec4 albedoColor;\n#ifdef INNERGLOW\nuniform vec4 innerGlowColor;\n#endif\n#ifdef BORDER\nvarying vec2 scaleInfo;\nuniform float edgeSmoothingValue;\nuniform float borderMinValue;\n#endif\n#ifdef HOVERLIGHT\nvarying vec3 worldPosition;\nuniform vec3 hoverPosition;\nuniform vec4 hoverColor;\nuniform float hoverRadius;\n#endif\n#ifdef TEXTURE\nuniform sampler2D albedoSampler;\n#endif\nvoid main(void) {\nvec3 albedo=albedoColor.rgb;\nfloat alpha=albedoColor.a;\n#ifdef TEXTURE\nalbedo=texture2D(albedoSampler,vUV).rgb;\n#endif\n#ifdef HOVERLIGHT\nfloat pointToHover=(1.0-clamp(length(hoverPosition-worldPosition)/hoverRadius,0.,1.))*hoverColor.a;\nalbedo=clamp(albedo+hoverColor.rgb*pointToHover,0.,1.);\n#else\nfloat pointToHover=1.0;\n#endif\n#ifdef BORDER\nfloat borderPower=10.0;\nfloat inverseBorderPower=1.0/borderPower;\nvec3 borderColor=albedo*borderPower;\nvec2 distanceToEdge;\ndistanceToEdge.x=abs(vUV.x-0.5)*2.0;\ndistanceToEdge.y=abs(vUV.y-0.5)*2.0;\nfloat borderValue=max(smoothstep(scaleInfo.x-edgeSmoothingValue,scaleInfo.x+edgeSmoothingValue,distanceToEdge.x),\nsmoothstep(scaleInfo.y-edgeSmoothingValue,scaleInfo.y+edgeSmoothingValue,distanceToEdge.y));\nborderColor=borderColor*borderValue*max(borderMinValue*inverseBorderPower,pointToHover);\nalbedo+=borderColor;\nalpha=max(alpha,borderValue);\n#endif\n#ifdef INNERGLOW\n\nvec2 uvGlow=(vUV-vec2(0.5,0.5))*(innerGlowColor.a*2.0);\nuvGlow=uvGlow*uvGlow;\nuvGlow=uvGlow*uvGlow;\nalbedo+=mix(vec3(0.0,0.0,0.0),innerGlowColor.rgb,uvGlow.x+uvGlow.y);\n#endif\ngl_FragColor=vec4(albedo,alpha);\n}";s.Effect.ShadersStore.fluentPixelShader=ct;var ut=function(t){function e(){var e=t.call(this)||this;return e.INNERGLOW=!1,e.BORDER=!1,e.HOVERLIGHT=!1,e.TEXTURE=!1,e.rebuild(),e}return o(e,t),e}(s.MaterialDefines),_t=function(t){function e(e,i){var r=t.call(this,e,i)||this;return r.innerGlowColorIntensity=.5,r.innerGlowColor=new s.Color3(1,1,1),r.albedoColor=new s.Color3(.3,.35,.4),r.renderBorders=!1,r.borderWidth=.5,r.edgeSmoothingValue=.02,r.borderMinValue=.1,r.renderHoverLight=!1,r.hoverRadius=1,r.hoverColor=new s.Color4(.3,.3,.3,1),r.hoverPosition=s.Vector3.Zero(),r}return o(e,t),e.prototype.needAlphaBlending=function(){return 1!==this.alpha},e.prototype.needAlphaTesting=function(){return!1},e.prototype.getAlphaTestTexture=function(){return null},e.prototype.isReadyForSubMesh=function(t,e,i){if(this.isFrozen&&e.effect&&e.effect._wasPreviouslyReady)return!0;e._materialDefines||(e.materialDefines=new ut);var r=this.getScene(),o=e._materialDefines;if(!this.checkReadyOnEveryCall&&e.effect&&o._renderId===r.getRenderId())return!0;if(o._areTexturesDirty)if(o.INNERGLOW=this.innerGlowColorIntensity>0,o.BORDER=this.renderBorders,o.HOVERLIGHT=this.renderHoverLight,this._albedoTexture){if(!this._albedoTexture.isReadyOrNotBlocking())return!1;o.TEXTURE=!0}else o.TEXTURE=!1;var n=r.getEngine();if(o.isDirty){o.markAsProcessed(),r.resetCachedMaterial();var a=[s.VertexBuffer.PositionKind];a.push(s.VertexBuffer.NormalKind),a.push(s.VertexBuffer.UVKind);var h=["world","viewProjection","innerGlowColor","albedoColor","borderWidth","edgeSmoothingValue","scaleFactor","borderMinValue","hoverColor","hoverPosition","hoverRadius"],l=["albedoSampler"],c=new Array;s.MaterialHelper.PrepareUniformsAndSamplersList({uniformsNames:h,uniformBuffersNames:c,samplers:l,defines:o,maxSimultaneousLights:4});var u=o.toString();e.setEffect(r.getEngine().createEffect("fluent",{attributes:a,uniformsNames:h,uniformBuffersNames:c,samplers:l,defines:u,fallbacks:null,onCompiled:this.onCompiled,onError:this.onError,indexParameters:{maxSimultaneousLights:4}},n),o,this._materialContext)}return!(!e.effect||!e.effect.isReady())&&(o._renderId=r.getRenderId(),e.effect._wasPreviouslyReady=!0,!0)},e.prototype.bindForSubMesh=function(t,e,i){var r=this.getScene(),o=i._materialDefines;if(o){var n=i.effect;n&&(this._activeEffect=n,this.bindOnlyWorldMatrix(t),this._activeEffect.setMatrix("viewProjection",r.getTransformMatrix()),this._mustRebind(r,n)&&(this._activeEffect.setColor4("albedoColor",this.albedoColor,this.alpha),o.INNERGLOW&&this._activeEffect.setColor4("innerGlowColor",this.innerGlowColor,this.innerGlowColorIntensity),o.BORDER&&(this._activeEffect.setFloat("borderWidth",this.borderWidth),this._activeEffect.setFloat("edgeSmoothingValue",this.edgeSmoothingValue),this._activeEffect.setFloat("borderMinValue",this.borderMinValue),e.getBoundingInfo().boundingBox.extendSize.multiplyToRef(e.scaling,s.TmpVectors.Vector3[0]),this._activeEffect.setVector3("scaleFactor",s.TmpVectors.Vector3[0])),o.HOVERLIGHT&&(this._activeEffect.setDirectColor4("hoverColor",this.hoverColor),this._activeEffect.setFloat("hoverRadius",this.hoverRadius),this._activeEffect.setVector3("hoverPosition",this.hoverPosition)),o.TEXTURE&&this._activeEffect.setTexture("albedoSampler",this._albedoTexture)),this._afterBind(e,this._activeEffect))}},e.prototype.getActiveTextures=function(){return t.prototype.getActiveTextures.call(this)},e.prototype.hasTexture=function(e){return!!t.prototype.hasTexture.call(this,e)},e.prototype.dispose=function(e){t.prototype.dispose.call(this,e)},e.prototype.clone=function(t){var i=this;return s.SerializationHelper.Clone((function(){return new e(t,i.getScene())}),this)},e.prototype.serialize=function(){var t=s.SerializationHelper.Serialize(this);return t.customType="BABYLON.GUI.FluentMaterial",t},e.prototype.getClassName=function(){return"FluentMaterial"},e.Parse=function(t,i,r){return s.SerializationHelper.Parse((function(){return new e(t.name,i)}),t,i,r)},n([Object(s.serialize)(),Object(s.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],e.prototype,"innerGlowColorIntensity",void 0),n([Object(s.serializeAsColor3)()],e.prototype,"innerGlowColor",void 0),n([Object(s.serializeAsColor3)()],e.prototype,"albedoColor",void 0),n([Object(s.serialize)(),Object(s.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],e.prototype,"renderBorders",void 0),n([Object(s.serialize)()],e.prototype,"borderWidth",void 0),n([Object(s.serialize)()],e.prototype,"edgeSmoothingValue",void 0),n([Object(s.serialize)()],e.prototype,"borderMinValue",void 0),n([Object(s.serialize)(),Object(s.expandToProperty)("_markAllSubMeshesAsTexturesDirty")],e.prototype,"renderHoverLight",void 0),n([Object(s.serialize)()],e.prototype,"hoverRadius",void 0),n([Object(s.serializeAsColor4)()],e.prototype,"hoverColor",void 0),n([Object(s.serializeAsVector3)()],e.prototype,"hoverPosition",void 0),n([Object(s.serializeAsTexture)("albedoTexture")],e.prototype,"_albedoTexture",void 0),n([Object(s.expandToProperty)("_markAllSubMeshesAsTexturesAndMiscDirty")],e.prototype,"albedoTexture",void 0),e}(s.PushMaterial);s._TypeStore.RegisteredTypes["BABYLON.GUI.FluentMaterial"]=_t;var dt,pt=function(t){function e(e,i){void 0===i&&(i=!0);var r=t.call(this,e)||this;return r._shareMaterials=!0,r._shareMaterials=i,r.pointerEnterAnimation=function(){r.mesh&&r._frontPlate.setEnabled(!0)},r.pointerOutAnimation=function(){r.mesh&&r._frontPlate.setEnabled(!1)},r}return o(e,t),e.prototype._disposeTooltip=function(){this._tooltipFade=null,this._tooltipTextBlock&&this._tooltipTextBlock.dispose(),this._tooltipTexture&&this._tooltipTexture.dispose(),this._tooltipMesh&&this._tooltipMesh.dispose(),this.onPointerEnterObservable.remove(this._tooltipHoverObserver),this.onPointerOutObservable.remove(this._tooltipOutObserver)},Object.defineProperty(e.prototype,"renderingGroupId",{get:function(){return this._backPlate.renderingGroupId},set:function(t){this._backPlate.renderingGroupId=t,this._textPlate.renderingGroupId=t,this._frontPlate.renderingGroupId=t,this._tooltipMesh&&(this._tooltipMesh.renderingGroupId=t)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"tooltipText",{get:function(){return this._tooltipTextBlock?this._tooltipTextBlock.text:null},set:function(t){var e=this;if(t){if(!this._tooltipFade){this._tooltipMesh=s.PlaneBuilder.CreatePlane("",{size:1},this._backPlate._scene);var i=s.PlaneBuilder.CreatePlane("",{size:1,sideOrientation:s.Mesh.DOUBLESIDE},this._backPlate._scene),r=new s.StandardMaterial("",this._backPlate._scene);r.diffuseColor=s.Color3.FromHexString("#212121"),i.material=r,i.isPickable=!1,this._tooltipMesh.addChild(i),i.position.z=.05,this._tooltipMesh.scaling.y=1/3,this._tooltipMesh.position.y=.7,this._tooltipMesh.position.z=-.15,this._tooltipMesh.isPickable=!1,this._tooltipMesh.parent=this._backPlate,this._tooltipTexture=$.CreateForMesh(this._tooltipMesh),this._tooltipTextBlock=new y,this._tooltipTextBlock.scaleY=3,this._tooltipTextBlock.color="white",this._tooltipTextBlock.fontSize=130,this._tooltipTexture.addControl(this._tooltipTextBlock),this._tooltipFade=new s.FadeInOutBehavior,this._tooltipFade.delay=500,this._tooltipMesh.addBehavior(this._tooltipFade),this._tooltipHoverObserver=this.onPointerEnterObservable.add((function(){e._tooltipFade&&e._tooltipFade.fadeIn(!0)})),this._tooltipOutObserver=this.onPointerOutObservable.add((function(){e._tooltipFade&&e._tooltipFade.fadeIn(!1)}))}this._tooltipTextBlock&&(this._tooltipTextBlock.text=t)}else this._disposeTooltip()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"text",{get:function(){return this._text},set:function(t){this._text!==t&&(this._text=t,this._rebuildContent())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"imageUrl",{get:function(){return this._imageUrl},set:function(t){this._imageUrl!==t&&(this._imageUrl=t,this._rebuildContent())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"backMaterial",{get:function(){return this._backMaterial},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"frontMaterial",{get:function(){return this._frontMaterial},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"plateMaterial",{get:function(){return this._plateMaterial},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"shareMaterials",{get:function(){return this._shareMaterials},enumerable:!1,configurable:!0}),e.prototype._getTypeName=function(){return"HolographicButton"},e.prototype._rebuildContent=function(){this._disposeFacadeTexture();var t=new C;if(t.isVertical=!0,this._imageUrl){var e=new v;e.source=this._imageUrl,e.paddingTop="40px",e.height="180px",e.width="100px",e.paddingBottom="40px",t.addControl(e)}if(this._text){var i=new y;i.text=this._text,i.color="white",i.height="30px",i.fontSize=24,t.addControl(i)}this._frontPlate&&(this.content=t)},e.prototype._createNode=function(e){return this._backPlate=s.BoxBuilder.CreateBox(this.name+"BackMesh",{width:1,height:1,depth:.08},e),this._frontPlate=s.BoxBuilder.CreateBox(this.name+"FrontMesh",{width:1,height:1,depth:.08},e),this._frontPlate.parent=this._backPlate,this._frontPlate.position.z=-.08,this._frontPlate.isPickable=!1,this._frontPlate.setEnabled(!1),this._textPlate=t.prototype._createNode.call(this,e),this._textPlate.parent=this._backPlate,this._textPlate.position.z=-.08,this._textPlate.isPickable=!1,this._backPlate},e.prototype._applyFacade=function(t){this._plateMaterial.emissiveTexture=t,this._plateMaterial.opacityTexture=t},e.prototype._createBackMaterial=function(t){var e=this;this._backMaterial=new _t(this.name+"Back Material",t.getScene()),this._backMaterial.renderHoverLight=!0,this._pickedPointObserver=this._host.onPickedPointChangedObservable.add((function(t){t?(e._backMaterial.hoverPosition=t,e._backMaterial.hoverColor.a=1):e._backMaterial.hoverColor.a=0}))},e.prototype._createFrontMaterial=function(t){this._frontMaterial=new _t(this.name+"Front Material",t.getScene()),this._frontMaterial.innerGlowColorIntensity=0,this._frontMaterial.alpha=.5,this._frontMaterial.renderBorders=!0},e.prototype._createPlateMaterial=function(t){this._plateMaterial=new s.StandardMaterial(this.name+"Plate Material",t.getScene()),this._plateMaterial.specularColor=s.Color3.Black()},e.prototype._affectMaterial=function(t){this._shareMaterials?(this._host._sharedMaterials.backFluentMaterial?this._backMaterial=this._host._sharedMaterials.backFluentMaterial:(this._createBackMaterial(t),this._host._sharedMaterials.backFluentMaterial=this._backMaterial),this._host._sharedMaterials.frontFluentMaterial?this._frontMaterial=this._host._sharedMaterials.frontFluentMaterial:(this._createFrontMaterial(t),this._host._sharedMaterials.frontFluentMaterial=this._frontMaterial)):(this._createBackMaterial(t),this._createFrontMaterial(t)),this._createPlateMaterial(t),this._backPlate.material=this._backMaterial,this._frontPlate.material=this._frontMaterial,this._textPlate.material=this._plateMaterial,this._rebuildContent()},e.prototype.dispose=function(){t.prototype.dispose.call(this),this._disposeTooltip(),this.shareMaterials||(this._backMaterial.dispose(),this._frontMaterial.dispose(),this._plateMaterial.dispose(),this._pickedPointObserver&&(this._host.onPickedPointChangedObservable.remove(this._pickedPointObserver),this._pickedPointObserver=null))},e}(nt),ft=function(t){function e(e,i){var r=t.call(this,i)||this;return r._currentMesh=e,r.pointerEnterAnimation=function(){r.mesh&&r.mesh.scaling.scaleInPlace(1.1)},r.pointerOutAnimation=function(){r.mesh&&r.mesh.scaling.scaleInPlace(1/1.1)},r.pointerDownAnimation=function(){r.mesh&&r.mesh.scaling.scaleInPlace(.95)},r.pointerUpAnimation=function(){r.mesh&&r.mesh.scaling.scaleInPlace(1/.95)},r}return o(e,t),e.prototype._getTypeName=function(){return"MeshButton3D"},e.prototype._createNode=function(t){var e=this;return this._currentMesh.getChildMeshes().forEach((function(t){e._injectGUI3DMetadata(t).control=e})),this._currentMesh},e.prototype._affectMaterial=function(t){},e}(nt),gt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype._mapGridNode=function(t,e){var i=t.mesh;if(i){t.position=e.clone();var r=s.TmpVectors.Vector3[0];switch(r.copyFrom(e),this.orientation){case st.FACEORIGIN_ORIENTATION:case st.FACEFORWARD_ORIENTATION:r.addInPlace(new s.Vector3(0,0,1)),i.lookAt(r);break;case st.FACEFORWARDREVERSED_ORIENTATION:case st.FACEORIGINREVERSED_ORIENTATION:r.addInPlace(new s.Vector3(0,0,-1)),i.lookAt(r)}}},e}(at),bt=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e._iteration=100,e}return o(e,t),Object.defineProperty(e.prototype,"iteration",{get:function(){return this._iteration},set:function(t){var e=this;this._iteration!==t&&(this._iteration=t,s.Tools.SetImmediate((function(){e._arrangeChildren()})))},enumerable:!1,configurable:!0}),e.prototype._mapGridNode=function(t,e){var i=t.mesh,r=this._scatterMapping(e);if(i){switch(this.orientation){case st.FACEORIGIN_ORIENTATION:case st.FACEFORWARD_ORIENTATION:i.lookAt(new s.Vector3(0,0,1));break;case st.FACEFORWARDREVERSED_ORIENTATION:case st.FACEORIGINREVERSED_ORIENTATION:i.lookAt(new s.Vector3(0,0,-1))}t.position=r}},e.prototype._scatterMapping=function(t){return t.x=(1-2*Math.random())*this._cellWidth,t.y=(1-2*Math.random())*this._cellHeight,t},e.prototype._finalProcessing=function(){for(var t=[],e=0,i=this._children;e<i.length;e++){var r=i[e];r.mesh&&t.push(r.mesh)}for(var o=0;o<this._iteration;o++){t.sort((function(t,e){var i=t.position.lengthSquared(),r=e.position.lengthSquared();return i<r?1:i>r?-1:0}));for(var n=Math.pow(this.margin,2),a=Math.max(this._cellWidth,this._cellHeight),h=s.TmpVectors.Vector2[0],l=s.TmpVectors.Vector3[0],c=0;c<t.length-1;c++)for(var u=c+1;u<t.length;u++)if(c!=u){t[u].position.subtractToRef(t[c].position,l),h.x=l.x,h.y=l.y;var _=a,d=h.lengthSquared()-n;(d-=Math.min(d,n))<Math.pow(_,2)&&(h.normalize(),l.scaleInPlace(.5*(_-Math.sqrt(d))),t[u].position.addInPlace(l),t[c].position.subtractInPlace(l))}}},e}(at),mt=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e._radius=5,e}return o(e,t),Object.defineProperty(e.prototype,"radius",{get:function(){return this._radius},set:function(t){var e=this;this._radius!==t&&(this._radius=t,s.Tools.SetImmediate((function(){e._arrangeChildren()})))},enumerable:!1,configurable:!0}),e.prototype._mapGridNode=function(t,e){var i=t.mesh;if(i){var r=this._sphericalMapping(e);switch(t.position=r,this.orientation){case st.FACEORIGIN_ORIENTATION:i.lookAt(new s.Vector3(2*r.x,2*r.y,2*r.z));break;case st.FACEORIGINREVERSED_ORIENTATION:i.lookAt(new s.Vector3(-r.x,-r.y,-r.z));break;case st.FACEFORWARD_ORIENTATION:break;case st.FACEFORWARDREVERSED_ORIENTATION:i.rotate(s.Axis.Y,Math.PI,s.Space.LOCAL)}}},e.prototype._sphericalMapping=function(t){var e=new s.Vector3(0,0,this._radius),i=t.y/this._radius,r=-t.x/this._radius;return s.Matrix.RotationYawPitchRollToRef(r,i,0,s.TmpVectors.Matrix[0]),s.Vector3.TransformNormal(e,s.TmpVectors.Matrix[0])},e}(at),yt=function(t){function e(e){void 0===e&&(e=!1);var i=t.call(this)||this;return i._isVertical=!1,i.margin=.1,i._isVertical=e,i}return o(e,t),Object.defineProperty(e.prototype,"isVertical",{get:function(){return this._isVertical},set:function(t){var e=this;this._isVertical!==t&&(this._isVertical=t,s.Tools.SetImmediate((function(){e._arrangeChildren()})))},enumerable:!1,configurable:!0}),e.prototype._arrangeChildren=function(){for(var t,e=0,i=0,r=0,o=[],n=s.Matrix.Invert(this.node.computeWorldMatrix(!0)),a=0,h=this._children;a<h.length;a++){if((p=h[a]).mesh){r++,p.mesh.computeWorldMatrix(!0),p.mesh.getWorldMatrix().multiplyToRef(n,s.TmpVectors.Matrix[0]);var l=p.mesh.getBoundingInfo().boundingBox,c=s.Vector3.TransformNormal(l.extendSize,s.TmpVectors.Matrix[0]);o.push(c),this._isVertical?i+=c.y:e+=c.x}}this._isVertical?i+=(r-1)*this.margin/2:e+=(r-1)*this.margin/2,t=this._isVertical?-i:-e;for(var u=0,_=0,d=this._children;_<d.length;_++){var p;if((p=d[_]).mesh){r--;c=o[u++];this._isVertical?(p.position.y=t+c.y,p.position.x=0,t+=2*c.y):(p.position.x=t+c.x,p.position.y=0,t+=2*c.x),t+=r>0?this.margin:0}}},e}(st);!function(t){t[t.None=0]="None",t[t.Hover=1]="Hover",t[t.Press=2]="Press"}(dt||(dt={}));var vt=function(t){function e(e,i){var r=t.call(this,e)||this;return r._collidableInitialized=!1,r._frontOffset=0,r._backOffset=0,r._hoverOffset=0,r._pushThroughBackOffset=0,r._activeInteractions=new Map,r._previousHeight=new Map,r._tempButtonForwardRay=new s.Ray(s.Vector3.Zero(),s.Vector3.Zero()),i&&(r.collisionMesh=i),r}return o(e,t),Object.defineProperty(e.prototype,"collidableFrontDirection",{set:function(t){this._collidableFrontDirection=t.normalize(),this._lastKnownCollidableScale=s.Vector3.Zero(),this._updateDistanceOffsets()},enumerable:!1,configurable:!0}),e.prototype._getWorldMatrixData=function(t){var e=s.Vector3.Zero(),i=s.Quaternion.Identity(),r=s.Vector3.Zero();return t.getWorldMatrix().decompose(r,i,e),{translation:e,rotation:i,scale:r}},Object.defineProperty(e.prototype,"collisionMesh",{set:function(t){this._collisionMesh&&this._collisionMesh.dispose(),!t.parent&&this.mesh&&t.setParent(this.mesh),this._collisionMesh=t,this._injectGUI3DMetadata(this._collisionMesh).control=this,this.collidableFrontDirection=t.forward,this._collidableInitialized=!0},enumerable:!1,configurable:!0}),e.prototype._getShortestDistancePointToLine=function(t,e,i){var r=e.subtract(t);return i.cross(r).length()},e.prototype._isPrimedForInteraction=function(t){var e=this._getHeightFromButtonCenter(t);return!(e>this._hoverOffset||e<this._pushThroughBackOffset)&&this._getShortestDistancePointToLine(this._collisionMesh.getAbsolutePosition(),t,this._collidableFrontDirection)<=this._collisionMesh.getBoundingInfo().boundingSphere.radiusWorld},e.prototype._getPointOnButton=function(t){var e=this._getHeightFromButtonCenter(t);if(e<=this._frontOffset&&e>=this._backOffset)return t;if(e>this._frontOffset){var i=this._frontOffset-e;return t.add(this._collidableFrontDirection.scale(i))}var r=this._backOffset-e;return t.add(this._collidableFrontDirection.scale(r))},e.prototype._updateDistanceOffsets=function(){if(!this._getWorldMatrixData(this._collisionMesh).scale.equalsWithEpsilon(this._lastKnownCollidableScale)){var t=this._collisionMesh.getAbsolutePosition();this._tempButtonForwardRay.origin=t,this._tempButtonForwardRay.direction=this._collidableFrontDirection;var e=this._tempButtonForwardRay.intersectsMesh(this._collisionMesh);this._tempButtonForwardRay.direction=this._tempButtonForwardRay.direction.negate();var i=this._tempButtonForwardRay.intersectsMesh(this._collisionMesh);this._frontOffset=0,this._backOffset=0,e.hit&&i.hit&&(this._frontOffset=this._getDistanceOffPlane(e.pickedPoint,this._collidableFrontDirection,t),this._backOffset=this._getDistanceOffPlane(i.pickedPoint,this._collidableFrontDirection,t));var r=this._frontOffset-this._backOffset;this._hoverOffset=this._frontOffset+1.25*r,this._pushThroughBackOffset=this._backOffset-1.5*r,this._lastKnownCollidableScale=this._getWorldMatrixData(this._collisionMesh).scale}},e.prototype._getHeightFromButtonCenter=function(t){return this._getDistanceOffPlane(t,this._collidableFrontDirection,this._collisionMesh.getAbsolutePosition())},e.prototype._getDistanceOffPlane=function(t,e,i){var r=s.Vector3.Dot(i,e);return s.Vector3.Dot(t,e)-r},e.prototype._updateButtonState=function(t,e,i){var r=this._activeInteractions.get(t)||dt.None;r!=e&&(e==dt.None?this._activeInteractions.delete(t):this._activeInteractions.set(t,e)),this._firePointerEvents(t,e,r,i)},e.prototype._firePointerEvents=function(t,e,i,r){e==dt.Press?i==dt.Hover?this._onPointerDown(this,r,t,0):i==dt.Press&&this._onPointerMove(this,r):e==dt.Hover?i==dt.None?this._onPointerEnter(this):i==dt.Press?this._onPointerUp(this,r,t,0,!1):this._onPointerMove(this,r):e==dt.None&&(i==dt.Hover?this._onPointerOut(this):i==dt.Press&&(this._onPointerUp(this,r,t,0,!1),this._onPointerOut(this)))},e.prototype._collisionCheckForStateChange=function(t,e,i){if(void 0===i&&(i=!1),this._collidableInitialized){this._updateDistanceOffsets();var r=this._isPrimedForInteraction(t),o=this._activeInteractions.get(e);if(r&&!i){var n=this._getPointOnButton(t),s=this._getHeightFromButtonCenter(t);this._lastTouchPoint=n;var a=function(t){return s>=t+.003},h=function(t){return s<=t-.003};switch(o||dt.None){case dt.None:a(this._frontOffset)&&h(this._hoverOffset)&&this._updateButtonState(e,dt.Hover,n);break;case dt.Hover:a(this._hoverOffset)?this._updateButtonState(e,dt.None,n):h(this._frontOffset)?this._updateButtonState(e,dt.Press,n):this._updateButtonState(e,dt.Hover,n);break;case dt.Press:a(this._frontOffset)?this._updateButtonState(e,dt.Hover,n):h(this._pushThroughBackOffset)?this._updateButtonState(e,dt.None,n):this._updateButtonState(e,dt.Press,n)}this._previousHeight.set(e,s)}else null!=o&&o!=dt.None&&(this._updateButtonState(e,dt.None,this._lastTouchPoint),this._previousHeight.delete(e))}},e.prototype._getTypeName=function(){return"TouchButton3D"},e.prototype._createNode=function(e){return t.prototype._createNode.call(this,e)},e.prototype.dispose=function(){t.prototype.dispose.call(this),this._collisionMesh&&this._collisionMesh.dispose()},e}(nt),Ot=function(t){function e(e,i,r){var o=this;if(i.useDynamicMesh)o=t.call(this,r,i.collisionMesh)||this;else{var n=i.collisionMesh.clone("",i.collisionMesh.parent);n.isVisible=!1,o=t.call(this,r,n)||this}return o._currentMesh=e,o.pointerEnterAnimation=function(){o.mesh&&o.mesh.scaling.scaleInPlace(1.1)},o.pointerOutAnimation=function(){o.mesh&&o.mesh.scaling.scaleInPlace(1/1.1)},o.pointerDownAnimation=function(){o.mesh&&o.mesh.scaling.scaleInPlace(.95)},o.pointerUpAnimation=function(){o.mesh&&o.mesh.scaling.scaleInPlace(1/.95)},o}return o(e,t),e.prototype._getTypeName=function(){return"TouchMeshButton3D"},e.prototype._createNode=function(t){var e=this;return this._currentMesh.getChildMeshes().forEach((function(t){e._injectGUI3DMetadata(t).control=e})),this._currentMesh},e.prototype._affectMaterial=function(t){},e}(vt),Ct="uniform vec3 cameraPosition;\nvarying vec3 vPosition;\nvarying vec3 vNormal;\nvarying vec2 vUV;\nvarying vec3 vTangent;\nvarying vec3 vBinormal;\nvarying vec4 vColor;\nvarying vec4 vExtra1;\nuniform float _Edge_Width_;\nuniform vec4 _Edge_Color_;\nuniform bool _Relative_Width_;\nuniform float _Proximity_Max_Intensity_;\nuniform float _Proximity_Far_Distance_;\nuniform float _Proximity_Near_Radius_;\nuniform float _Proximity_Anisotropy_;\nuniform float _Selection_Fuzz_;\nuniform float _Selected_;\nuniform float _Selection_Fade_;\nuniform float _Selection_Fade_Size_;\nuniform float _Selected_Distance_;\nuniform float _Selected_Fade_Length_;\nuniform bool _Blob_Enable_;\nuniform vec3 _Blob_Position_;\nuniform float _Blob_Intensity_;\nuniform float _Blob_Near_Size_;\nuniform float _Blob_Far_Size_;\nuniform float _Blob_Near_Distance_;\nuniform float _Blob_Far_Distance_;\nuniform float _Blob_Fade_Length_;\nuniform float _Blob_Inner_Fade_;\nuniform float _Blob_Pulse_;\nuniform float _Blob_Fade_;\nuniform sampler2D _Blob_Texture_;\nuniform bool _Blob_Enable_2_;\nuniform vec3 _Blob_Position_2_;\nuniform float _Blob_Near_Size_2_;\nuniform float _Blob_Inner_Fade_2_;\nuniform float _Blob_Pulse_2_;\nuniform float _Blob_Fade_2_;\nuniform vec3 _Active_Face_Dir_;\nuniform vec3 _Active_Face_Up_;\nuniform bool Enable_Fade;\nuniform float _Fade_Width_;\nuniform bool _Smooth_Active_Face_;\nuniform bool _Show_Frame_;\nuniform bool _Use_Blob_Texture_;\nuniform bool Use_Global_Left_Index;\nuniform bool Use_Global_Right_Index;\nuniform vec4 Global_Left_Index_Tip_Position;\nuniform vec4 Global_Right_Index_Tip_Position;\nuniform vec4 Global_Left_Thumb_Tip_Position;\nuniform vec4 Global_Right_Thumb_Tip_Position;\nuniform float Global_Left_Index_Tip_Proximity;\nuniform float Global_Right_Index_Tip_Proximity;\n\nvoid Holo_Edge_Fragment_B35(\nvec4 Edges,\nfloat Edge_Width,\nout float NotEdge)\n{\nvec2 c=vec2(min(Edges.r,Edges.g),min(Edges.b,Edges.a));\nvec2 df=fwidth(c)*Edge_Width;\nvec2 g=clamp(c/df,0.0,1.0);\nNotEdge=g.x*g.y;\n}\n\n\nvoid Blob_Fragment_B39(\nvec2 UV,\nvec3 Blob_Info,\nsampler2D Blob_Texture,\nout vec4 Blob_Color)\n{\nfloat k=dot(UV,UV);\nBlob_Color=Blob_Info.y*texture(Blob_Texture,vec2(vec2(sqrt(k),Blob_Info.x).x,1.0-vec2(sqrt(k),Blob_Info.x).y))*(1.0-clamp(k,0.0,1.0));\n}\n\n\nvec2 FilterStep(vec2 Edge,vec2 X)\n{\n\nvec2 dX=max(fwidth(X),vec2(0.00001,0.00001));\nreturn clamp( (X+dX-max(Edge,X-dX))/(dX*2.0),0.0,1.0);\n}\nvoid Wireframe_Fragment_B59(\nvec3 Widths,\nvec2 UV,\nfloat Proximity,\nvec4 Edge_Color,\nout vec4 Wireframe)\n{\nvec2 c=min(UV,vec2(1.0,1.0)-UV);\nvec2 g=FilterStep(Widths.xy*0.5,c);\nWireframe=(1.0-min(g.x,g.y))*Proximity*Edge_Color;\n}\n\n\nvoid Proximity_B53(\nvec3 Proximity_Center,\nvec3 Proximity_Center_2,\nfloat Proximity_Max_Intensity,\nfloat Proximity_Near_Radius,\nvec3 Position,\nvec3 Show_Selection,\nvec4 Extra1,\nfloat Dist_To_Face,\nfloat Intensity,\nout float Proximity)\n{\nvec2 delta1=Extra1.xy;\nvec2 delta2=Extra1.zw;\nfloat d2=sqrt(min(dot(delta1,delta1),dot(delta2,delta2))+Dist_To_Face*Dist_To_Face);\n\nProximity=Intensity*Proximity_Max_Intensity*(1.0-clamp(d2/Proximity_Near_Radius,0.0,1.0))*(1.0-Show_Selection.x)+Show_Selection.x;\n}\n\n\nvoid To_XYZ_B46(\nvec3 Vec3,\nout float X,\nout float Y,\nout float Z)\n{\nX=Vec3.x;\nY=Vec3.y;\nZ=Vec3.z;\n}\n\nvoid main()\n{\nfloat NotEdge_Q35;\n#if ENABLE_FADE\nHolo_Edge_Fragment_B35(vColor,_Fade_Width_,NotEdge_Q35);\n#else\nNotEdge_Q35=1.0;\n#endif\n\n\nvec4 Blob_Color_Q39;\nfloat k=dot(vUV,vUV);\nvec2 blobTextureCoord=vec2(vec2(sqrt(k),vTangent.x).x,1.0-vec2(sqrt(k),vTangent.x).y);\nvec4 blobColor=mix(vec4(1.0,1.0,1.0,1.0)*step(1.0-vTangent.x,clamp(sqrt(k)+0.1,0.0,1.0)),texture(_Blob_Texture_,blobTextureCoord),float(_Use_Blob_Texture_));\nBlob_Color_Q39=vTangent.y*blobColor*(1.0-clamp(k,0.0,1.0));\n\nfloat Is_Quad_Q24;\nIs_Quad_Q24=vNormal.z;\n\nvec3 Blob_Position_Q41=mix(_Blob_Position_,Global_Left_Index_Tip_Position.xyz,float(Use_Global_Left_Index));\n\nvec3 Blob_Position_Q42=mix(_Blob_Position_2_,Global_Right_Index_Tip_Position.xyz,float(Use_Global_Right_Index));\nfloat X_Q46;\nfloat Y_Q46;\nfloat Z_Q46;\nTo_XYZ_B46(vBinormal,X_Q46,Y_Q46,Z_Q46);\nfloat Proximity_Q53;\nProximity_B53(Blob_Position_Q41,Blob_Position_Q42,_Proximity_Max_Intensity_,_Proximity_Near_Radius_,vPosition,vBinormal,vExtra1,Y_Q46,Z_Q46,Proximity_Q53);\nvec4 Wireframe_Q59;\nWireframe_Fragment_B59(vNormal,vUV,Proximity_Q53,_Edge_Color_,Wireframe_Q59);\n\nvec4 Wire_Or_Blob_Q23=mix(Wireframe_Q59,Blob_Color_Q39,Is_Quad_Q24);\n\nvec4 Result_Q22;\nResult_Q22=mix(Wire_Or_Blob_Q23,vec4(0.3,0.3,0.3,0.3),float(_Show_Frame_));\n\nvec4 Final_Color_Q37=NotEdge_Q35*Result_Q22;\nvec4 Out_Color=Final_Color_Q37;\nfloat Clip_Threshold=0.0;\nbool To_sRGB=false;\ngl_FragColor=Out_Color;\n}";s.Effect.ShadersStore.fluentButtonPixelShader=Ct;var xt="uniform mat4 world;\nuniform mat4 viewProjection;\nuniform vec3 cameraPosition;\nattribute vec3 position;\nattribute vec3 normal;\nattribute vec2 uv;\nattribute vec3 tangent;\nattribute vec4 color;\nuniform float _Edge_Width_;\nuniform vec4 _Edge_Color_;\nuniform float _Proximity_Max_Intensity_;\nuniform float _Proximity_Far_Distance_;\nuniform float _Proximity_Near_Radius_;\nuniform float _Proximity_Anisotropy_;\nuniform float _Selection_Fuzz_;\nuniform float _Selected_;\nuniform float _Selection_Fade_;\nuniform float _Selection_Fade_Size_;\nuniform float _Selected_Distance_;\nuniform float _Selected_Fade_Length_;\nuniform bool _Blob_Enable_;\nuniform vec3 _Blob_Position_;\nuniform float _Blob_Intensity_;\nuniform float _Blob_Near_Size_;\nuniform float _Blob_Far_Size_;\nuniform float _Blob_Near_Distance_;\nuniform float _Blob_Far_Distance_;\nuniform float _Blob_Fade_Length_;\nuniform float _Blob_Inner_Fade_;\nuniform float _Blob_Pulse_;\nuniform float _Blob_Fade_;\nuniform sampler2D _Blob_Texture_;\nuniform bool _Blob_Enable_2_;\nuniform vec3 _Blob_Position_2_;\nuniform float _Blob_Near_Size_2_;\nuniform float _Blob_Inner_Fade_2_;\nuniform float _Blob_Pulse_2_;\nuniform float _Blob_Fade_2_;\nuniform vec3 _Active_Face_Dir_;\nuniform vec3 _Active_Face_Up_;\nuniform bool _Enable_Fade_;\nuniform float _Fade_Width_;\nuniform bool _Smooth_Active_Face_;\nuniform bool _Show_Frame_;\nuniform bool Use_Global_Left_Index;\nuniform bool Use_Global_Right_Index;\nuniform vec4 Global_Left_Index_Tip_Position;\nuniform vec4 Global_Right_Index_Tip_Position;\nuniform vec4 Global_Left_Thumb_Tip_Position;\nuniform vec4 Global_Right_Thumb_Tip_Position;\nuniform float Global_Left_Index_Tip_Proximity;\nuniform float Global_Right_Index_Tip_Proximity;\nvarying vec3 vPosition;\nvarying vec3 vNormal;\nvarying vec2 vUV;\nvarying vec3 vTangent;\nvarying vec3 vBinormal;\nvarying vec4 vColor;\nvarying vec4 vExtra1;\n\nvoid Blob_Vertex_B47(\nvec3 Position,\nvec3 Normal,\nvec3 Tangent,\nvec3 Bitangent,\nvec3 Blob_Position,\nfloat Intensity,\nfloat Blob_Near_Size,\nfloat Blob_Far_Size,\nfloat Blob_Near_Distance,\nfloat Blob_Far_Distance,\nvec4 Vx_Color,\nvec2 UV,\nvec3 Face_Center,\nvec2 Face_Size,\nvec2 In_UV,\nfloat Blob_Fade_Length,\nfloat Selection_Fade,\nfloat Selection_Fade_Size,\nfloat Inner_Fade,\nvec3 Active_Face_Center,\nfloat Blob_Pulse,\nfloat Blob_Fade,\nfloat Blob_Enabled,\nout vec3 Out_Position,\nout vec2 Out_UV,\nout vec3 Blob_Info)\n{\nfloat blobSize,fadeIn;\nvec3 Hit_Position;\nBlob_Info=vec3(0.0,0.0,0.0);\nfloat Hit_Distance=dot(Blob_Position-Face_Center,Normal);\nHit_Position=Blob_Position-Hit_Distance*Normal;\nfloat absD=abs(Hit_Distance);\nfloat lerpVal=clamp((absD-Blob_Near_Distance)/(Blob_Far_Distance-Blob_Near_Distance),0.0,1.0);\nfadeIn=1.0-clamp((absD-Blob_Far_Distance)/Blob_Fade_Length,0.0,1.0);\nfloat innerFade=1.0-clamp(-Hit_Distance/Inner_Fade,0.0,1.0);\n\nfloat farClip=clamp(1.0-step(Blob_Far_Distance+Blob_Fade_Length,absD),0.0,1.0);\nfloat size=mix(Blob_Near_Size,Blob_Far_Size,lerpVal)*farClip;\nblobSize=mix(size,Selection_Fade_Size,Selection_Fade)*innerFade*Blob_Enabled;\nBlob_Info.x=lerpVal*0.5+0.5;\nBlob_Info.y=fadeIn*Intensity*(1.0-Selection_Fade)*Blob_Fade;\nBlob_Info.x*=(1.0-Blob_Pulse);\n\nvec3 delta=Hit_Position-Face_Center;\nvec2 blobCenterXY=vec2(dot(delta,Tangent),dot(delta,Bitangent));\nvec2 quadUVin=2.0*UV-1.0;\nvec2 blobXY=blobCenterXY+quadUVin*blobSize;\n\nvec2 blobClipped=clamp(blobXY,-Face_Size*0.5,Face_Size*0.5);\nvec2 blobUV=(blobClipped-blobCenterXY)/max(blobSize,0.0001)*2.0;\nvec3 blobCorner=Face_Center+blobClipped.x*Tangent+blobClipped.y*Bitangent;\n\nOut_Position=mix(Position,blobCorner,Vx_Color.rrr);\nOut_UV=mix(In_UV,blobUV,Vx_Color.rr);\n}\n\n\nvec2 ProjectProximity(\nvec3 blobPosition,\nvec3 position,\nvec3 center,\nvec3 dir,\nvec3 xdir,\nvec3 ydir,\nout float vdistance\n)\n{\nvec3 delta=blobPosition-position;\nvec2 xy=vec2(dot(delta,xdir),dot(delta,ydir));\nvdistance=abs(dot(delta,dir));\nreturn xy;\n}\nvoid Proximity_Vertex_B66(\nvec3 Blob_Position,\nvec3 Blob_Position_2,\nvec3 Active_Face_Center,\nvec3 Active_Face_Dir,\nvec3 Position,\nfloat Proximity_Far_Distance,\nfloat Relative_Scale,\nfloat Proximity_Anisotropy,\nvec3 Up,\nout vec4 Extra1,\nout float Distance_To_Face,\nout float Intensity)\n{\nvec3 Active_Face_Dir_X=normalize(cross(Active_Face_Dir,Up));\n\nvec3 Active_Face_Dir_Y=cross(Active_Face_Dir,Active_Face_Dir_X);\nfloat distz1,distz2;\nExtra1.xy=ProjectProximity(Blob_Position,Position,Active_Face_Center,Active_Face_Dir,Active_Face_Dir_X*Proximity_Anisotropy,Active_Face_Dir_Y,distz1)/Relative_Scale;\nExtra1.zw=ProjectProximity(Blob_Position_2,Position,Active_Face_Center,Active_Face_Dir,Active_Face_Dir_X*Proximity_Anisotropy,Active_Face_Dir_Y,distz2)/Relative_Scale;\nDistance_To_Face=dot(Active_Face_Dir,Position-Active_Face_Center);\nIntensity=1.0-clamp(min(distz1,distz2)/Proximity_Far_Distance,0.0,1.0);\n}\n\n\nvoid Holo_Edge_Vertex_B44(\nvec3 Incident,\nvec3 Normal,\nvec2 UV,\nvec3 Tangent,\nvec3 Bitangent,\nbool Smooth_Active_Face,\nfloat Active,\nout vec4 Holo_Edges)\n{\nfloat NdotI=dot(Incident,Normal);\nvec2 flip=(UV-vec2(0.5,0.5));\nfloat udot=dot(Incident,Tangent)*flip.x*NdotI;\nfloat uval=1.0-float(udot>0.0);\nfloat vdot=-dot(Incident,Bitangent)*flip.y*NdotI;\nfloat vval=1.0-float(vdot>0.0);\nfloat Smooth_And_Active=step(1.0,float(Smooth_Active_Face && Active>0.0));\nuval=mix(uval,max(1.0,uval),Smooth_And_Active);\nvval=mix(vval,max(1.0,vval),Smooth_And_Active);\nHolo_Edges=vec4(1.0,1.0,1.0,1.0)-vec4(uval*UV.x,uval*(1.0-UV.x),vval*UV.y,vval*(1.0-UV.y));\n}\n\n\nvoid Object_To_World_Pos_B13(\nvec3 Pos_Object,\nout vec3 Pos_World)\n{\nPos_World=(world*vec4(Pos_Object,1.0)).xyz;\n}\n\n\nvoid Choose_Blob_B38(\nvec4 Vx_Color,\nvec3 Position1,\nvec3 Position2,\nbool Blob_Enable_1,\nbool Blob_Enable_2,\nfloat Near_Size_1,\nfloat Near_Size_2,\nfloat Blob_Inner_Fade_1,\nfloat Blob_Inner_Fade_2,\nfloat Blob_Pulse_1,\nfloat Blob_Pulse_2,\nfloat Blob_Fade_1,\nfloat Blob_Fade_2,\nout vec3 Position,\nout float Near_Size,\nout float Inner_Fade,\nout float Blob_Enable,\nout float Fade,\nout float Pulse)\n{\nPosition=Position1*(1.0-Vx_Color.g)+Vx_Color.g*Position2;\nfloat b1=float(Blob_Enable_1);\nfloat b2=float(Blob_Enable_2);\nBlob_Enable=b1+(b2-b1)*Vx_Color.g;\nPulse=Blob_Pulse_1*(1.0-Vx_Color.g)+Vx_Color.g*Blob_Pulse_2;\nFade=Blob_Fade_1*(1.0-Vx_Color.g)+Vx_Color.g*Blob_Fade_2;\nNear_Size=Near_Size_1*(1.0-Vx_Color.g)+Vx_Color.g*Near_Size_2;\nInner_Fade=Blob_Inner_Fade_1*(1.0-Vx_Color.g)+Vx_Color.g*Blob_Inner_Fade_2;\n}\n\n\nvoid Wireframe_Vertex_B51(\nvec3 Position,\nvec3 Normal,\nvec3 Tangent,\nvec3 Bitangent,\nfloat Edge_Width,\nvec2 Face_Size,\nout vec3 Wire_Vx_Pos,\nout vec2 UV,\nout vec2 Widths)\n{\nWidths.xy=Edge_Width/Face_Size;\nfloat x=dot(Position,Tangent);\nfloat y=dot(Position,Bitangent);\nfloat dx=0.5-abs(x);\nfloat newx=(0.5-dx*Widths.x*2.0)*sign(x);\nfloat dy=0.5-abs(y);\nfloat newy=(0.5-dy*Widths.y*2.0)*sign(y);\nWire_Vx_Pos=Normal*0.5+newx*Tangent+newy*Bitangent;\nUV.x=dot(Wire_Vx_Pos,Tangent)+0.5;\nUV.y=dot(Wire_Vx_Pos,Bitangent)+0.5;\n}\n\n\nvec2 ramp2(vec2 start,vec2 end,vec2 x)\n{\nreturn clamp((x-start)/(end-start),vec2(0.0,0.0),vec2(1.0,1.0));\n}\nfloat computeSelection(\nvec3 blobPosition,\nvec3 normal,\nvec3 tangent,\nvec3 bitangent,\nvec3 faceCenter,\nvec2 faceSize,\nfloat selectionFuzz,\nfloat farDistance,\nfloat fadeLength\n)\n{\nvec3 delta=blobPosition-faceCenter;\nfloat absD=abs(dot(delta,normal));\nfloat fadeIn=1.0-clamp((absD-farDistance)/fadeLength,0.0,1.0);\nvec2 blobCenterXY=vec2(dot(delta,tangent),dot(delta,bitangent));\nvec2 innerFace=faceSize*(1.0-selectionFuzz)*0.5;\nvec2 selectPulse=ramp2(-faceSize*0.5,-innerFace,blobCenterXY)-ramp2(innerFace,faceSize*0.5,blobCenterXY);\nreturn selectPulse.x*selectPulse.y*fadeIn;\n}\nvoid Selection_Vertex_B48(\nvec3 Blob_Position,\nvec3 Blob_Position_2,\nvec3 Face_Center,\nvec2 Face_Size,\nvec3 Normal,\nvec3 Tangent,\nvec3 Bitangent,\nfloat Selection_Fuzz,\nfloat Selected,\nfloat Far_Distance,\nfloat Fade_Length,\nvec3 Active_Face_Dir,\nout float Show_Selection)\n{\nfloat select1=computeSelection(Blob_Position,Normal,Tangent,Bitangent,Face_Center,Face_Size,Selection_Fuzz,Far_Distance,Fade_Length);\nfloat select2=computeSelection(Blob_Position_2,Normal,Tangent,Bitangent,Face_Center,Face_Size,Selection_Fuzz,Far_Distance,Fade_Length);\nfloat Active=max(0.0,dot(Active_Face_Dir,Normal));\nShow_Selection=mix(max(select1,select2),1.0,Selected)*Active;\n}\n\n\nvoid Proximity_Visibility_B54(\nfloat Selection,\nvec3 Proximity_Center,\nvec3 Proximity_Center_2,\nfloat Input_Width,\nfloat Proximity_Far_Distance,\nfloat Proximity_Radius,\nvec3 Active_Face_Center,\nvec3 Active_Face_Dir,\nout float Width)\n{\n\nvec3 boxEdges=(world*vec4(vec3(0.5,0.5,0.5),0.0)).xyz;\nfloat boxMaxSize=length(boxEdges);\nfloat d1=dot(Proximity_Center-Active_Face_Center,Active_Face_Dir);\nvec3 blob1=Proximity_Center-d1*Active_Face_Dir;\nfloat d2=dot(Proximity_Center_2-Active_Face_Center,Active_Face_Dir);\nvec3 blob2=Proximity_Center_2-d2*Active_Face_Dir;\n\nvec3 delta1=blob1-Active_Face_Center;\nvec3 delta2=blob2-Active_Face_Center;\nfloat dist1=dot(delta1,delta1);\nfloat dist2=dot(delta2,delta2);\nfloat nearestProxDist=sqrt(min(dist1,dist2));\n\nWidth=Input_Width*(1.0-step(boxMaxSize+Proximity_Radius,nearestProxDist))*(1.0-step(Proximity_Far_Distance,min(d1,d2))*(1.0-step(0.0001,Selection)));\n}\n\n\nvoid Object_To_World_Dir_B67(\nvec3 Dir_Object,\nout vec3 Dir_World)\n{\nDir_World=(world*vec4(Dir_Object,0.0)).xyz;\n}\n\nvoid main()\n{\n\nvec3 Active_Face_Center_Q49;\nActive_Face_Center_Q49=(world*vec4(_Active_Face_Dir_*0.5,1.0)).xyz;\n\nvec3 Blob_Position_Q41=mix(_Blob_Position_,Global_Left_Index_Tip_Position.xyz,float(Use_Global_Left_Index));\n\nvec3 Blob_Position_Q42=mix(_Blob_Position_2_,Global_Right_Index_Tip_Position.xyz,float(Use_Global_Right_Index));\n\nvec3 Active_Face_Dir_Q64=normalize((world*vec4(_Active_Face_Dir_,0.0)).xyz);\n\nfloat Relative_Scale_Q57;\n#if RELATIVE_WIDTH\nRelative_Scale_Q57=length((world*vec4(vec3(0,1,0),0.0)).xyz);\n#else\nRelative_Scale_Q57=1.0;\n#endif\n\nvec3 Tangent_World_Q30;\nTangent_World_Q30=(world*vec4(tangent,0.0)).xyz;\n\nvec3 Binormal_World_Q31;\nBinormal_World_Q31=(world*vec4((cross(normal,tangent)),0.0)).xyz;\n\nvec3 Normal_World_Q60;\nNormal_World_Q60=(world*vec4(normal,0.0)).xyz;\n\nvec3 Result_Q18=0.5*normal;\nvec3 Dir_World_Q67;\nObject_To_World_Dir_B67(_Active_Face_Up_,Dir_World_Q67);\n\nfloat Product_Q56=_Edge_Width_*Relative_Scale_Q57;\n\nvec3 Normal_World_N_Q29=normalize(Normal_World_Q60);\n\nvec3 Tangent_World_N_Q28=normalize(Tangent_World_Q30);\n\nvec3 Binormal_World_N_Q32=normalize(Binormal_World_Q31);\nvec3 Position_Q38;\nfloat Near_Size_Q38;\nfloat Inner_Fade_Q38;\nfloat Blob_Enable_Q38;\nfloat Fade_Q38;\nfloat Pulse_Q38;\nChoose_Blob_B38(color,Blob_Position_Q41,Blob_Position_Q42,_Blob_Enable_,_Blob_Enable_2_,_Blob_Near_Size_,_Blob_Near_Size_2_,_Blob_Inner_Fade_,_Blob_Inner_Fade_2_,_Blob_Pulse_,_Blob_Pulse_2_,_Blob_Fade_,_Blob_Fade_2_,Position_Q38,Near_Size_Q38,Inner_Fade_Q38,Blob_Enable_Q38,Fade_Q38,Pulse_Q38);\n\nvec3 Face_Center_Q33;\nFace_Center_Q33=(world*vec4(Result_Q18,1.0)).xyz;\n\nvec2 Face_Size_Q50=vec2(length(Tangent_World_Q30),length(Binormal_World_Q31));\nfloat Show_Selection_Q48;\nSelection_Vertex_B48(Blob_Position_Q41,Blob_Position_Q42,Face_Center_Q33,Face_Size_Q50,Normal_World_N_Q29,Tangent_World_N_Q28,Binormal_World_N_Q32,_Selection_Fuzz_,_Selected_,_Selected_Distance_,_Selected_Fade_Length_,Active_Face_Dir_Q64,Show_Selection_Q48);\n\nvec3 Normalized_Q72=normalize(Dir_World_Q67);\n\nfloat Active_Q34=max(0.0,dot(Active_Face_Dir_Q64,Normal_World_N_Q29));\nfloat Width_Q54;\nProximity_Visibility_B54(Show_Selection_Q48,Blob_Position_Q41,Blob_Position_Q42,Product_Q56,_Proximity_Far_Distance_,_Proximity_Near_Radius_,Active_Face_Center_Q49,Active_Face_Dir_Q64,Width_Q54);\nvec3 Wire_Vx_Pos_Q51;\nvec2 UV_Q51;\nvec2 Widths_Q51;\nWireframe_Vertex_B51(position,normal,tangent,(cross(normal,tangent)),Width_Q54,Face_Size_Q50,Wire_Vx_Pos_Q51,UV_Q51,Widths_Q51);\n\nvec3 Vec3_Q27=vec3(Widths_Q51.x,Widths_Q51.y,color.r);\nvec3 Pos_World_Q13;\nObject_To_World_Pos_B13(Wire_Vx_Pos_Q51,Pos_World_Q13);\n\nvec3 Incident_Q36=normalize(Pos_World_Q13-cameraPosition);\nvec3 Out_Position_Q47;\nvec2 Out_UV_Q47;\nvec3 Blob_Info_Q47;\nBlob_Vertex_B47(Pos_World_Q13,Normal_World_N_Q29,Tangent_World_N_Q28,Binormal_World_N_Q32,Position_Q38,_Blob_Intensity_,Near_Size_Q38,_Blob_Far_Size_,_Blob_Near_Distance_,_Blob_Far_Distance_,color,uv,Face_Center_Q33,Face_Size_Q50,UV_Q51,_Blob_Fade_Length_,_Selection_Fade_,_Selection_Fade_Size_,Inner_Fade_Q38,Active_Face_Center_Q49,Pulse_Q38,Fade_Q38,Blob_Enable_Q38,Out_Position_Q47,Out_UV_Q47,Blob_Info_Q47);\nvec4 Extra1_Q66;\nfloat Distance_To_Face_Q66;\nfloat Intensity_Q66;\nProximity_Vertex_B66(Blob_Position_Q41,Blob_Position_Q42,Active_Face_Center_Q49,Active_Face_Dir_Q64,Pos_World_Q13,_Proximity_Far_Distance_,Relative_Scale_Q57,_Proximity_Anisotropy_,Normalized_Q72,Extra1_Q66,Distance_To_Face_Q66,Intensity_Q66);\nvec4 Holo_Edges_Q44;\nHolo_Edge_Vertex_B44(Incident_Q36,Normal_World_N_Q29,uv,Tangent_World_Q30,Binormal_World_Q31,_Smooth_Active_Face_,Active_Q34,Holo_Edges_Q44);\n\nvec3 Vec3_Q19=vec3(Show_Selection_Q48,Distance_To_Face_Q66,Intensity_Q66);\nvec3 Position=Out_Position_Q47;\nvec2 UV=Out_UV_Q47;\nvec3 Tangent=Blob_Info_Q47;\nvec3 Binormal=Vec3_Q19;\nvec3 Normal=Vec3_Q27;\nvec4 Extra1=Extra1_Q66;\nvec4 Color=Holo_Edges_Q44;\ngl_Position=viewProjection*vec4(Position,1);\nvPosition=Position;\nvNormal=Normal;\nvUV=UV;\nvTangent=Tangent;\nvBinormal=Binormal;\nvColor=Color;\nvExtra1=Extra1;\n}";s.Effect.ShadersStore.fluentButtonVertexShader=xt;var Pt=function(t){function e(){var e=t.call(this)||this;return e.RELATIVE_WIDTH=!0,e.ENABLE_FADE=!0,e._needNormals=!0,e._needUVs=!0,e.rebuild(),e}return o(e,t),e}(s.MaterialDefines),Tt=function(t){function e(i,r){var o=t.call(this,i,r)||this;return o.edgeWidth=.04,o.edgeColor=new s.Color4(.592157,.592157,.592157,1),o.proximityMaxIntensity=.45,o.proximityFarDistance=.16,o.proximityNearRadius=1.5,o.proximityAnisotropy=1,o.selectionFuzz=.5,o.selected=0,o.selectionFade=0,o.selectionFadeSize=.3,o.selectedDistance=.08,o.selectedFadeLength=.08,o.blobIntensity=.5,o.blobFarSize=.05,o.blobNearDistance=0,o.blobFarDistance=.08,o.blobFadeLength=.08,o.leftBlobEnable=!0,o.leftBlobNearSize=.025,o.leftBlobPulse=0,o.leftBlobFade=1,o.leftBlobInnerFade=.01,o.rightBlobEnable=!0,o.rightBlobNearSize=.025,o.rightBlobPulse=0,o.rightBlobFade=1,o.rightBlobInnerFade=.01,o.activeFaceDir=new s.Vector3(0,0,-1),o.activeFaceUp=new s.Vector3(0,1,0),o.enableFade=!0,o.fadeWidth=1.5,o.smoothActiveFace=!0,o.showFrame=!1,o.useBlobTexture=!0,o.globalLeftIndexTipPosition=s.Vector3.Zero(),o.globalRightIndexTipPosition=s.Vector3.Zero(),o.alphaMode=s.Constants.ALPHA_ADD,o.disableDepthWrite=!0,o.backFaceCulling=!1,o._blobTexture=new s.Texture(e.BLOB_TEXTURE_URL,r,!0,!1,s.Texture.NEAREST_SAMPLINGMODE),o}return o(e,t),e.prototype.needAlphaBlending=function(){return!0},e.prototype.needAlphaTesting=function(){return!0},e.prototype.getAlphaTestTexture=function(){return null},e.prototype.isReadyForSubMesh=function(t,e,i){if(this.isFrozen&&e.effect&&e.effect._wasPreviouslyReady)return!0;e._materialDefines||(e.materialDefines=new Pt);var r=e._materialDefines,o=this.getScene();if(this._isReadyForSubMesh(e))return!0;var n=o.getEngine();if(s.MaterialHelper.PrepareDefinesForAttributes(t,r,!0,!1),r.isDirty){r.markAsProcessed(),o.resetCachedMaterial();var a=new s.EffectFallbacks;r.FOG&&a.addFallback(1,"FOG"),s.MaterialHelper.HandleFallbacksForShadows(r,a),r.IMAGEPROCESSINGPOSTPROCESS=o.imageProcessingConfiguration.applyByPostProcess;var h=[s.VertexBuffer.PositionKind];r.NORMAL&&h.push(s.VertexBuffer.NormalKind),r.UV1&&h.push(s.VertexBuffer.UVKind),r.UV2&&h.push(s.VertexBuffer.UV2Kind),r.VERTEXCOLOR&&h.push(s.VertexBuffer.ColorKind),r.TANGENT&&h.push(s.VertexBuffer.TangentKind),s.MaterialHelper.PrepareAttributesForInstances(h,r);var l=r.toString(),c=["world","viewProjection","cameraPosition","_Edge_Width_","_Edge_Color_","_Relative_Width_","_Proximity_Max_Intensity_","_Proximity_Far_Distance_","_Proximity_Near_Radius_","_Proximity_Anisotropy_","_Selection_Fuzz_","_Selected_","_Selection_Fade_","_Selection_Fade_Size_","_Selected_Distance_","_Selected_Fade_Length_","_Blob_Enable_","_Blob_Position_","_Blob_Intensity_","_Blob_Near_Size_","_Blob_Far_Size_","_Blob_Near_Distance_","_Blob_Far_Distance_","_Blob_Fade_Length_","_Blob_Inner_Fade_","_Blob_Pulse_","_Blob_Fade_","_Blob_Texture_","_Blob_Enable_2_","_Blob_Position_2_","_Blob_Near_Size_2_","_Blob_Inner_Fade_2_","_Blob_Pulse_2_","_Blob_Fade_2_","_Active_Face_Dir_","_Active_Face_Up_","_Enable_Fade_","_Fade_Width_","_Smooth_Active_Face_","_Show_Frame_","_Use_Blob_Texture_","Use_Global_Left_Index","Use_Global_Right_Index","Global_Left_Index_Tip_Position","Global_Right_Index_Tip_Position","Global_Left_Thumb_Tip_Position","Global_Right_Thumb_Tip_Position","Global_Left_Index_Tip_Proximity","Global_Right_Index_Tip_Proximity"],u=["_Blob_Texture_"],_=new Array;s.MaterialHelper.PrepareUniformsAndSamplersList({uniformsNames:c,uniformBuffersNames:_,samplers:u,defines:r,maxSimultaneousLights:4}),e.setEffect(o.getEngine().createEffect("fluentButton",{attributes:h,uniformsNames:c,uniformBuffersNames:_,samplers:u,defines:l,fallbacks:a,onCompiled:this.onCompiled,onError:this.onError,indexParameters:{maxSimultaneousLights:4}},n),r)}return!(!e.effect||!e.effect.isReady())&&(r._renderId=o.getRenderId(),e.effect._wasPreviouslyReady=!0,!0)},e.prototype.bindForSubMesh=function(t,e,i){var r=this.getScene();if(i._materialDefines){var o=i.effect;o&&(this._activeEffect=o,this.bindOnlyWorldMatrix(t),this._activeEffect.setMatrix("viewProjection",r.getTransformMatrix()),this._activeEffect.setVector3("cameraPosition",r.activeCamera.position),this._activeEffect.setTexture("_Blob_Texture_",this._blobTexture),this._activeEffect.setFloat("_Edge_Width_",this.edgeWidth),this._activeEffect.setColor4("_Edge_Color_",new s.Color3(this.edgeColor.r,this.edgeColor.g,this.edgeColor.b),this.edgeColor.a),this._activeEffect.setFloat("_Proximity_Max_Intensity_",this.proximityMaxIntensity),this._activeEffect.setFloat("_Proximity_Far_Distance_",this.proximityFarDistance),this._activeEffect.setFloat("_Proximity_Near_Radius_",this.proximityNearRadius),this._activeEffect.setFloat("_Proximity_Anisotropy_",this.proximityAnisotropy),this._activeEffect.setFloat("_Selection_Fuzz_",this.selectionFuzz),this._activeEffect.setFloat("_Selected_",this.selected),this._activeEffect.setFloat("_Selection_Fade_",this.selectionFade),this._activeEffect.setFloat("_Selection_Fade_Size_",this.selectionFadeSize),this._activeEffect.setFloat("_Selected_Distance_",this.selectedDistance),this._activeEffect.setFloat("_Selected_Fade_Length_",this.selectedFadeLength),this._activeEffect.setFloat("_Blob_Enable_",this.leftBlobEnable?1:0),this._activeEffect.setFloat("_Blob_Intensity_",this.blobIntensity),this._activeEffect.setFloat("_Blob_Near_Size_",this.leftBlobNearSize),this._activeEffect.setFloat("_Blob_Far_Size_",this.blobFarSize),this._activeEffect.setFloat("_Blob_Near_Distance_",this.blobNearDistance),this._activeEffect.setFloat("_Blob_Far_Distance_",this.blobFarDistance),this._activeEffect.setFloat("_Blob_Fade_Length_",this.blobFadeLength),this._activeEffect.setFloat("_Blob_Inner_Fade_",this.leftBlobInnerFade),this._activeEffect.setFloat("_Blob_Pulse_",this.leftBlobPulse),this._activeEffect.setFloat("_Blob_Fade_",this.leftBlobFade),this._activeEffect.setFloat("_Blob_Enable_2_",this.rightBlobEnable?1:0),this._activeEffect.setFloat("_Blob_Near_Size_2_",this.rightBlobNearSize),this._activeEffect.setFloat("_Blob_Inner_Fade_2_",this.rightBlobInnerFade),this._activeEffect.setFloat("_Blob_Pulse_2_",this.rightBlobPulse),this._activeEffect.setFloat("_Blob_Fade_2_",this.rightBlobFade),this._activeEffect.setVector3("_Active_Face_Dir_",this.activeFaceDir),this._activeEffect.setVector3("_Active_Face_Up_",this.activeFaceUp),this._activeEffect.setFloat("_Fade_Width_",this.fadeWidth),this._activeEffect.setFloat("_Smooth_Active_Face_",this.smoothActiveFace?1:0),this._activeEffect.setFloat("_Show_Frame_",this.showFrame?1:0),this._activeEffect.setFloat("_Use_Blob_Texture_",this.useBlobTexture?1:0),this._activeEffect.setFloat("Use_Global_Left_Index",1),this._activeEffect.setFloat("Use_Global_Right_Index",1),this._activeEffect.setVector4("Global_Left_Index_Tip_Position",new s.Vector4(this.globalLeftIndexTipPosition.x,this.globalLeftIndexTipPosition.y,this.globalLeftIndexTipPosition.z,1)),this._activeEffect.setVector4("Global_Right_Index_Tip_Position",new s.Vector4(this.globalRightIndexTipPosition.x,this.globalRightIndexTipPosition.y,this.globalRightIndexTipPosition.z,1)),this._afterBind(e,this._activeEffect))}},e.prototype.getAnimatables=function(){return[]},e.prototype.dispose=function(e){t.prototype.dispose.call(this,e)},e.prototype.clone=function(t){var i=this;return s.SerializationHelper.Clone((function(){return new e(t,i.getScene())}),this)},e.prototype.serialize=function(){var t=s.SerializationHelper.Serialize(this);return t.customType="BABYLON.FluentButtonMaterial",t},e.prototype.getClassName=function(){return"FluentButtonMaterial"},e.Parse=function(t,i,r){return s.SerializationHelper.Parse((function(){return new e(t.name,i)}),t,i,r)},e.BLOB_TEXTURE_URL="https://assets.babylonjs.com/meshes/MRTK/mrtk-fluent-button-blob.png",n([Object(s.serialize)()],e.prototype,"edgeWidth",void 0),n([Object(s.serializeAsColor4)()],e.prototype,"edgeColor",void 0),n([Object(s.serialize)()],e.prototype,"proximityMaxIntensity",void 0),n([Object(s.serialize)()],e.prototype,"proximityFarDistance",void 0),n([Object(s.serialize)()],e.prototype,"proximityNearRadius",void 0),n([Object(s.serialize)()],e.prototype,"proximityAnisotropy",void 0),n([Object(s.serialize)()],e.prototype,"selectionFuzz",void 0),n([Object(s.serialize)()],e.prototype,"selected",void 0),n([Object(s.serialize)()],e.prototype,"selectionFade",void 0),n([Object(s.serialize)()],e.prototype,"selectionFadeSize",void 0),n([Object(s.serialize)()],e.prototype,"selectedDistance",void 0),n([Object(s.serialize)()],e.prototype,"selectedFadeLength",void 0),n([Object(s.serialize)()],e.prototype,"blobIntensity",void 0),n([Object(s.serialize)()],e.prototype,"blobFarSize",void 0),n([Object(s.serialize)()],e.prototype,"blobNearDistance",void 0),n([Object(s.serialize)()],e.prototype,"blobFarDistance",void 0),n([Object(s.serialize)()],e.prototype,"blobFadeLength",void 0),n([Object(s.serialize)()],e.prototype,"leftBlobEnable",void 0),n([Object(s.serialize)()],e.prototype,"leftBlobNearSize",void 0),n([Object(s.serialize)()],e.prototype,"leftBlobPulse",void 0),n([Object(s.serialize)()],e.prototype,"leftBlobFade",void 0),n([Object(s.serialize)()],e.prototype,"leftBlobInnerFade",void 0),n([Object(s.serialize)()],e.prototype,"rightBlobEnable",void 0),n([Object(s.serialize)()],e.prototype,"rightBlobNearSize",void 0),n([Object(s.serialize)()],e.prototype,"rightBlobPulse",void 0),n([Object(s.serialize)()],e.prototype,"rightBlobFade",void 0),n([Object(s.serialize)()],e.prototype,"rightBlobInnerFade",void 0),n([Object(s.serializeAsVector3)()],e.prototype,"activeFaceDir",void 0),n([Object(s.serializeAsVector3)()],e.prototype,"activeFaceUp",void 0),n([Object(s.serialize)()],e.prototype,"enableFade",void 0),n([Object(s.serialize)()],e.prototype,"fadeWidth",void 0),n([Object(s.serialize)()],e.prototype,"smoothActiveFace",void 0),n([Object(s.serialize)()],e.prototype,"showFrame",void 0),n([Object(s.serialize)()],e.prototype,"useBlobTexture",void 0),n([Object(s.serializeAsVector3)()],e.prototype,"globalLeftIndexTipPosition",void 0),n([Object(s.serializeAsVector3)()],e.prototype,"globalRightIndexTipPosition",void 0),e}(s.PushMaterial);s._TypeStore.RegisteredTypes["BABYLON.GUI.FluentButtonMaterial"]=Tt;var wt=function(t){function e(e,i){void 0===i&&(i=!0);var r=t.call(this,e)||this;return r._shareMaterials=!0,r._shareMaterials=i,r.pointerEnterAnimation=function(){r._frontMaterial.leftBlobEnable=!0,r._frontMaterial.rightBlobEnable=!0},r.pointerOutAnimation=function(){r._frontMaterial.leftBlobEnable=!1,r._frontMaterial.rightBlobEnable=!1},r._pointerHoverObserver=r.onPointerMoveObservable.add((function(t){r._frontMaterial.globalLeftIndexTipPosition=t})),r}return o(e,t),e.prototype._disposeTooltip=function(){this._tooltipFade=null,this._tooltipTextBlock&&this._tooltipTextBlock.dispose(),this._tooltipTexture&&this._tooltipTexture.dispose(),this._tooltipMesh&&this._tooltipMesh.dispose(),this.onPointerEnterObservable.remove(this._tooltipHoverObserver),this.onPointerOutObservable.remove(this._tooltipOutObserver)},Object.defineProperty(e.prototype,"renderingGroupId",{get:function(){return this._backPlate.renderingGroupId},set:function(t){this._backPlate.renderingGroupId=t,this._textPlate.renderingGroupId=t,this._frontPlate.renderingGroupId=t,this._tooltipMesh&&(this._tooltipMesh.renderingGroupId=t)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"tooltipText",{get:function(){return this._tooltipTextBlock?this._tooltipTextBlock.text:null},set:function(t){var e=this;if(t){if(!this._tooltipFade){this._tooltipMesh=s.PlaneBuilder.CreatePlane("",{size:1},this._backPlate._scene);var i=s.PlaneBuilder.CreatePlane("",{size:1,sideOrientation:s.Mesh.DOUBLESIDE},this._backPlate._scene),r=new s.StandardMaterial("",this._backPlate._scene);r.diffuseColor=s.Color3.FromHexString("#212121"),i.material=r,i.isPickable=!1,this._tooltipMesh.addChild(i),i.position.z=.05,this._tooltipMesh.scaling.y=1/3,this._tooltipMesh.position.y=.7,this._tooltipMesh.position.z=-.15,this._tooltipMesh.isPickable=!1,this._tooltipMesh.parent=this._backPlate,this._tooltipTexture=$.CreateForMesh(this._tooltipMesh),this._tooltipTextBlock=new y,this._tooltipTextBlock.scaleY=3,this._tooltipTextBlock.color="white",this._tooltipTextBlock.fontSize=130,this._tooltipTexture.addControl(this._tooltipTextBlock),this._tooltipFade=new s.FadeInOutBehavior,this._tooltipFade.delay=500,this._tooltipMesh.addBehavior(this._tooltipFade),this._tooltipHoverObserver=this.onPointerEnterObservable.add((function(){e._tooltipFade&&e._tooltipFade.fadeIn(!0)})),this._tooltipOutObserver=this.onPointerOutObservable.add((function(){e._tooltipFade&&e._tooltipFade.fadeIn(!1)}))}this._tooltipTextBlock&&(this._tooltipTextBlock.text=t)}else this._disposeTooltip()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"text",{get:function(){return this._text},set:function(t){this._text!==t&&(this._text=t,this._rebuildContent())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"imageUrl",{get:function(){return this._imageUrl},set:function(t){this._imageUrl!==t&&(this._imageUrl=t,this._rebuildContent())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"backMaterial",{get:function(){return this._backMaterial},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"frontMaterial",{get:function(){return this._frontMaterial},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"plateMaterial",{get:function(){return this._plateMaterial},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"shareMaterials",{get:function(){return this._shareMaterials},enumerable:!1,configurable:!0}),e.prototype._getTypeName=function(){return"TouchHolographicButton"},e.prototype._rebuildContent=function(){this._disposeFacadeTexture();var t=new C;if(t.isVertical=!0,this._imageUrl){var e=new v;e.source=this._imageUrl,e.paddingTop="40px",e.height="180px",e.width="100px",e.paddingBottom="40px",t.addControl(e)}if(this._text){var i=new y;i.text=this._text,i.color="white",i.height="30px",i.fontSize=24,t.addControl(i)}document.createElement&&(this.content=t)},e.prototype._createNode=function(i){var r,o=this,n=s.BoxBuilder.CreateBox(null!==(r=this.name)&&void 0!==r?r:"TouchHolographicButton",{width:1,height:1,depth:1},i);n.isPickable=!0,n.isVisible=!1,n.scaling=new s.Vector3(.032,.032,.016),s.SceneLoader.ImportMeshAsync(void 0,e.MODEL_BASE_URL,e.MODEL_FILENAME,i).then((function(t){var e=t.meshes[1];e.name=o.name+"_frontPlate",e.isPickable=!1,e.parent=n,o._frontMaterial&&(e.material=o._frontMaterial),o._frontPlate=e}));return this._backPlate=s.BoxBuilder.CreateBox(this.name+"BackMesh",{width:1,height:1,depth:.04},i),this._backPlate.parent=n,this._backPlate.position.z=.48,this._backPlate.isPickable=!1,this._textPlate=t.prototype._createNode.call(this,i),this._textPlate.parent=n,this._textPlate.position.z=0,this._textPlate.isPickable=!1,this.collisionMesh=n,this.collidableFrontDirection=this._backPlate.forward.negate(),n},e.prototype._applyFacade=function(t){this._plateMaterial.emissiveTexture=t,this._plateMaterial.opacityTexture=t,this._plateMaterial.diffuseColor=new s.Color3(.4,.4,.4)},e.prototype._createBackMaterial=function(t){this._backMaterial=new s.StandardMaterial(this.name+"Back Material",t.getScene()),this._backMaterial.diffuseColor=new s.Color3(.5,.5,.5)},e.prototype._createFrontMaterial=function(t){this._frontMaterial=new Tt(this.name+"Front Material",t.getScene())},e.prototype._createPlateMaterial=function(t){this._plateMaterial=new s.StandardMaterial(this.name+"Plate Material",t.getScene()),this._plateMaterial.specularColor=s.Color3.Black()},e.prototype._affectMaterial=function(t){this._shareMaterials?(this._host._touchSharedMaterials.backFluentMaterial?this._backMaterial=this._host._touchSharedMaterials.backFluentMaterial:(this._createBackMaterial(t),this._host._touchSharedMaterials.backFluentMaterial=this._backMaterial),this._host._touchSharedMaterials.frontFluentMaterial?this._frontMaterial=this._host._touchSharedMaterials.frontFluentMaterial:(this._createFrontMaterial(t),this._host._touchSharedMaterials.frontFluentMaterial=this._frontMaterial)):(this._createBackMaterial(t),this._createFrontMaterial(t)),this._createPlateMaterial(t),this._backPlate.material=this._backMaterial,this._textPlate.material=this._plateMaterial,this._frontPlate&&(this._frontPlate.material=this._frontMaterial),this._rebuildContent()},e.prototype.dispose=function(){t.prototype.dispose.call(this),this._disposeTooltip(),this.onPointerMoveObservable.remove(this._pointerHoverObserver),this.shareMaterials||(this._backMaterial.dispose(),this._frontMaterial.dispose(),this._plateMaterial.dispose(),this._pickedPointObserver&&(this._host.onPickedPointChangedObservable.remove(this._pickedPointObserver),this._pickedPointObserver=null))},e.MODEL_BASE_URL="https://assets.babylonjs.com/meshes/MRTK/",e.MODEL_FILENAME="mrtk-fluent-button.glb",e}(vt),Mt=function(t){function e(e,i){var r=t.call(this,e,i)||this;return r._isPressed=!1,r.onToggleOnObservable=new s.Observable,r.onToggleOffObservable=new s.Observable,r.onPointerUpObservable.add((function(t){r._onToggle(t)})),r}return o(e,t),e.prototype._onToggle=function(t){this._isPressed=!this._isPressed,this._isPressed?this.onToggleOnObservable.notifyObservers(t):this.onToggleOffObservable.notifyObservers(t)},e.prototype._getTypeName=function(){return"TouchToggleButton3D"},e.prototype._createNode=function(e){return t.prototype._createNode.call(this,e)},e.prototype._affectMaterial=function(e){t.prototype._affectMaterial.call(this,e)},e.prototype.dispose=function(){this.onToggleOnObservable.clear(),this.onToggleOffObservable.clear(),t.prototype.dispose.call(this)},e}(vt),It=function(){function t(e){var i=this;this._touchableButtons=new Set,this._touchIds=new Map,this._lastControlOver={},this._lastControlDown={},this.onPickedPointChangedObservable=new s.Observable,this._sharedMaterials={},this._touchSharedMaterials={},this._processTouchControls=function(){var e=i._utilityLayer?i._utilityLayer.utilityLayerScene:null;if(e){var r=e.getMeshesByTags("touchEnabled");i._touchIds.forEach((function(t,e){var o=!1;r.forEach((function(t){t.name===e&&(o=!0)})),o||(i._touchableButtons.forEach((function(e){e._collisionCheckForStateChange(s.Vector3.Zero(),t,!0)})),i._touchIds.delete(e))})),r.forEach((function(e){var r=e.name;i._touchIds.has(r)||i._touchIds.set(r,t._touchIdCounter++)})),i._touchableButtons.forEach((function(t){r.forEach((function(e){var r=i._touchIds.get(e.name);t._collisionCheckForStateChange(e.getAbsolutePosition(),r)}))}))}},this._scene=e||s.EngineStore.LastCreatedScene,this._sceneDisposeObserver=this._scene.onDisposeObservable.add((function(){i._sceneDisposeObserver=null,i._utilityLayer=null,i.dispose()})),this._utilityLayer=new s.UtilityLayerRenderer(this._scene),this._utilityLayer.onlyCheckPointerDownEvents=!1,this._utilityLayer.pickUtilitySceneFirst=!1,this._utilityLayer.mainSceneTrackerPredicate=function(t){var e,i,r;return t&&(null===(r=null===(i=null===(e=t.metadata)||void 0===e?void 0:e.GUI3D)||void 0===i?void 0:i.control)||void 0===r?void 0:r._node)},this._rootContainer=new st("RootContainer"),this._rootContainer._host=this;var r=this._utilityLayer.utilityLayerScene;this._pointerOutObserver=this._utilityLayer.onPointerOutObservable.add((function(t){i._handlePointerOut(t,!0)})),this._pointerObserver=r.onPointerObservable.add((function(t,e){i._doPicking(t)})),this._utilityLayer.utilityLayerScene.autoClear=!1,this._utilityLayer.utilityLayerScene.autoClearDepthAndStencil=!1,new s.HemisphericLight("hemi",s.Vector3.Up(),this._utilityLayer.utilityLayerScene)}return Object.defineProperty(t.prototype,"scene",{get:function(){return this._scene},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"utilityLayer",{get:function(){return this._utilityLayer},enumerable:!1,configurable:!0}),t.prototype._handlePointerOut=function(t,e){var i=this._lastControlOver[t];i&&(i._onPointerOut(i),delete this._lastControlOver[t]),e&&this._lastControlDown[t]&&(this._lastControlDown[t].forcePointerUp(),delete this._lastControlDown[t]),this.onPickedPointChangedObservable.notifyObservers(null)},t.prototype._doPicking=function(t){var e,i;if(!this._utilityLayer||!this._utilityLayer.shouldRender||!this._utilityLayer.utilityLayerScene.activeCamera)return!1;var r=t.event,o=r.pointerId||0,n=r.button,a=t.pickInfo;if(!a||!a.hit)return this._handlePointerOut(o,t.type===s.PointerEventTypes.POINTERUP),!1;a.pickedPoint&&this.onPickedPointChangedObservable.notifyObservers(a.pickedPoint);var h=null===(i=null===(e=a.pickedMesh.metadata)||void 0===e?void 0:e.GUI3D)||void 0===i?void 0:i.control;return h&&!h._processObservables(t.type,a.pickedPoint,o,n)&&t.type===s.PointerEventTypes.POINTERMOVE&&(this._lastControlOver[o]&&this._lastControlOver[o]._onPointerOut(this._lastControlOver[o]),delete this._lastControlOver[o]),t.type===s.PointerEventTypes.POINTERUP&&(this._lastControlDown[r.pointerId]&&(this._lastControlDown[r.pointerId].forcePointerUp(),delete this._lastControlDown[r.pointerId]),"touch"===r.pointerType&&this._handlePointerOut(o,!1)),!0},Object.defineProperty(t.prototype,"rootContainer",{get:function(){return this._rootContainer},enumerable:!1,configurable:!0}),t.prototype.containsControl=function(t){return this._rootContainer.containsControl(t)},t.prototype.addControl=function(t){this._rootContainer.addControl(t);var e=this._utilityLayer?this._utilityLayer.utilityLayerScene:null;return e&&t instanceof vt&&(0==this._touchableButtons.size&&e.registerBeforeRender(this._processTouchControls),this._touchableButtons.add(t)),this},t.prototype.removeControl=function(t){this._rootContainer.removeControl(t);var e=this._utilityLayer?this._utilityLayer.utilityLayerScene:null;return e&&t instanceof vt&&(this._touchableButtons.delete(t),0==this._touchableButtons.size&&e.unregisterBeforeRender(this._processTouchControls)),this},t.prototype.dispose=function(){for(var t in this._rootContainer.dispose(),this._sharedMaterials)this._sharedMaterials.hasOwnProperty(t)&&this._sharedMaterials[t].dispose();for(var t in this._sharedMaterials={},this._touchSharedMaterials)this._touchSharedMaterials.hasOwnProperty(t)&&this._touchSharedMaterials[t].dispose();this._touchSharedMaterials={},this._pointerOutObserver&&this._utilityLayer&&(this._utilityLayer.onPointerOutObservable.remove(this._pointerOutObserver),this._pointerOutObserver=null),this.onPickedPointChangedObservable.clear();var e=this._utilityLayer?this._utilityLayer.utilityLayerScene:null;e&&(0!=this._touchableButtons.size&&e.unregisterBeforeRender(this._processTouchControls),this._pointerObserver&&(e.onPointerObservable.remove(this._pointerObserver),this._pointerObserver=null)),this._scene&&this._sceneDisposeObserver&&(this._scene.onDisposeObservable.remove(this._sceneDisposeObserver),this._sceneDisposeObserver=null),this._utilityLayer&&this._utilityLayer.dispose()},t._touchIdCounter=300,t}()},function(t,e,i){"use strict";i.r(e),function(t){var r=i(1);i.d(e,"Button",(function(){return r.Button})),i.d(e,"Checkbox",(function(){return r.Checkbox})),i.d(e,"ColorPicker",(function(){return r.ColorPicker})),i.d(e,"Container",(function(){return r.Container})),i.d(e,"Control",(function(){return r.Control})),i.d(e,"Ellipse",(function(){return r.Ellipse})),i.d(e,"FocusableButton",(function(){return r.FocusableButton})),i.d(e,"Grid",(function(){return r.Grid})),i.d(e,"Image",(function(){return r.Image})),i.d(e,"InputText",(function(){return r.InputText})),i.d(e,"InputPassword",(function(){return r.InputPassword})),i.d(e,"Line",(function(){return r.Line})),i.d(e,"MultiLine",(function(){return r.MultiLine})),i.d(e,"RadioButton",(function(){return r.RadioButton})),i.d(e,"StackPanel",(function(){return r.StackPanel})),i.d(e,"SelectorGroup",(function(){return r.SelectorGroup})),i.d(e,"CheckboxGroup",(function(){return r.CheckboxGroup})),i.d(e,"RadioGroup",(function(){return r.RadioGroup})),i.d(e,"SliderGroup",(function(){return r.SliderGroup})),i.d(e,"SelectionPanel",(function(){return r.SelectionPanel})),i.d(e,"ScrollViewer",(function(){return r.ScrollViewer})),i.d(e,"TextWrapping",(function(){return r.TextWrapping})),i.d(e,"TextBlock",(function(){return r.TextBlock})),i.d(e,"TextWrapper",(function(){return r.TextWrapper})),i.d(e,"ToggleButton",(function(){return r.ToggleButton})),i.d(e,"KeyPropertySet",(function(){return r.KeyPropertySet})),i.d(e,"VirtualKeyboard",(function(){return r.VirtualKeyboard})),i.d(e,"Rectangle",(function(){return r.Rectangle})),i.d(e,"DisplayGrid",(function(){return r.DisplayGrid})),i.d(e,"BaseSlider",(function(){return r.BaseSlider})),i.d(e,"Slider",(function(){return r.Slider})),i.d(e,"ImageBasedSlider",(function(){return r.ImageBasedSlider})),i.d(e,"ScrollBar",(function(){return r.ScrollBar})),i.d(e,"ImageScrollBar",(function(){return r.ImageScrollBar})),i.d(e,"name",(function(){return r.name})),i.d(e,"AdvancedDynamicTexture",(function(){return r.AdvancedDynamicTexture})),i.d(e,"AdvancedDynamicTextureInstrumentation",(function(){return r.AdvancedDynamicTextureInstrumentation})),i.d(e,"Vector2WithInfo",(function(){return r.Vector2WithInfo})),i.d(e,"Matrix2D",(function(){return r.Matrix2D})),i.d(e,"Measure",(function(){return r.Measure})),i.d(e,"MultiLinePoint",(function(){return r.MultiLinePoint})),i.d(e,"Style",(function(){return r.Style})),i.d(e,"ValueAndUnit",(function(){return r.ValueAndUnit})),i.d(e,"XmlLoader",(function(){return r.XmlLoader})),i.d(e,"AbstractButton3D",(function(){return r.AbstractButton3D})),i.d(e,"Button3D",(function(){return r.Button3D})),i.d(e,"Container3D",(function(){return r.Container3D})),i.d(e,"Control3D",(function(){return r.Control3D})),i.d(e,"CylinderPanel",(function(){return r.CylinderPanel})),i.d(e,"HolographicButton",(function(){return r.HolographicButton})),i.d(e,"MeshButton3D",(function(){return r.MeshButton3D})),i.d(e,"PlanePanel",(function(){return r.PlanePanel})),i.d(e,"ScatterPanel",(function(){return r.ScatterPanel})),i.d(e,"SpherePanel",(function(){return r.SpherePanel})),i.d(e,"StackPanel3D",(function(){return r.StackPanel3D})),i.d(e,"ButtonState",(function(){return r.ButtonState})),i.d(e,"TouchButton3D",(function(){return r.TouchButton3D})),i.d(e,"TouchMeshButton3D",(function(){return r.TouchMeshButton3D})),i.d(e,"TouchHolographicButton",(function(){return r.TouchHolographicButton})),i.d(e,"TouchToggleButton3D",(function(){return r.TouchToggleButton3D})),i.d(e,"VolumeBasedPanel",(function(){return r.VolumeBasedPanel})),i.d(e,"FluentMaterialDefines",(function(){return r.FluentMaterialDefines})),i.d(e,"FluentMaterial",(function(){return r.FluentMaterial})),i.d(e,"FluentButtonMaterial",(function(){return r.FluentButtonMaterial})),i.d(e,"GUI3DManager",(function(){return r.GUI3DManager})),i.d(e,"Vector3WithInfo",(function(){return r.Vector3WithInfo}));var o=void 0!==t?t:"undefined"!=typeof window?window:void 0;void 0!==o&&(o.BABYLON=o.BABYLON||{},o.BABYLON.GUI=r)}.call(this,i(3))},function(t,e){var i;i=function(){return this}();try{i=i||new Function("return this")()}catch(t){"object"==typeof window&&(i=window)}t.exports=i}])}));