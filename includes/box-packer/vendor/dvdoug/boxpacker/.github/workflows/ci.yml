name: CI

on:
  push: ~
  pull_request: ~
  schedule:
    - cron: '44 13 * * *'

jobs:
  tests:
    name: Tests
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        php-version:
          - "8.1"
          - "8.0"
          - "7.4"
          - "7.3"
          - "7.2"
          - "7.1"

        dependencies:
          - "highest"
          - "lowest"
          - "latest"

    steps:
    - uses: actions/checkout@v2
      with:
        fetch-depth: 2

    - name: "Setup PHP"
      uses: "shivammathur/setup-php@v2"
      with:
        php-version: ${{ matrix.php-version }}
        coverage: "xdebug"
        tools: composer:v2, cs2pr
        ini-values: "error_reporting=E_ALL, display_errors=On"
      env:
        COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Verify PHP version used
      run: |
        php -v && php -m;

    - name: Setup problem matchers for PHP
      run: echo "::add-matcher::${{ runner.tool_cache }}/php.json"

    - name: Setup problem matchers for PHPUnit
      run: echo "::add-matcher::${{ runner.tool_cache }}/phpunit.json"

    - name: Validate composer.json
      run: composer validate --strict

    - name: Get composer cache directory
      id: composercache
      run: echo "::set-output name=dir::$(composer config cache-files-dir)"

    - name: Set up build cache
      uses: "actions/cache@v2"
      with:
        path: |
          ${{ steps.composercache.outputs.dir }}
          "build/php-code-coverage-cache"
          ".php-cs-fixer.cache"
        key: php-${{ matrix.php-version }}-deps-${{ matrix.dependencies }}-${{ github.ref }}-${{ github.sha }}
        restore-keys: |
          php-${{ matrix.php-version }}-deps-${{ matrix.dependencies }}-${{ github.ref }}
          php-${{ matrix.php-version }}-deps-${{ matrix.dependencies }}

    - name: Remove PHP-CS-Fixer if not called
      if: matrix.php-version != '8.0' || matrix.dependencies != 'highest'
      run: composer remove --dev --no-update "friendsofphp/php-cs-fixer";

    - name: Fix min version for PHP8
      if: matrix.php-version == '8.0' || matrix.php-version == '8.1'
      run: |
        composer require --dev --no-update "dvdoug/behat-code-coverage ^5.2";
        composer require --dev --no-update "behat/behat ^3.8";

    - name: Install dependencies (low)
      if: matrix.dependencies == 'lowest'
      run: |
        composer update --prefer-lowest --no-interaction --prefer-dist;
        composer show;

    - name: Install dependencies (high)
      if: matrix.dependencies == 'highest'
      run: |
        composer update --no-interaction --prefer-dist;
        composer show;

    - name: Install dependencies (latest)
      if: matrix.dependencies == 'latest'
      run: |
        composer config minimum-stability dev;
        composer update --no-interaction --prefer-dist;
        composer show;

    - name: Install dependencies (force latest)
      if: matrix.dependencies == 'force_latest'
      run: |
        composer config minimum-stability dev;
        composer update --no-interaction --prefer-dist --ignore-platform-reqs;
        composer show;

    - name: PHPUnit
      run: |
        if [ "${{ matrix.php-version }}" = "8.0" ] && [ "${{ matrix.dependencies }}" = "highest" ]; then
          php -dmemory_limit=-1 vendor/phpunit/phpunit/phpunit --exclude-group=efficiency;
          composer global require scrutinizer/ocular;
          ~/.composer/vendor/bin/ocular code-coverage:upload --format=php-clover build/coverage-phpunit/clover.xml;
        else
          php -dmemory_limit=-1 vendor/phpunit/phpunit/phpunit --exclude-group=efficiency --no-coverage;
        fi;

    - name: Behat
      run: |
        if [ "${{ matrix.php-version }}" = "8.0" ] && [ "${{ matrix.dependencies }}" = "highest" ]; then
          php -dmemory_limit=-1 vendor/behat/behat/bin/behat --strict;
          ~/.composer/vendor/bin/ocular code-coverage:upload --format=php-clover build/coverage-behat/clover.xml;
        else
          php -dmemory_limit=-1 vendor/behat/behat/bin/behat --strict --no-coverage;
        fi;

    - name: Check code style
      if: matrix.php-version == '8.0' && matrix.dependencies == 'highest'
      run: vendor/bin/php-cs-fixer fix --verbose --dry-run --diff --allow-risky=yes --format=checkstyle | cs2pr


  speedrun:
    name: Speed run
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        php-version:
          - "8.1"
          - "8.0"
          - "7.4"
          - "7.3"
          - "7.2"
          - "7.1"

        dependencies:
          - "highest"
          - "lowest"
          - "latest"

    steps:
    - uses: actions/checkout@v2

    - name: "Setup PHP"
      uses: "shivammathur/setup-php@v2"
      with:
        php-version: ${{ matrix.php-version }}
        coverage: "none"
        tools: composer:v2
        ini-values: "opcache.enable=1, opcache.enable_cli=1, opcache.jit=1205, opcache.jit_buffer_size=128M, error_reporting=E_ALL, display_errors=On"
      env:
        COMPOSER_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Verify PHP version used
      run: |
        php -v && php -m;

    - name: Setup problem matchers for PHP
      run: echo "::add-matcher::${{ runner.tool_cache }}/php.json"

    - name: Setup problem matchers for PHPUnit
      run: echo "::add-matcher::${{ runner.tool_cache }}/phpunit.json"

    - name: Validate composer.json
      run: composer validate

    - name: Get composer cache directory
      id: composercache
      run: echo "::set-output name=dir::$(composer config cache-files-dir)"

    - name: Set up build cache
      uses: "actions/cache@v2"
      with:
        path: |
          ${{ steps.composercache.outputs.dir }}
          "build/php-code-coverage-cache"
          ".php-cs-fixer.cache"
        key: php-${{ matrix.php-version }}-deps-${{ matrix.dependencies }}-${{ github.ref }}-${{ github.sha }}
        restore-keys: |
          php-${{ matrix.php-version }}-deps-${{ matrix.dependencies }}-${{ github.ref }}
          php-${{ matrix.php-version }}-deps-${{ matrix.dependencies }}

    - name: Remove PHP-CS-Fixer
      run: composer remove --dev --no-update "friendsofphp/php-cs-fixer";

    - name: Install dependencies (low)
      if: matrix.dependencies == 'lowest'
      run: |
        composer update --prefer-lowest --no-interaction --prefer-dist;
        composer show;

    - name: Install dependencies (high)
      if: matrix.dependencies == 'highest'
      run: |
        composer update --no-interaction --prefer-dist;
        composer show;

    - name: Install dependencies (latest)
      if: matrix.dependencies == 'latest'
      run: |
        composer config minimum-stability dev;
        composer update --no-interaction --prefer-dist;
        composer show;

    - name: Install dependencies (force latest)
      if: matrix.dependencies == 'force_latest'
      run: |
        composer config minimum-stability dev;
        composer update --no-interaction --prefer-dist --ignore-platform-reqs;
        composer show;

    - name: PHPUnit (no coverage)
      run: vendor/bin/phpunit --no-coverage;

    - name: Behat (no coverage)
      run: vendor/bin/behat --strict --no-coverage;
