BoxPacker
=========

An implementation of the "4D" bin packing/knapsack problem i.e. given a list of items, how many boxes do you need to fit
them all in taking into account physical dimensions and weights.

Especially useful for e.g. e-commerce contexts when you need to know box size/weight to calculate shipping costs, or
even just want to know the right number of labels to print.

See [documentation](https://boxpacker.io/) for more details.

[![Build Status](https://github.com/dvdoug/BoxPacker/workflows/CI/badge.svg?branch=3.x)](https://github.com/dvdoug/BoxPacker/actions?query=workflow%3ACI+branch%3A3.x)
[![Scrutinizer Code Quality](https://scrutinizer-ci.com/g/dvdoug/BoxPacker/badges/quality-score.png?b=3.x)](https://scrutinizer-ci.com/g/dvdoug/BoxPacker/?branch=3.x)
[![Download count](https://img.shields.io/packagist/dt/dvdoug/boxpacker.svg)](https://packagist.org/packages/dvdoug/boxpacker)
[![Current version](https://img.shields.io/packagist/v/dvdoug/boxpacker.svg)](https://packagist.org/packages/dvdoug/boxpacker)
[![Documentation](https://readthedocs.org/projects/boxpacker/badge/?version=3.x)](https://www.boxpacker.io/en/3.x/)

License
-------
BoxPacker is MIT-licensed. 
