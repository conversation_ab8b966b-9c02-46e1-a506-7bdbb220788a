{"name": "automattic/jetpack-autoloader", "description": "Creates a custom autoloader for a plugin or theme.", "type": "composer-plugin", "license": "GPL-2.0-or-later", "require": {"composer-plugin-api": "^1.1 || ^2.0"}, "require-dev": {"yoast/phpunit-polyfills": "1.0.3", "automattic/jetpack-changelogger": "^3.1"}, "autoload": {"classmap": ["src/AutoloadGenerator.php"], "psr-4": {"Automattic\\Jetpack\\Autoloader\\": "src"}}, "scripts": {"phpunit": ["./vendor/phpunit/phpunit/phpunit --colors=always"], "test-coverage": ["php -dpcov.directory=. ./vendor/bin/phpunit --coverage-php \"./tests/php/tmp/coverage-report.php\"", "php ./tests/php/bin/test-coverage.php \"$COVERAGE_DIR/clover.xml\""], "test-php": ["@composer phpunit"]}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"autotagger": true, "class": "Automattic\\Jetpack\\Autoloader\\CustomAutoloaderPlugin", "mirror-repo": "Automattic/jetpack-autoloader", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-autoloader/compare/v${old}...v${new}"}, "branch-alias": {"dev-master": "2.11.x-dev"}}}