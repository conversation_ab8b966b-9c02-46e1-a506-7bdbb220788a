{"packages": [{"name": "automattic/jetpack-autoloader", "version": "v2.11.4", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-autoloader.git", "reference": "bf742b8f0b885d661deab74ec7775ff5856a7008"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-autoloader/zipball/bf742b8f0b885d661deab74ec7775ff5856a7008", "reference": "bf742b8f0b885d661deab74ec7775ff5856a7008", "shasum": ""}, "require": {"composer-plugin-api": "^1.1 || ^2.0"}, "require-dev": {"automattic/jetpack-changelogger": "^3.1", "yoast/phpunit-polyfills": "1.0.3"}, "time": "2022-04-26T14:33:38+00:00", "type": "composer-plugin", "extra": {"autotagger": true, "class": "Automattic\\Jetpack\\Autoloader\\CustomAutoloaderPlugin", "mirror-repo": "Automattic/jetpack-autoloader", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-autoloader/compare/v${old}...v${new}"}, "branch-alias": {"dev-master": "2.11.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Automattic\\Jetpack\\Autoloader\\": "src"}, "classmap": ["src/AutoloadGenerator.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Creates a custom autoloader for a plugin or theme.", "support": {"source": "https://github.com/Automattic/jetpack-autoloader/tree/v2.11.4"}, "install-path": "../automattic/jetpack-autoloader"}, {"name": "dvdoug/boxpacker", "version": "3.9.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/dvdoug/BoxPacker.git", "reference": "07176158e0ebef29b701852181663ed8b429149f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dvdoug/BoxPacker/zipball/07176158e0ebef29b701852181663ed8b429149f", "reference": "07176158e0ebef29b701852181663ed8b429149f", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.1||^8.0", "psr/log": "^1.0||^2.0||^3.0"}, "require-dev": {"behat/behat": "^3.7", "dvdoug/behat-code-coverage": "^5.0.1", "friendsofphp/php-cs-fixer": "^3.0", "monolog/monolog": "^1.0||^2.0", "phpunit/phpunit": "^7.5.20||^8.5.21||^9.5.8"}, "time": "2021-10-21T21:47:05+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"DVDoug\\BoxPacker\\": "src/", "DVDoug\\BoxPacker\\Test\\": "tests/Test"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "role": "Developer"}], "description": "An implementation of the 3D (actually 4D) bin packing/knapsack problem (aka creating parcels by putting items into boxes)", "homepage": "http://boxpacker.io/", "keywords": ["bin packing", "binpacking", "box", "boxes", "boxpacking", "container", "knapsack", "packaging", "packing", "parcel", "parcelpacking", "shipping"], "support": {"issues": "https://github.com/dvdoug/BoxPacker/issues", "source": "https://github.com/dvdoug/BoxPacker/tree/3.9.4"}, "funding": [{"url": "https://github.com/dvdoug", "type": "github"}], "install-path": "../dvdoug/boxpacker"}, {"name": "psr/log", "version": "1.1.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2021-05-03T11:20:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "install-path": "../psr/log"}], "dev": false, "dev-package-names": []}