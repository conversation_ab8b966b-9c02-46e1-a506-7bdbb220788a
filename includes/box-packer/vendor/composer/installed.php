<?php return array(
    'root' => array(
        'name' => 'woocommerce/box-packer',
        'pretty_version' => 'dev-trunk',
        'version' => 'dev-trunk',
        'reference' => 'ac1a7e21a9ce2c40a9882951a8d36117a35995fa',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'automattic/jetpack-autoloader' => array(
            'pretty_version' => 'v2.11.4',
            'version' => '2.11.4.0',
            'reference' => 'bf742b8f0b885d661deab74ec7775ff5856a7008',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../automattic/jetpack-autoloader',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dvdoug/boxpacker' => array(
            'pretty_version' => '3.9.4',
            'version' => '3.9.4.0',
            'reference' => '07176158e0ebef29b701852181663ed8b429149f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dvdoug/boxpacker',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'woocommerce/box-packer' => array(
            'pretty_version' => 'dev-trunk',
            'version' => 'dev-trunk',
            'reference' => 'ac1a7e21a9ce2c40a9882951a8d36117a35995fa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
