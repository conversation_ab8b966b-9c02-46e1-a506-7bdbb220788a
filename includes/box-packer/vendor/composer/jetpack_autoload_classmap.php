<?php

// This file `jetpack_autoload_classmap.php` was auto generated by automattic/jetpack-autoloader.

$vendorDir = dirname(__DIR__);
$baseDir   = dirname($vendorDir);

return array(
	'WooCommerce\\BoxPacker\\Package' => array(
		'version' => 'dev-trunk',
		'path'    => $baseDir . '/src/class-package.php'
	),
	'WooCommerce\\BoxPacker\\Abstract_Item' => array(
		'version' => 'dev-trunk',
		'path'    => $baseDir . '/src/class-abstract-item.php'
	),
	'WooCommerce\\BoxPacker\\Original\\Packer' => array(
		'version' => 'dev-trunk',
		'path'    => $baseDir . '/src/original/class-packer.php'
	),
	'WooCommerce\\BoxPacker\\Original\\Item' => array(
		'version' => 'dev-trunk',
		'path'    => $baseDir . '/src/original/class-item.php'
	),
	'WooCommerce\\BoxPacker\\Original\\Box' => array(
		'version' => 'dev-trunk',
		'path'    => $baseDir . '/src/original/class-box.php'
	),
	'WooCommerce\\BoxPacker\\Box' => array(
		'version' => 'dev-trunk',
		'path'    => $baseDir . '/src/interface-box.php'
	),
	'WooCommerce\\BoxPacker\\DVDoug\\Util' => array(
		'version' => 'dev-trunk',
		'path'    => $baseDir . '/src/dvdoug/trait-util.php'
	),
	'WooCommerce\\BoxPacker\\DVDoug\\Packer' => array(
		'version' => 'dev-trunk',
		'path'    => $baseDir . '/src/dvdoug/class-packer.php'
	),
	'WooCommerce\\BoxPacker\\DVDoug\\Item' => array(
		'version' => 'dev-trunk',
		'path'    => $baseDir . '/src/dvdoug/class-item.php'
	),
	'WooCommerce\\BoxPacker\\DVDoug\\Box' => array(
		'version' => 'dev-trunk',
		'path'    => $baseDir . '/src/dvdoug/class-box.php'
	),
	'WooCommerce\\BoxPacker\\Abstract_Packer' => array(
		'version' => 'dev-trunk',
		'path'    => $baseDir . '/src/class-abstract-packer.php'
	),
	'Psr\\Log\\LoggerAwareInterface' => array(
		'version' => '1.1.4.0',
		'path'    => $vendorDir . '/psr/log/Psr/Log/LoggerAwareInterface.php'
	),
	'Psr\\Log\\LogLevel' => array(
		'version' => '1.1.4.0',
		'path'    => $vendorDir . '/psr/log/Psr/Log/LogLevel.php'
	),
	'Psr\\Log\\Test\\LoggerInterfaceTest' => array(
		'version' => '1.1.4.0',
		'path'    => $vendorDir . '/psr/log/Psr/Log/Test/LoggerInterfaceTest.php'
	),
	'Psr\\Log\\Test\\TestLogger' => array(
		'version' => '1.1.4.0',
		'path'    => $vendorDir . '/psr/log/Psr/Log/Test/TestLogger.php'
	),
	'Psr\\Log\\Test\\DummyTest' => array(
		'version' => '1.1.4.0',
		'path'    => $vendorDir . '/psr/log/Psr/Log/Test/DummyTest.php'
	),
	'Psr\\Log\\LoggerAwareTrait' => array(
		'version' => '1.1.4.0',
		'path'    => $vendorDir . '/psr/log/Psr/Log/LoggerAwareTrait.php'
	),
	'Psr\\Log\\InvalidArgumentException' => array(
		'version' => '1.1.4.0',
		'path'    => $vendorDir . '/psr/log/Psr/Log/InvalidArgumentException.php'
	),
	'Psr\\Log\\NullLogger' => array(
		'version' => '1.1.4.0',
		'path'    => $vendorDir . '/psr/log/Psr/Log/NullLogger.php'
	),
	'Psr\\Log\\LoggerInterface' => array(
		'version' => '1.1.4.0',
		'path'    => $vendorDir . '/psr/log/Psr/Log/LoggerInterface.php'
	),
	'Psr\\Log\\LoggerTrait' => array(
		'version' => '1.1.4.0',
		'path'    => $vendorDir . '/psr/log/Psr/Log/LoggerTrait.php'
	),
	'Psr\\Log\\AbstractLogger' => array(
		'version' => '1.1.4.0',
		'path'    => $vendorDir . '/psr/log/Psr/Log/AbstractLogger.php'
	),
	'DVDoug\\BoxPacker\\Test\\ConstrainedPlacementNoStackingTestItem' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/tests/Test/ConstrainedPlacementNoStackingTestItem.php'
	),
	'DVDoug\\BoxPacker\\Test\\TestBox' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/tests/Test/TestBox.php'
	),
	'DVDoug\\BoxPacker\\Test\\THPackTestItem' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/tests/Test/THPackTestItem.php'
	),
	'DVDoug\\BoxPacker\\Test\\LimitedSupplyTestBox' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/tests/Test/LimitedSupplyTestBox.php'
	),
	'DVDoug\\BoxPacker\\Test\\TestItem' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/tests/Test/TestItem.php'
	),
	'DVDoug\\BoxPacker\\Test\\ConstrainedTestItem' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/tests/Test/ConstrainedTestItem.php'
	),
	'DVDoug\\BoxPacker\\Test\\ConstrainedPlacementByCountTestItem' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/tests/Test/ConstrainedPlacementByCountTestItem.php'
	),
	'DVDoug\\BoxPacker\\BoxList' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/BoxList.php'
	),
	'DVDoug\\BoxPacker\\PackedItemList' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/PackedItemList.php'
	),
	'DVDoug\\BoxPacker\\LimitedSupplyBox' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/LimitedSupplyBox.php'
	),
	'DVDoug\\BoxPacker\\LayerStabiliser' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/LayerStabiliser.php'
	),
	'DVDoug\\BoxPacker\\PackedItem' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/PackedItem.php'
	),
	'DVDoug\\BoxPacker\\PackedBoxList' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/PackedBoxList.php'
	),
	'DVDoug\\BoxPacker\\ItemTooLargeException' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/ItemTooLargeException.php'
	),
	'DVDoug\\BoxPacker\\PackedLayer' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/PackedLayer.php'
	),
	'DVDoug\\BoxPacker\\Packer' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/Packer.php'
	),
	'DVDoug\\BoxPacker\\ItemList' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/ItemList.php'
	),
	'DVDoug\\BoxPacker\\ConstrainedItem' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/ConstrainedItem.php'
	),
	'DVDoug\\BoxPacker\\OrientatedItem' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/OrientatedItem.php'
	),
	'DVDoug\\BoxPacker\\NoBoxesAvailableException' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/NoBoxesAvailableException.php'
	),
	'DVDoug\\BoxPacker\\Item' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/Item.php'
	),
	'DVDoug\\BoxPacker\\LayerPacker' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/LayerPacker.php'
	),
	'DVDoug\\BoxPacker\\ConstrainedPlacementItem' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/ConstrainedPlacementItem.php'
	),
	'DVDoug\\BoxPacker\\VolumePacker' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/VolumePacker.php'
	),
	'DVDoug\\BoxPacker\\PackedBox' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/PackedBox.php'
	),
	'DVDoug\\BoxPacker\\InfalliblePacker' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/InfalliblePacker.php'
	),
	'DVDoug\\BoxPacker\\OrientatedItemFactory' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/OrientatedItemFactory.php'
	),
	'DVDoug\\BoxPacker\\WorkingVolume' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/WorkingVolume.php'
	),
	'DVDoug\\BoxPacker\\WeightRedistributor' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/WeightRedistributor.php'
	),
	'DVDoug\\BoxPacker\\Box' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/Box.php'
	),
	'DVDoug\\BoxPacker\\OrientatedItemSorter' => array(
		'version' => '3.9.4.0',
		'path'    => $vendorDir . '/dvdoug/boxpacker/src/OrientatedItemSorter.php'
	),
	'Automattic\\Jetpack\\Autoloader\\AutoloadGenerator' => array(
		'version' => '2.11.4.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadGenerator.php'
	),
	'Container' => array(
		'version' => '2.11.4.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-container.php'
	),
	'Autoloader_Locator' => array(
		'version' => '2.11.4.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-autoloader-locator.php'
	),
	'Latest_Autoloader_Guard' => array(
		'version' => '2.11.4.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-latest-autoloader-guard.php'
	),
	'PHP_Autoloader' => array(
		'version' => '2.11.4.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-php-autoloader.php'
	),
	'Automattic\\Jetpack\\Autoloader\\AutoloadProcessor' => array(
		'version' => '2.11.4.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadProcessor.php'
	),
	'Autoloader_Handler' => array(
		'version' => '2.11.4.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-autoloader-handler.php'
	),
	'Automattic\\Jetpack\\Autoloader\\AutoloadFileWriter' => array(
		'version' => '2.11.4.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadFileWriter.php'
	),
	'Automattic\\Jetpack\\Autoloader\\CustomAutoloaderPlugin' => array(
		'version' => '2.11.4.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/CustomAutoloaderPlugin.php'
	),
	'Version_Selector' => array(
		'version' => '2.11.4.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-version-selector.php'
	),
	'Path_Processor' => array(
		'version' => '2.11.4.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-path-processor.php'
	),
	'Hook_Manager' => array(
		'version' => '2.11.4.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-hook-manager.php'
	),
	'Manifest_Reader' => array(
		'version' => '2.11.4.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-manifest-reader.php'
	),
	'Plugin_Locator' => array(
		'version' => '2.11.4.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-plugin-locator.php'
	),
	'Plugins_Handler' => array(
		'version' => '2.11.4.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-plugins-handler.php'
	),
	'Autoloader' => array(
		'version' => '2.11.4.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-autoloader.php'
	),
	'Automattic\\Jetpack\\Autoloader\\ManifestGenerator' => array(
		'version' => '2.11.4.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/ManifestGenerator.php'
	),
	'Shutdown_Handler' => array(
		'version' => '2.11.4.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-shutdown-handler.php'
	),
	'Version_Loader' => array(
		'version' => '2.11.4.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-version-loader.php'
	),
);
