<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Automattic\\Jetpack\\Autoloader\\AutoloadFileWriter' => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadFileWriter.php',
    'Automattic\\Jetpack\\Autoloader\\AutoloadGenerator' => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadGenerator.php',
    'Automattic\\Jetpack\\Autoloader\\AutoloadProcessor' => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadProcessor.php',
    'Automattic\\Jetpack\\Autoloader\\CustomAutoloaderPlugin' => $vendorDir . '/automattic/jetpack-autoloader/src/CustomAutoloaderPlugin.php',
    'Automattic\\Jetpack\\Autoloader\\ManifestGenerator' => $vendorDir . '/automattic/jetpack-autoloader/src/ManifestGenerator.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'DVDoug\\BoxPacker\\Box' => $vendorDir . '/dvdoug/boxpacker/src/Box.php',
    'DVDoug\\BoxPacker\\BoxList' => $vendorDir . '/dvdoug/boxpacker/src/BoxList.php',
    'DVDoug\\BoxPacker\\ConstrainedItem' => $vendorDir . '/dvdoug/boxpacker/src/ConstrainedItem.php',
    'DVDoug\\BoxPacker\\ConstrainedPlacementItem' => $vendorDir . '/dvdoug/boxpacker/src/ConstrainedPlacementItem.php',
    'DVDoug\\BoxPacker\\InfalliblePacker' => $vendorDir . '/dvdoug/boxpacker/src/InfalliblePacker.php',
    'DVDoug\\BoxPacker\\Item' => $vendorDir . '/dvdoug/boxpacker/src/Item.php',
    'DVDoug\\BoxPacker\\ItemList' => $vendorDir . '/dvdoug/boxpacker/src/ItemList.php',
    'DVDoug\\BoxPacker\\ItemTooLargeException' => $vendorDir . '/dvdoug/boxpacker/src/ItemTooLargeException.php',
    'DVDoug\\BoxPacker\\LayerPacker' => $vendorDir . '/dvdoug/boxpacker/src/LayerPacker.php',
    'DVDoug\\BoxPacker\\LayerStabiliser' => $vendorDir . '/dvdoug/boxpacker/src/LayerStabiliser.php',
    'DVDoug\\BoxPacker\\LimitedSupplyBox' => $vendorDir . '/dvdoug/boxpacker/src/LimitedSupplyBox.php',
    'DVDoug\\BoxPacker\\NoBoxesAvailableException' => $vendorDir . '/dvdoug/boxpacker/src/NoBoxesAvailableException.php',
    'DVDoug\\BoxPacker\\OrientatedItem' => $vendorDir . '/dvdoug/boxpacker/src/OrientatedItem.php',
    'DVDoug\\BoxPacker\\OrientatedItemFactory' => $vendorDir . '/dvdoug/boxpacker/src/OrientatedItemFactory.php',
    'DVDoug\\BoxPacker\\OrientatedItemSorter' => $vendorDir . '/dvdoug/boxpacker/src/OrientatedItemSorter.php',
    'DVDoug\\BoxPacker\\PackedBox' => $vendorDir . '/dvdoug/boxpacker/src/PackedBox.php',
    'DVDoug\\BoxPacker\\PackedBoxList' => $vendorDir . '/dvdoug/boxpacker/src/PackedBoxList.php',
    'DVDoug\\BoxPacker\\PackedItem' => $vendorDir . '/dvdoug/boxpacker/src/PackedItem.php',
    'DVDoug\\BoxPacker\\PackedItemList' => $vendorDir . '/dvdoug/boxpacker/src/PackedItemList.php',
    'DVDoug\\BoxPacker\\PackedLayer' => $vendorDir . '/dvdoug/boxpacker/src/PackedLayer.php',
    'DVDoug\\BoxPacker\\Packer' => $vendorDir . '/dvdoug/boxpacker/src/Packer.php',
    'DVDoug\\BoxPacker\\Test\\ConstrainedPlacementByCountTestItem' => $vendorDir . '/dvdoug/boxpacker/tests/Test/ConstrainedPlacementByCountTestItem.php',
    'DVDoug\\BoxPacker\\Test\\ConstrainedPlacementNoStackingTestItem' => $vendorDir . '/dvdoug/boxpacker/tests/Test/ConstrainedPlacementNoStackingTestItem.php',
    'DVDoug\\BoxPacker\\Test\\ConstrainedTestItem' => $vendorDir . '/dvdoug/boxpacker/tests/Test/ConstrainedTestItem.php',
    'DVDoug\\BoxPacker\\Test\\LimitedSupplyTestBox' => $vendorDir . '/dvdoug/boxpacker/tests/Test/LimitedSupplyTestBox.php',
    'DVDoug\\BoxPacker\\Test\\THPackTestItem' => $vendorDir . '/dvdoug/boxpacker/tests/Test/THPackTestItem.php',
    'DVDoug\\BoxPacker\\Test\\TestBox' => $vendorDir . '/dvdoug/boxpacker/tests/Test/TestBox.php',
    'DVDoug\\BoxPacker\\Test\\TestItem' => $vendorDir . '/dvdoug/boxpacker/tests/Test/TestItem.php',
    'DVDoug\\BoxPacker\\VolumePacker' => $vendorDir . '/dvdoug/boxpacker/src/VolumePacker.php',
    'DVDoug\\BoxPacker\\WeightRedistributor' => $vendorDir . '/dvdoug/boxpacker/src/WeightRedistributor.php',
    'DVDoug\\BoxPacker\\WorkingVolume' => $vendorDir . '/dvdoug/boxpacker/src/WorkingVolume.php',
    'Psr\\Log\\AbstractLogger' => $vendorDir . '/psr/log/Psr/Log/AbstractLogger.php',
    'Psr\\Log\\InvalidArgumentException' => $vendorDir . '/psr/log/Psr/Log/InvalidArgumentException.php',
    'Psr\\Log\\LogLevel' => $vendorDir . '/psr/log/Psr/Log/LogLevel.php',
    'Psr\\Log\\LoggerAwareInterface' => $vendorDir . '/psr/log/Psr/Log/LoggerAwareInterface.php',
    'Psr\\Log\\LoggerAwareTrait' => $vendorDir . '/psr/log/Psr/Log/LoggerAwareTrait.php',
    'Psr\\Log\\LoggerInterface' => $vendorDir . '/psr/log/Psr/Log/LoggerInterface.php',
    'Psr\\Log\\LoggerTrait' => $vendorDir . '/psr/log/Psr/Log/LoggerTrait.php',
    'Psr\\Log\\NullLogger' => $vendorDir . '/psr/log/Psr/Log/NullLogger.php',
    'Psr\\Log\\Test\\DummyTest' => $vendorDir . '/psr/log/Psr/Log/Test/DummyTest.php',
    'Psr\\Log\\Test\\LoggerInterfaceTest' => $vendorDir . '/psr/log/Psr/Log/Test/LoggerInterfaceTest.php',
    'Psr\\Log\\Test\\TestLogger' => $vendorDir . '/psr/log/Psr/Log/Test/TestLogger.php',
);
